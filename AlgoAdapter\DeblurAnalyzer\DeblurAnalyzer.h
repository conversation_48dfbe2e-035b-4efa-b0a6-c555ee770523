#ifndef HX_DENOISE_ANALYZER_H__
#define HX_DENOISE_ANALYZER_H__


#ifdef ALGO_ADAPTER
#define DENOISE_DLL_API __declspec(dllexport)
#else
#define DENOISE_DLL_API __declspec(dllimport)
#endif

#include <memory>
#include <vector>
#include <string>
#include <algorithm>
#include <mutex>


class DENOISE_DLL_API DeblurAnalyzer
{
public:
    ~DeblurAnalyzer();

	bool run(unsigned char*pRaw, int rows, int cols);
	static std::shared_ptr<DeblurAnalyzer> instance();

private:
	DeblurAnalyzer();

	static std::shared_ptr<DeblurAnalyzer> m_ins;
	static std::mutex m_mutex;

};

#endif
