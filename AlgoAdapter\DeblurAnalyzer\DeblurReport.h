#ifndef HX_DENOISE_REPORT_H__
#define HX_DENOISE_REPORT_H__
#include "reports.h"
#include "opencv2/opencv.hpp"

class DeblurReport : public PathologyReport
{
public:
    DeblurReport();
    ~DeblurReport();
    virtual std::string type();

    virtual void type(const std::string& typeName);

    virtual void process(PathologyImageItemPtr imgItem, const std::vector<WTensor>& outputs = std::vector<WTensor>())override;
	void getDeblurImg(cv::Mat &img) { img = m_rstImg.clone(); };

private:
	cv::Mat m_rstImg;


};

#endif
