#include "STReport.h"
#include <iostream>
#include "log/Logger4cpp.h"
using namespace std;


STReport::STReport()
{
	raw_text_data_ = get_raw_text_data();
	/*
	 * Ki67:1
	 * ER:2
	 * PR:3
	 * HER-2:4
	 * HP:5
	 * AR_ER_PR:6
	 */
}


STReport::~STReport()
{
}

std::string STReport::type()
{
    return "ST";
}

void STReport::process(PathologyImageItemPtr imgItem, const std::vector<WTensor>& outputs)
{
	if (outputs.size() < 2) {
		LOG_ERROR("Error outputs num " << outputs.size());
		return;
	}

	label_type_ = outputs.at(0).template flat<int>().data()[0];
	auto text = outputs.at(1).template flat<int>();
	text_ = ids_to_string(text.data(), text.size());
	LOG_INFO("Text:" << text_.c_str()<<" Type:"<<label_type_);
}

void STReport::type(const std::string& typeName)
{

}
string STReport::ids_to_string(const int* ids,int size) 
{
	string res;
	for (auto i = 0; i < size; ++i) {
		const auto id = ids[i];
		if (id == 0)
			res += "; ";
		else if (id == 69)
			res.push_back(' ');
		else if (id >= raw_text_data_.size())
			res += "?";
		else
			res.push_back(raw_text_data_[id - 1]);
	}
	return res;
}
string STReport::get_raw_text_data() const
{
	string text_array;

	for (auto i = int('a'); i <= int('z'); ++i) {
		text_array.push_back(char(i));
	}
	for (auto i = int('A'); i <= int('Z'); ++i) {
		text_array.push_back(char(i));
	}
	for (auto i = int('0'); i <= int('9'); ++i) {
		text_array.push_back(char(i));
	}
	text_array.push_back('/');
	text_array.push_back('\\');
	text_array.push_back('-');
	text_array.push_back('+');
	text_array.push_back(':');

	return text_array;
}
