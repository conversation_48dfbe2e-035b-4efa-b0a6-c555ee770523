#pragma once
#include "opencv2/opencv.hpp"
#define BYTE unsigned char

// object mark label 
enum _OBJECT_LABEL
{
	_OBJ_LABEL_BLANK = 0,	// blank area, no need to scan 
	_OBJ_LABEL_FUSE = 1,	// scan for fusing 
	_OBJ_LABEL_3D = 2,	// 3D scan 
	_OBJ_LABEL_FUSE_3D = 3,	// both 3D scan and fusing 

	_OBJ_LABEL_USERDEF = 5,  // User define area
	_OBJ_LABEL_UNSURE = 6,  // Unsure object
	_OBJ_LABEL_EDGE = 7,  // Edge area
	_OBJ_LABEL_NORMAL = 8	// 8, 7, 6, 5; if label >= 8, normal scan 
};


class TissueInfo
{
public:
	TissueInfo();
	~TissueInfo();
	bool setInfo(float fStepX, float fStepY, int objLeft, int objTop, int objWidth, int objHeight);
	bool doMain(cv::Rect &stRange, cv::Mat &img);
	void getMap(cv::Mat &memMap);
	void getColor(cv::Mat &memColor);
	void getMapSize(int &mapWidth, int &mapHeight);
	int  getSlideType();
private:
	void createMapImage(cv::Rect &stRange, cv::Mat &img);
	void createColorImage(cv::Rect &stRange, cv::Mat &img);
	int getColorTh() { return m_colorTh; }

	int RGB2YUV(cv::Mat &imgR, cv::Mat &imgG, cv::Mat &imgB, cv::Mat &imgY, cv::Mat &imgU, cv::Mat &imgV);
private:
	float m_stepX;
	float m_stepY;
	cv::Rect m_rtObj;
	int m_mapWidth;
	int m_mapHeight;
	int m_colorTh;
	cv::Mat m_stColor;
	cv::Mat m_stMap;
	cv::Mat m_stColorImg;
	


	
};

