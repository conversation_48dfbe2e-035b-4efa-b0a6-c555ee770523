#include "TissueReport.h"
#include "log/Logger4cpp.h"
#include "TissueInfo.h"
using namespace std;

TissueReport::TissueReport()
		:m_nSlideType(4)
{
}


TissueReport::~TissueReport()
{
}


std::string TissueReport::type()
{
    return "Tissue";
}

void TissueReport::process(PathologyImageItemPtr imgItem, const std::vector<WTensor>& output)
{
	if (output.size() < 2) {
		LOG_ERROR("Tissue output size error.");
		return;
	}
	LOG_INFO( "enter proccess:" );
	auto tensor = output.at(0).template tensor<int, 2>();
	m_nSlideType = output.at(1).template flat<int>().data()[0];
	//std::vector<std::vector<cv::Point>> contours;
	_contours.clear();
	cv::Rect rt;
	
	cout << "total:" << tensor.dimension(0) << std::endl;
	for (auto i = 0; i < tensor.dimension(0); ++i) {
		const auto v = tensor(i, 2);
		//cout << "v:" << tensor(i, 0)  << "," <<  tensor(i, 1) << "," <<  tensor(i, 2) << endl;
		
		vector<cv::Point> tmp_contour;
		for (;(i < tensor.dimension(0)) && (v == tensor(i, 2)); ++i) {
			tmp_contour.emplace_back(tensor(i, 0), tensor(i, 1));

		}
		if (tmp_contour.size() > 2)
		{
			rt = rt | cv::boundingRect(tmp_contour);
			/*std::cout << rt << std::endl;
			cout << "tmp_contor.size:" << tmp_contour.size() << std::endl;
			for (int i = 0; i < tmp_contour.size(); i++)
			{
				cout << tmp_contour[i] << endl;
			}*/
			_contours.push_back(std::move(tmp_contour));
			
			
		}
		
	}

	if (_contours.empty()) {
		LOG_ERROR("No valid tissue region area.");
		return;
	}

	LOG_INFO("success.");

	cv::Mat img(imgItem->height, imgItem->width, CV_8UC3, imgItem->rawBuffer);

	

	cv::Mat mask(img.size(), CV_8UC3, cv::Scalar(0, 0, 0));

	cv::drawContours(mask, _contours, -1, cv::Scalar(255, 255, 255), -1);


	cv::Mat img_masked;
	img.copyTo(img_masked, mask);
	cv::rectangle(img, rt, cv::Scalar(255, 0, 0));

	getTissueInfo().doMain(rt, img_masked);
#ifdef LOCAL_TEST
	cv::imwrite("./log/countors.jpg", mask);
	std::stringstream ss;
	ss << "./log/macro_img_tissue.jpg";
	LOG_INFO("report path:" << report_path_.c_str());
	cv::imwrite("./log/macrorect.jpg", img);

	cv::imwrite(ss.str(), img_masked);
#endif
	

}

void TissueReport::type(const std::string& typeName)
{

}


TissueInfo& TissueReport::getTissueInfo()
{
	return m_tisueInfo;
}

int TissueReport::getSlideType()
{
	return m_nSlideType;
}
