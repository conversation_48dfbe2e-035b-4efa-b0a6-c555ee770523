#ifndef HX_TISSUE_REPORT_H__
#define HX_TISSUE_REPORT_H__


#ifdef ALGO_ADAPTER
#define TUSSIE_DLL_API __declspec(dllexport)
#else
#define TUSSIE_DLL_API __declspec(dllimport)
#endif



#include "reports.h"
#include "interface/IImageData.h"
#include "TissueInfo.h"

class TUSSIE_DLL_API TissueReport : public PathologyReport
{
public:
    TissueReport();
    ~TissueReport();

    virtual std::string type();

    virtual void type(const std::string& typeName);

    virtual void process(PathologyImageItemPtr imgItem,const std::vector<WTensor>& output)override;
	inline std::vector<std::vector<cv::Point>> get_contours() const { return _contours; }
	TissueInfo& getTissueInfo();
	int getSlideType();
private:
	std::vector<std::vector<cv::Point>> _contours;
	TissueInfo m_tisueInfo;
	int m_nSlideType;
};

#endif
