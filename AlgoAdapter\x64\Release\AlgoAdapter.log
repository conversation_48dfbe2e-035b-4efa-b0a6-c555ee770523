﻿C:\Program Files (x86)\MSBuild\Microsoft.Cpp\v4.0\V140\Microsoft.CppBuild.targets(368,5): warning MSB8004: Output Directory does not end with a trailing slash.  This build instance will add the slash as it is required to allow proper evaluation of the Output Directory.
  AlgoInit.cpp
D:\code\dphx\cpp\common\PathologyImgItem.h(12): warning C4251: “PathologyImageItem::fileName”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 struct“PathologyImageItem”的客户端使用
  C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\include\xstring(2633): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\code\dphx\cpp\common\../common/wtoolkit.h(18): warning C4244: “return”: 从“__int64”转换到“int”，可能丢失数据
d:\code\dphx\cpp\common\interface\IPathogyReport.h(10): fatal error C1083: 无法打开包括文件: “wdata.h”: No such file or directory
  DeblurAnalyzer.cpp
d:\code\iviewersdk\algoadapter\debluranalyzer\DeblurAnalyzer.h(29): warning C4251: “DeblurAnalyzer::m_ins”: class“std::shared_ptr<DeblurAnalyzer>”需要有 dll 接口由 class“DeblurAnalyzer”的客户端使用
  d:\code\iviewersdk\algoadapter\debluranalyzer\DeblurAnalyzer.h(24): note: 参见“std::shared_ptr<DeblurAnalyzer>”的声明
d:\code\iviewersdk\algoadapter\debluranalyzer\DeblurAnalyzer.h(30): warning C4251: “DeblurAnalyzer::m_mutex”: class“std::mutex”需要有 dll 接口由 class“DeblurAnalyzer”的客户端使用
  C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\include\mutex(78): note: 参见“std::mutex”的声明
d:\code\dphx\cpp\common\interface\../PathologyImgItem.h(12): warning C4251: “PathologyImageItem::fileName”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 struct“PathologyImageItem”的客户端使用
  C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\include\xstring(2633): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
d:\code\dphx\cpp\tfadapter\../common/wtoolkit.h(18): warning C4244: “return”: 从“__int64”转换到“int”，可能丢失数据
D:\code\dphx\cpp\common\interface/IPathogyReport.h(10): fatal error C1083: 无法打开包括文件: “wdata.h”: No such file or directory
  DeblurReport.cpp
d:\code\dphx\cpp\common\interface\../PathologyImgItem.h(12): warning C4251: “PathologyImageItem::fileName”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 struct“PathologyImageItem”的客户端使用
  C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\include\xstring(2633): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
d:\code\dphx\cpp\tfadapter\../common/wtoolkit.h(18): warning C4244: “return”: 从“__int64”转换到“int”，可能丢失数据
D:\code\dphx\cpp\common\interface/IPathogyReport.h(10): fatal error C1083: 无法打开包括文件: “wdata.h”: No such file or directory
  OcrAnalyzer.cpp
d:\code\iviewersdk\algoadapter\ocranalyzer\OcrAnalyzer.h(29): warning C4251: “OcrAnalyzer::m_ins”: class“std::shared_ptr<OcrAnalyzer>”需要有 dll 接口由 class“OcrAnalyzer”的客户端使用
  d:\code\iviewersdk\algoadapter\ocranalyzer\OcrAnalyzer.h(24): note: 参见“std::shared_ptr<OcrAnalyzer>”的声明
d:\code\iviewersdk\algoadapter\ocranalyzer\OcrAnalyzer.h(30): warning C4251: “OcrAnalyzer::m_mutex”: class“std::mutex”需要有 dll 接口由 class“OcrAnalyzer”的客户端使用
  C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\include\mutex(78): note: 参见“std::mutex”的声明
d:\code\dphx\cpp\common\interface\../PathologyImgItem.h(12): warning C4251: “PathologyImageItem::fileName”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 struct“PathologyImageItem”的客户端使用
  C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\include\xstring(2633): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
d:\code\dphx\cpp\tfadapter\../common/wtoolkit.h(18): warning C4244: “return”: 从“__int64”转换到“int”，可能丢失数据
D:\code\dphx\cpp\common\interface/IPathogyReport.h(10): fatal error C1083: 无法打开包括文件: “wdata.h”: No such file or directory
  STReport.cpp
d:\code\dphx\cpp\common\interface\../PathologyImgItem.h(12): warning C4251: “PathologyImageItem::fileName”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 struct“PathologyImageItem”的客户端使用
  C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\include\xstring(2633): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
d:\code\dphx\cpp\tfadapter\../common/wtoolkit.h(18): warning C4244: “return”: 从“__int64”转换到“int”，可能丢失数据
D:\code\dphx\cpp\common\interface/IPathogyReport.h(10): fatal error C1083: 无法打开包括文件: “wdata.h”: No such file or directory
  TissueAanlyzer.cpp
d:\code\iviewersdk\algoadapter\tissueanalyzer\TissueAnalyzer.h(32): warning C4251: “TissueAnalyzer::m_ins”: class“std::shared_ptr<TissueAnalyzer>”需要有 dll 接口由 class“TissueAnalyzer”的客户端使用
  d:\code\iviewersdk\algoadapter\tissueanalyzer\TissueAnalyzer.h(26): note: 参见“std::shared_ptr<TissueAnalyzer>”的声明
d:\code\iviewersdk\algoadapter\tissueanalyzer\TissueAnalyzer.h(33): warning C4251: “TissueAnalyzer::m_mutex”: class“std::mutex”需要有 dll 接口由 class“TissueAnalyzer”的客户端使用
  C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\include\mutex(78): note: 参见“std::mutex”的声明
d:\code\dphx\cpp\common\interface\../PathologyImgItem.h(12): warning C4251: “PathologyImageItem::fileName”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 struct“PathologyImageItem”的客户端使用
  C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\include\xstring(2633): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
d:\code\dphx\cpp\tfadapter\../common/wtoolkit.h(18): warning C4244: “return”: 从“__int64”转换到“int”，可能丢失数据
D:\code\dphx\cpp\common\interface/IPathogyReport.h(10): fatal error C1083: 无法打开包括文件: “wdata.h”: No such file or directory
  TissueInfo.cpp
TissueAnalyzer\TissueInfo.cpp(59): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
TissueAnalyzer\TissueInfo.cpp(60): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
TissueAnalyzer\TissueInfo.cpp(107): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
TissueAnalyzer\TissueInfo.cpp(108): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
TissueAnalyzer\TissueInfo.cpp(212): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
TissueAnalyzer\TissueInfo.cpp(213): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
  TissueReport.cpp
d:\code\dphx\cpp\common\interface\../PathologyImgItem.h(12): warning C4251: “PathologyImageItem::fileName”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 struct“PathologyImageItem”的客户端使用
  C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\include\xstring(2633): note: 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
d:\code\dphx\cpp\tfadapter\../common/wtoolkit.h(18): warning C4244: “return”: 从“__int64”转换到“int”，可能丢失数据
D:\code\dphx\cpp\common\interface/IPathogyReport.h(10): fatal error C1083: 无法打开包括文件: “wdata.h”: No such file or directory
