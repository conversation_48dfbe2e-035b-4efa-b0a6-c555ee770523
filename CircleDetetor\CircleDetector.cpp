/**
* @date         2015-08-15
* @filename     CircleDetector.cpp
* @purpose      find circle object
* @version      1.0
* @history      initial draft
* <AUTHOR> UNIC, Beijing, China
* @copyright    UNIC Technologies, 2005-2015. All rights reserved.
*/
#include "./CircleDetector.h"
#include "./AlgoCircleDetector.h"
#include <opencv2/opencv.hpp>
class CCircleDetector::Data
{
public:
    CAlgoCircleDetector gap;
};

CCircleDetector::CCircleDetector()
{
    m_pD = new Data();
}

CCircleDetector::~CCircleDetector()
{
    delete m_pD;
    m_pD = NULL;
}

void CCircleDetector::GetPara(Para &para) const
{
    CircleDetector::Param paraGap;
    m_pD->gap.GetPara(paraGap);
    memcpy(&para, &paraGap, sizeof(para));
}

bool CCircleDetector::SetPara(const Para &para)
{
    CircleDetector::Param paraGap;
    memcpy(&paraGap, &para, sizeof(para));
    m_pD->gap.SetPara(paraGap);
    return true;
}

bool CCircleDetector::FindCircle(const unsigned char *pucImg, const int nWidth, const int nHeight, const int nPitch, const int nLeft, const int nTop,
    const int nRight, const int nBottom)
{
    UN_IMAGE_INFO_S stImg;
    stImg.pucBuffer = (uchar *)pucImg;
    stImg.nWidth = nWidth;
    stImg.nHeight = nHeight;
    stImg.nPitch = nPitch;
    stImg.nLeft = nLeft;
    stImg.nTop = nTop;
    stImg.nRight = nRight;
    stImg.nBottom = nBottom;
    int nRv = m_pD->gap.DoInspect(&stImg);
    if (0 != nRv)
    {
        return false;
    }
    return true;
}

bool CCircleDetector::
GetResults(float &fCircleX, float &fCircleY, float &fCircleR) const
{
    m_pD->gap.GetResults(fCircleX, fCircleY, fCircleR);
    return true;
}

