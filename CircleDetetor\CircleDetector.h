/**
* @date         2016-08-09
* @filename     CircleDetector.h
* @purpose      circle detector
* @version      1.0
* @history      initial draft
* @copyright    UNIC Technologies Inc, 2005-2016. All rights reserved.
*/

#ifndef __CIRCLE_DETECTOR_H__
#define __CIRCLE_DETECTOR_H__

#ifdef ALGOCIRCLEDETECTOR_EXPORTS
#define ALGOCIRCLEDETECTOR_API __declspec(dllexport)
#else
#define ALGOCIRCLEDETECTOR_API __declspec(dllimport)
#endif

class ALGOCIRCLEDETECTOR_API CCircleDetector
{
public:
    enum Polarity
    {
        kPolarityBright,
        kPolarityDark,
        kPolarityAny
    };
    enum Channel
    {
        Channel_Gray,
        Channel_Red,
        Channel_Green,
        Channel_Blue
    };

    struct Para
    {
        Polarity ePolarity;
        int nEdgeTh; // low threshold in hysteresis thresholding, high threshold is 2 times of nEdgeTh
        int smoothRadius;
        float fRealMinDiameter;//unic:mm
        float fRealMaxDiameter;//unic:mm
        float fEpsilon;
        int nMaxCircles; // max number of circles to detect
        int nMinScore;
        int nDownSample2sqr;
        Channel channel;
        float fPixelSize;// unit:mm
        bool bDumpImage;
        Para()
        {
            ePolarity = kPolarityDark;
            nEdgeTh = 5;
            smoothRadius = 1;
            fRealMinDiameter = 0.02f;
            fRealMaxDiameter = 0.03f;
            fEpsilon = 0.01f;
            nMaxCircles = 1;
            nMinScore = 60;
            nDownSample2sqr = 0;
            channel = Channel_Gray;
            fPixelSize = 1.00f;
            bDumpImage = false;
        }
    };

    bool SetPara(const Para &para);

    void GetPara(Para &para) const;

    bool FindCircle(const unsigned char *pucImg, const int nWidth, const int nHeight,
        const int nPitch, const int nLeft = 0, const int nTop = 0,
        const int nRight = 0, const int nBottom = 0);

    bool GetResults(float &fCircleX, float &fCircleY, float &fCircleR) const;

    explicit CCircleDetector();

    ~CCircleDetector();

private:
    class Data;
    Data *m_pD;
};

#endif // __CIRCLE_DETECTOR_H__
