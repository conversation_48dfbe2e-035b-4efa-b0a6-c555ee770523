// edge detector
// Copyright (C) UNIC Technologies. All rights reserved.
// History:
// (1) 20120711: <NAME_EMAIL>
// (2) 20121116: added ZoomImage functions by <PERSON>
// (3) 20130313: changed border in nms from 1 to 2 by <PERSON>

#include <vector>
#include <deque>
using namespace std;
#include <cmath>
#include <cassert>
#include "EdgeDetector.h"
#include "CircleDetectorCore.h"
using namespace EdgeDetector;
#ifdef _OPENMP
#include <omp.h>
#endif


#pragma warning(disable : 4018)


#ifndef UN_ANGLE_2PI
#define UN_ANGLE_2PI 256
#define UN_ANGLE_PI (UN_ANGLE_2PI>>1)
#define UN_ANGLE_PI2 (UN_ANGLE_PI>>1)
#endif

#ifndef UN_ANGLE_PRECICE
#define UN_ANGLE_PRECICE	64
#endif

#define MIRROR_START(i) ((i)<0?-(i):(i))
#define MIRROR_END(i, length) ((i)>=(length)?(length)+(length)-(i)-2:(i))

/************************************************************************/
/* Define angle image, use 0-255 to present 0-360 degree				*/
/************************************************************************/
static int g_nAngleSin[UN_ANGLE_2PI] = {
	0, 1608, 3216, 4821, 6424, 8022, 9616, 11204, 12785, 14359, 15924, 17479, 19024, 20557, 22078, 23586, 25080, 26558, 28020, 29466, 30893, 
	32303, 33692, 35062, 36410, 37736, 39040, 40320, 41576, 42806, 44011, 45190, 46341, 47464, 48559, 49624, 50660, 51665, 52639, 53581, 54491, 
	55368, 56212, 57022, 57798, 58538, 59244, 59914, 60547, 61145, 61705, 62228, 62714, 63162, 63572, 63944, 64277, 64571, 64827, 65043, 65220, 
	65358, 65457, 65516, 65536, 65516, 65457, 65358, 65220, 65043, 64827, 64571, 64277, 63944, 63572, 63162, 62714, 62228, 61705, 61145, 60547, 
	59914, 59244, 58538, 57798, 57022, 56212, 55368, 54491, 53581, 52639, 51665, 50660, 49624, 48559, 47464, 46341, 45190, 44011, 42806, 41576, 
	40320, 39040, 37736, 36410, 35062, 33692, 32303, 30893, 29466, 28020, 26558, 25080, 23586, 22078, 20557, 19024, 17479, 15924, 14359, 12785, 
	11204, 9616, 8022, 6424, 4821, 3216, 1608, 0, -1608, -3216, -4821, -6424, -8022, -9616, -11204, -12785, -14359, -15924, -17479, -19024, 
	-20557, -22078, -23586, -25080, -26558, -28020, -29466, -30893, -32303, -33692, -35062, -36410, -37736, -39040, -40320, -41576, -42806, -44011, -45190, -46341, 
	-47464, -48559, -49624, -50660, -51665, -52639, -53581, -54491, -55368, -56212, -57022, -57798, -58538, -59244, -59914, -60547, -61145, -61705, -62228, -62714, 
	-63162, -63572, -63944, -64277, -64571, -64827, -65043, -65220, -65358, -65457, -65516, -65536, -65516, -65457, -65358, -65220, -65043, -64827, -64571, -64277, 
	-63944, -63572, -63162, -62714, -62228, -61705, -61145, -60547, -59914, -59244, -58538, -57798, -57022, -56212, -55368, -54491, -53581, -52639, -51665, -50660, 
	-49624, -48559, -47464, -46341, -45190, -44011, -42806, -41576, -40320, -39040, -37736, -36410, -35062, -33692, -32303, -30893, -29466, -28020, -26558, -25080, 
	-23586, -22078, -20557, -19024, -17479, -15924, -14359, -12785, -11204, -9616, -8022, -6424, -4821, -3216, -1608
};

static int g_nAngleCos[UN_ANGLE_2PI] = {
	65536, 65516, 65457, 65358, 65220, 65043, 64827, 64571, 64277, 63944, 63572, 63162, 62714, 62228, 61705, 61145, 60547, 59914, 59244, 58538, 57798, 
	57022, 56212, 55368, 54491, 53581, 52639, 51665, 50660, 49624, 48559, 47464, 46341, 45190, 44011, 42806, 41576, 40320, 39040, 37736, 36410, 
	35062, 33692, 32303, 30893, 29466, 28020, 26558, 25080, 23586, 22078, 20557, 19024, 17479, 15924, 14359, 12785, 11204, 9616, 8022, 6424, 
	4821, 3216, 1608, 0, -1608, -3216, -4821, -6424, -8022, -9616, -11204, -12785, -14359, -15924, -17479, -19024, -20557, -22078, -23586, -25080, 
	-26558, -28020, -29466, -30893, -32303, -33692, -35062, -36410, -37736, -39040, -40320, -41576, -42806, -44011, -45190, -46341, -47464, -48559, -49624, -50660, 
	-51665, -52639, -53581, -54491, -55368, -56212, -57022, -57798, -58538, -59244, -59914, -60547, -61145, -61705, -62228, -62714, -63162, -63572, -63944, -64277, 
	-64571, -64827, -65043, -65220, -65358, -65457, -65516, -65536, -65516, -65457, -65358, -65220, -65043, -64827, -64571, -64277, -63944, -63572, -63162, -62714, 
	-62228, -61705, -61145, -60547, -59914, -59244, -58538, -57798, -57022, -56212, -55368, -54491, -53581, -52639, -51665, -50660, -49624, -48559, -47464, -46341, 
	-45190, -44011, -42806, -41576, -40320, -39040, -37736, -36410, -35062, -33692, -32303, -30893, -29466, -28020, -26558, -25080, -23586, -22078, -20557, -19024, 
	-17479, -15924, -14359, -12785, -11204, -9616, -8022, -6424, -4821, -3216, -1608, 0, 1608, 3216, 4821, 6424, 8022, 9616, 11204, 12785, 
	14359, 15924, 17479, 19024, 20557, 22078, 23586, 25080, 26558, 28020, 29466, 30893, 32303, 33692, 35062, 36410, 37736, 39040, 40320, 41576, 
	42806, 44011, 45190, 46341, 47464, 48559, 49624, 50660, 51665, 52639, 53581, 54491, 55368, 56212, 57022, 57798, 58538, 59244, 59914, 60547, 
	61145, 61705, 62228, 62714, 63162, 63572, 63944, 64277, 64571, 64827, 65043, 65220, 65358, 65457, 65516
};

static int g_nAngleTan[] = {
	0,1609,3220,4834,6455,8083,9721,11372,13036,14717,16416,18136,19880,21650,23449,25280,27146,29050,
	30996,32988,35030,37126,39281,41500,43790,46156,48605,51145,53784,56532,59398,62395,65536
};

static int g_nArcTan[UN_ANGLE_PRECICE][UN_ANGLE_PRECICE] = 
{{0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0},
{64,32,19,13,10,8,7,6,5,5,4,4,3,3,3,3,3,2,2,2,2,2,2,2,2,2,2,2,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1},
{64,45,32,24,19,16,13,11,10,9,8,7,7,6,6,5,5,5,5,4,4,4,4,4,3,3,3,3,3,3,3,3,3,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,1,1,1,1,1,1,1,1,1},
{64,51,40,32,26,22,19,16,15,13,12,11,10,9,9,8,8,7,7,6,6,6,6,5,5,5,5,5,4,4,4,4,4,4,4,3,3,3,3,3,3,3,3,3,3,3,3,3,3,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2},
{64,54,45,38,32,27,24,21,19,17,16,14,13,12,11,11,10,9,9,8,8,8,7,7,7,6,6,6,6,6,5,5,5,5,5,5,5,4,4,4,4,4,4,4,4,4,4,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3},
{64,56,48,42,37,32,28,25,23,21,19,17,16,15,14,13,12,12,11,10,10,10,9,9,8,8,8,7,7,7,7,7,6,6,6,6,6,5,5,5,5,5,5,5,5,5,4,4,4,4,4,4,4,4,4,4,4,4,4,3,3,3,3,3},
{64,57,51,45,40,36,32,29,26,24,22,20,19,18,16,16,15,14,13,12,12,11,11,10,10,10,9,9,9,8,8,8,8,7,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,4,4,4,4,4,4,4,4,4},
{64,58,53,48,43,39,35,32,29,27,25,23,22,20,19,18,17,16,15,14,14,13,13,12,12,11,11,10,10,10,9,9,9,9,8,8,8,8,7,7,7,7,7,7,6,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5},
{64,59,54,49,45,41,38,35,32,30,27,26,24,22,21,20,19,18,17,16,16,15,14,14,13,13,12,12,11,11,11,10,10,10,9,9,9,9,8,8,8,8,8,7,7,7,7,7,7,7,6,6,6,6,6,6,6,6,6,5,5,5,5,5},
{64,59,55,51,47,43,40,37,34,32,30,28,26,25,23,22,21,20,19,18,17,16,16,15,15,14,14,13,13,12,12,12,11,11,11,10,10,10,9,9,9,9,9,8,8,8,8,8,8,7,7,7,7,7,7,7,6,6,6,6,6,6,6,6},
{64,60,56,52,48,45,42,39,37,34,32,30,28,27,25,24,23,22,21,20,19,18,17,17,16,16,15,14,14,14,13,13,12,12,12,11,11,11,10,10,10,10,10,9,9,9,9,9,8,8,8,8,8,8,7,7,7,7,7,7,7,7,7,6},
{64,60,57,53,50,47,44,41,38,36,34,32,30,29,27,26,25,23,22,21,20,20,19,18,18,17,16,16,15,15,14,14,13,13,13,12,12,12,11,11,11,11,10,10,10,10,10,9,9,9,9,9,8,8,8,8,8,8,8,8,7,7,7,7},
{64,61,57,54,51,48,45,42,40,38,36,34,32,30,29,27,26,25,24,23,22,21,20,20,19,18,18,17,16,16,16,15,15,14,14,13,13,13,12,12,12,12,11,11,11,11,10,10,10,10,10,9,9,9,9,9,9,8,8,8,8,8,8,8},
{64,61,58,55,52,49,46,44,42,39,37,35,34,32,30,29,28,27,25,24,23,23,22,21,20,20,19,18,18,17,17,16,16,15,15,14,14,14,13,13,13,13,12,12,12,11,11,11,11,11,10,10,10,10,10,9,9,9,9,9,9,9,8,8},
{64,61,58,55,53,50,48,45,43,41,39,37,35,34,32,31,29,28,27,26,25,24,23,22,22,21,20,19,19,18,18,17,17,16,16,16,15,15,14,14,14,13,13,13,13,12,12,12,12,11,11,11,11,11,10,10,10,10,10,9,9,9,9,9},
{64,61,59,56,53,51,48,46,44,42,40,38,37,35,33,32,31,29,28,27,26,25,24,24,23,22,21,21,20,19,19,18,18,17,17,16,16,16,15,15,15,14,14,14,13,13,13,13,12,12,12,12,11,11,11,11,11,10,10,10,10,10,10,10},
{64,61,59,56,54,52,49,47,45,43,41,39,38,36,35,33,32,31,30,29,27,27,26,25,24,23,22,22,21,21,20,19,19,18,18,17,17,17,16,16,16,15,15,15,14,14,14,13,13,13,13,12,12,12,12,12,11,11,11,11,11,10,10,10},
{64,62,59,57,55,52,50,48,46,44,42,41,39,37,36,35,33,32,31,30,29,28,27,26,25,24,24,23,22,22,21,20,20,19,19,18,18,18,17,17,16,16,16,15,15,15,14,14,14,14,13,13,13,13,12,12,12,12,12,11,11,11,11,11},
{64,62,59,57,55,53,51,49,47,45,43,42,40,39,37,36,34,33,32,31,30,29,28,27,26,25,25,24,23,23,22,21,21,20,20,19,19,18,18,18,17,17,16,16,16,16,15,15,15,14,14,14,14,13,13,13,13,12,12,12,12,12,12,11},
{64,62,60,58,56,54,52,50,48,46,44,43,41,40,38,37,35,34,33,32,31,30,29,28,27,26,26,25,24,24,23,22,22,21,21,20,20,19,19,18,18,18,17,17,17,16,16,16,15,15,15,15,14,14,14,14,13,13,13,13,12,12,12,12},
{64,62,60,58,56,54,52,50,48,47,45,44,42,41,39,38,37,35,34,33,32,31,30,29,28,27,27,26,25,25,24,23,23,22,22,21,21,20,20,19,19,18,18,18,17,17,17,16,16,16,16,15,15,15,14,14,14,14,14,13,13,13,13,13},
{64,62,60,58,56,54,53,51,49,48,46,44,43,41,40,39,37,36,35,34,33,32,31,30,29,28,28,27,26,26,25,24,24,23,23,22,22,21,21,20,20,19,19,19,18,18,17,17,17,16,16,16,16,15,15,15,15,14,14,14,14,14,13,13},
{64,62,60,58,57,55,53,51,50,48,47,45,44,42,41,40,38,37,36,35,34,33,32,31,30,29,29,28,27,26,26,25,25,24,23,23,22,22,21,21,20,20,20,19,19,19,18,18,18,17,17,17,16,16,16,16,15,15,15,15,14,14,14,14},
{64,62,60,59,57,55,54,52,50,49,47,46,44,43,42,40,39,38,37,36,35,34,33,32,31,30,30,29,28,27,27,26,25,25,24,24,23,23,22,22,21,21,20,20,20,19,19,19,18,18,18,17,17,17,16,16,16,16,15,15,15,15,14,14},
{64,62,61,59,57,56,54,52,51,49,48,46,45,44,42,41,40,39,38,37,36,35,34,33,32,31,30,30,29,28,27,27,26,26,25,24,24,23,23,22,22,22,21,21,20,20,20,19,19,19,18,18,18,17,17,17,16,16,16,16,16,15,15,15},
{64,62,61,59,58,56,54,53,51,50,48,47,46,44,43,42,41,40,39,38,37,36,35,34,33,32,31,30,30,29,28,28,27,26,26,25,25,24,24,23,23,22,22,21,21,21,20,20,20,19,19,19,18,18,18,17,17,17,17,16,16,16,16,15},
{64,62,61,59,58,56,55,53,52,50,49,48,46,45,44,43,42,40,39,38,37,36,35,34,34,33,32,31,30,30,29,28,28,27,27,26,25,25,24,24,23,23,23,22,22,21,21,21,20,20,20,19,19,19,18,18,18,17,17,17,17,16,16,16},
{64,62,61,59,58,57,55,54,52,51,50,48,47,46,45,43,42,41,40,39,38,37,36,35,34,34,33,32,31,31,30,29,29,28,27,27,26,26,25,25,24,24,23,23,22,22,22,21,21,21,20,20,20,19,19,19,18,18,18,17,17,17,17,16},
{64,63,61,60,58,57,55,54,53,51,50,49,48,46,45,44,43,42,41,40,39,38,37,36,35,34,34,33,32,31,31,30,29,29,28,27,27,26,26,25,25,24,24,24,23,23,22,22,22,21,21,20,20,20,19,19,19,19,18,18,18,18,17,17},
{64,63,61,60,58,57,56,54,53,52,50,49,48,47,46,45,43,42,41,40,39,38,38,37,36,35,34,33,33,32,31,31,30,29,29,28,28,27,27,26,26,25,25,24,24,23,23,23,22,22,21,21,21,20,20,20,19,19,19,19,18,18,18,18},
{64,63,61,60,59,57,56,55,53,52,51,50,48,47,46,45,44,43,42,41,40,39,38,37,37,36,35,34,33,33,32,31,31,30,29,29,28,28,27,27,26,26,25,25,24,24,24,23,23,22,22,22,21,21,21,20,20,20,19,19,19,19,18,18},
{64,63,61,60,59,57,56,55,54,52,51,50,49,48,47,46,45,44,43,42,41,40,39,38,37,36,36,35,34,33,33,32,31,31,30,30,29,28,28,27,27,26,26,25,25,25,24,24,23,23,23,22,22,22,21,21,21,20,20,20,19,19,19,19},
{64,63,61,60,59,58,56,55,54,53,52,51,49,48,47,46,45,44,43,42,41,40,39,39,38,37,36,35,35,34,33,33,32,31,31,30,30,29,29,28,27,27,27,26,26,25,25,24,24,24,23,23,22,22,22,21,21,21,21,20,20,20,19,19},
{64,63,62,60,59,58,57,55,54,53,52,51,50,49,48,47,46,45,44,43,42,41,40,39,38,38,37,36,35,35,34,33,33,32,31,31,30,30,29,29,28,28,27,27,26,26,25,25,25,24,24,23,23,23,22,22,22,21,21,21,20,20,20,20},
{64,63,62,60,59,58,57,56,55,53,52,51,50,49,48,47,46,45,44,43,42,41,41,40,39,38,37,37,36,35,35,34,33,33,32,31,31,30,30,29,29,28,28,27,27,26,26,26,25,25,24,24,24,23,23,23,22,22,22,21,21,21,20,20},
{64,63,62,61,59,58,57,56,55,54,53,52,51,50,48,48,47,46,45,44,43,42,41,40,40,39,38,37,37,36,35,34,34,33,33,32,31,31,30,30,29,29,28,28,27,27,27,26,26,25,25,25,24,24,23,23,23,22,22,22,22,21,21,21},
{64,63,62,61,59,58,57,56,55,54,53,52,51,50,49,48,47,46,45,44,43,42,42,41,40,39,39,38,37,36,36,35,34,34,33,33,32,31,31,30,30,29,29,28,28,27,27,27,26,26,25,25,25,24,24,24,23,23,23,22,22,22,21,21},
{64,63,62,61,60,59,57,56,55,54,53,52,51,50,49,48,47,46,46,45,44,43,42,41,41,40,39,38,38,37,36,36,35,34,34,33,33,32,31,31,30,30,29,29,28,28,28,27,27,26,26,26,25,25,24,24,24,23,23,23,23,22,22,22},
{64,63,62,61,60,59,58,57,56,55,54,53,52,51,50,49,48,47,46,45,44,43,43,42,41,40,40,39,38,37,37,36,35,35,34,34,33,33,32,31,31,30,30,29,29,29,28,28,27,27,26,26,26,25,25,25,24,24,24,23,23,23,22,22},
{64,63,62,61,60,59,58,57,56,55,54,53,52,51,50,49,48,47,46,46,45,44,43,42,42,41,40,39,39,38,37,37,36,35,35,34,34,33,33,32,31,31,30,30,30,29,29,28,28,27,27,27,26,26,25,25,25,24,24,24,23,23,23,23},
{64,63,62,61,60,59,58,57,56,55,54,53,52,51,50,49,48,48,47,46,45,44,44,43,42,41,41,40,39,38,38,37,37,36,35,35,34,34,33,33,32,31,31,31,30,30,29,29,28,28,27,27,27,26,26,26,25,25,25,24,24,24,23,23},
{64,63,62,61,60,59,58,57,56,55,54,53,52,51,51,50,49,48,47,46,46,45,44,43,42,42,41,40,40,39,38,38,37,36,36,35,35,34,34,33,33,32,32,31,31,30,30,29,29,28,28,28,27,27,26,26,26,25,25,25,24,24,24,24},
{64,63,62,61,60,59,58,57,56,55,54,54,53,52,51,50,49,48,48,47,46,45,44,44,43,42,41,41,40,39,39,38,37,37,36,36,35,35,34,34,33,32,32,32,31,31,30,30,29,29,28,28,28,27,27,27,26,26,26,25,25,25,24,24},
{64,63,62,61,60,59,58,57,57,56,55,54,53,52,51,50,49,49,48,47,46,45,45,44,43,43,42,41,40,40,39,39,38,37,37,36,36,35,35,34,33,33,32,32,32,31,31,30,30,29,29,29,28,28,27,27,27,26,26,26,25,25,25,24},
{64,63,62,61,60,59,58,58,57,56,55,54,53,52,51,51,50,49,48,47,47,46,45,44,44,43,42,42,41,40,40,39,38,38,37,37,36,36,35,34,34,33,33,32,32,32,31,31,30,30,29,29,29,28,28,27,27,27,26,26,26,25,25,25},
{64,63,62,61,60,59,59,58,57,56,55,54,53,53,52,51,50,49,48,48,47,46,45,45,44,43,43,42,41,41,40,39,39,38,38,37,37,36,35,35,34,34,33,33,32,32,32,31,31,30,30,29,29,29,28,28,28,27,27,27,26,26,26,25},
{64,63,62,61,60,60,59,58,57,56,55,54,54,53,52,51,50,50,49,48,47,47,46,45,44,44,43,42,42,41,40,40,39,39,38,37,37,36,36,35,35,34,34,33,33,32,32,32,31,31,30,30,30,29,29,28,28,28,27,27,27,26,26,26},
{64,63,62,61,61,60,59,58,57,56,55,55,54,53,52,51,51,50,49,48,48,47,46,45,45,44,43,43,42,41,41,40,40,39,38,38,37,37,36,36,35,35,34,34,33,33,32,32,32,31,31,30,30,30,29,29,28,28,28,27,27,27,26,26},
{64,63,62,61,61,60,59,58,57,56,56,55,54,53,52,52,51,50,49,49,48,47,46,46,45,44,44,43,42,42,41,41,40,39,39,38,38,37,37,36,36,35,35,34,34,33,33,32,32,32,31,31,30,30,30,29,29,29,28,28,27,27,27,27},
{64,63,62,62,61,60,59,58,57,57,56,55,54,53,53,52,51,50,50,49,48,48,47,46,45,45,44,43,43,42,42,41,40,40,39,39,38,38,37,37,36,36,35,35,34,34,33,33,32,32,32,31,31,30,30,30,29,29,29,28,28,28,27,27},
{64,63,62,62,61,60,59,58,58,57,56,55,54,54,53,52,51,51,50,49,48,48,47,46,46,45,44,44,43,43,42,41,41,40,40,39,39,38,38,37,37,36,36,35,35,34,34,33,33,32,32,32,31,31,30,30,30,29,29,29,28,28,28,27},
{64,63,62,62,61,60,59,58,58,57,56,55,55,54,53,52,52,51,50,49,49,48,47,47,46,45,45,44,44,43,42,42,41,41,40,39,39,38,38,37,37,36,36,35,35,35,34,34,33,33,32,32,32,31,31,30,30,30,29,29,29,28,28,28},
{64,63,62,62,61,60,59,59,58,57,56,56,55,54,53,53,52,51,50,50,49,48,48,47,46,46,45,44,44,43,43,42,42,41,40,40,39,39,38,38,37,37,36,36,35,35,34,34,34,33,33,32,32,32,31,31,30,30,30,29,29,29,28,28},
{64,63,62,62,61,60,59,59,58,57,56,56,55,54,53,53,52,51,51,50,49,49,48,47,47,46,45,45,44,44,43,42,42,41,41,40,40,39,39,38,38,37,37,36,36,35,35,34,34,34,33,33,32,32,32,31,31,31,30,30,29,29,29,28},
{64,63,62,62,61,60,59,59,58,57,57,56,55,54,54,53,52,52,51,50,50,49,48,48,47,46,46,45,45,44,43,43,42,42,41,41,40,40,39,39,38,38,37,37,36,36,35,35,34,34,34,33,33,32,32,32,31,31,31,30,30,30,29,29},
{64,63,63,62,61,60,60,59,58,57,57,56,55,55,54,53,52,52,51,50,50,49,48,48,47,47,46,45,45,44,44,43,43,42,41,41,40,40,39,39,38,38,37,37,37,36,36,35,35,34,34,34,33,33,32,32,32,31,31,31,30,30,30,29},
{64,63,63,62,61,60,60,59,58,58,57,56,55,55,54,53,53,52,51,51,50,49,49,48,48,47,46,46,45,45,44,43,43,42,42,41,41,40,40,39,39,38,38,37,37,36,36,36,35,35,34,34,34,33,33,32,32,32,31,31,31,30,30,30},
{64,63,63,62,61,60,60,59,58,58,57,56,56,55,54,54,53,52,52,51,50,50,49,48,48,47,47,46,45,45,44,44,43,43,42,42,41,41,40,40,39,39,38,38,37,37,36,36,35,35,35,34,34,33,33,33,32,32,32,31,31,31,30,30},
{64,63,63,62,61,60,60,59,58,58,57,56,56,55,54,54,53,52,52,51,50,50,49,49,48,47,47,46,46,45,45,44,43,43,42,42,41,41,40,40,39,39,38,38,38,37,37,36,36,35,35,35,34,34,33,33,33,32,32,32,31,31,31,30},
{64,63,63,62,61,61,60,59,59,58,57,56,56,55,55,54,53,53,52,51,51,50,49,49,48,48,47,47,46,45,45,44,44,43,43,42,42,41,41,40,40,39,39,38,38,37,37,37,36,36,35,35,35,34,34,33,33,33,32,32,32,31,31,31},
{64,63,63,62,61,61,60,59,59,58,57,57,56,55,55,54,53,53,52,52,51,50,50,49,48,48,47,47,46,46,45,45,44,44,43,42,42,41,41,41,40,40,39,39,38,38,37,37,37,36,36,35,35,35,34,34,33,33,33,32,32,32,31,31},
{64,63,63,62,61,61,60,59,59,58,57,57,56,55,55,54,54,53,52,52,51,50,50,49,49,48,48,47,46,46,45,45,44,44,43,43,42,42,41,41,40,40,39,39,39,38,38,37,37,36,36,36,35,35,34,34,34,33,33,33,32,32,32,31},
{64,63,63,62,61,61,60,59,59,58,57,57,56,56,55,54,54,53,52,52,51,51,50,50,49,48,48,47,47,46,46,45,45,44,44,43,43,42,42,41,41,40,40,39,39,38,38,38,37,37,36,36,36,35,35,34,34,34,33,33,33,32,32,32},
{64,63,63,62,61,61,60,59,59,58,58,57,56,56,55,54,54,53,53,52,51,51,50,50,49,49,48,48,47,46,46,45,45,44,44,43,43,42,42,41,41,40,40,40,39,39,38,38,37,37,37,36,36,36,35,35,34,34,34,33,33,33,32,32}
};


const int g_kMaxBinomialKernelLength = 23;

int g_nBinomialKernels[][g_kMaxBinomialKernelLength] = 
{
	{1,2,1},
	{1,4,6,4,1},
	{1,6,15,20,15,6,1},
	{1,8,28,56,70,56,28,8,1},
	{1,10,45,120,210,252,210,120,45,10,1},
	{1,12,66,220,495,792,924,792,495,220,66,12,1},
	{1,14,91,364,1001,2002,3003,3432,3003,2002,1001,364,91,14,1},
	{1,16,120,560,1820,4368,8008,11440,12870,11440,8008,4368,1820,560,120,16,1},
	{1,18,153,816,3060,8568,18564,31824,43758,48620,43758,31824,18564,8568,3060,816,153,18,1},
	{1,20,190,1140,4845,15504,38760,77520,125970,167960,184756,167960,125970,77520,38760,15504,4845,1140,190,20,1},
	{1,22,231,1540,7315,26334,74613,170544,319770,497420,646646,705432,646646,497420,319770,170544,74613,26334,7315,1540,231,22,1}
};



// static void BinomialConvolveX(unsigned char *src, unsigned char *dst, int width, int height, int *kernel, int radius)
// {
// 	int sum;
// 	int offset;
// 
// 	int kernelLength = radius*2 + 1;
// 	for (int y = 0; y < height; y++)
// 	{
// 		for (int x = radius; x < width - radius; x++)
// 		{
// 			offset = width * y + x;
// 			sum = 0;
// 			for (int k = -radius; k <= radius; k++)
// 				sum += src[offset + k] * kernel[radius + k];
// 			dst[offset] = (unsigned char) (sum>>(kernelLength-1));
// 		}
// 	}
// 
// 	for (int y = 0; y < height; y++)
// 	{
// 		for (int x = 0; x < radius; x++)
// 		{
// 			offset = width * y + x;
// 			sum = 0;
// 			for (int k = -radius; k <= radius; k++)
// 				sum += src[width * y + MIRROR_START(x + k)] * kernel[radius + k];
// 			dst[offset] = (unsigned char) (sum>>(kernelLength-1));
// 		}
// 	}
// 
// 	for (int y = 0; y < height; y++)
// 	{
// 		for (int x = width - radius; x < width; x++)
// 		{
// 			offset = width * y + x;
// 			sum = 0;
// 			for (int k = -radius; k <= radius; k++)
// 				sum += src[width * y + MIRROR_END(x + k, width)] * kernel[radius + k];
// 			dst[offset] = (unsigned char) (sum>>(kernelLength-1));
// 		}
// 	}
// 
// }

// static void BinomialConvolveY(unsigned char *src, unsigned char *dst, int width, int height, int *kernel, int radius)
// {
// 	int sum;
// 	int offset;
// 
// 	int kernelLength = radius*2 + 1;
// 	for (int y = radius; y < height - radius; y++)
// 	{
// 		for (int x = 0; x < width; x++)
// 		{
// 			offset = width * y + x;
// 			sum = 0;
// 			for (int k = -radius; k <= radius; k++)
// 				sum += src[offset + width * k] * kernel[radius + k];
// 			dst[offset] = (unsigned char) (sum>>(kernelLength-1));
// 		}
// 	}
// 
// 	for (int y = 0; y < radius; y++)
// 	{
// 		for (int x = 0; x < width; x++)
// 		{
// 			offset = width * y + x;
// 			sum = 0;
// 			for (int k = -radius; k <= radius; k++)
// 				sum += src[width * MIRROR_START(y + k) + x] * kernel[radius + k];
// 			dst[offset] = (unsigned char) (sum>>(kernelLength-1));
// 		}
// 	}
// 
// 	for (int y = height - radius; y < height; y++)
// 	{
// 		for (int x = 0; x < width; x++)
// 		{
// 			offset = width * y + x;
// 			sum = 0;
// 			for (int k = -radius; k <= radius; k++)
// 				sum += src[width * MIRROR_END(y + k, height) + x] * kernel[radius + k];
// 			dst[offset] = (unsigned char) (sum>>(kernelLength-1));
// 		}
// 	}
// }



#if 0

void EdgeDetector::BinomialSmooth(unsigned char *pSrcImg, unsigned char *pDstImg, int nImgWidth, int nImgHeight, int nMaskWidth, int nMaskHeight)
{
	nMaskWidth = ((nMaskWidth>>1)<<1) + 1;
	nMaskHeight = ((nMaskHeight>>1)<<1) + 1;

	if (nMaskWidth > g_kMaxBinomialKernelLength)
		nMaskWidth = g_kMaxBinomialKernelLength;
	if (nMaskHeight > g_kMaxBinomialKernelLength)
		nMaskHeight = g_kMaxBinomialKernelLength;

	if (nMaskWidth <= 1 && nMaskHeight <= 1)
	{
		if (pSrcImg != pDstImg)
			memcpy(pDstImg, pSrcImg, sizeof(unsigned char) * nImgWidth * nImgHeight);
		return;
	}

	const int rw = nMaskWidth/2, rh = nMaskHeight/2;

	unsigned char *pTmpImg = new unsigned char[nImgWidth * nImgHeight];
	BinomialConvolveX(pSrcImg, pTmpImg, nImgWidth, nImgHeight, g_nBinomialKernels[rw-1], rw);
	BinomialConvolveY(pTmpImg, pDstImg, nImgWidth, nImgHeight, g_nBinomialKernels[rh-1], rh);

	delete [] pTmpImg;
}

#else

static void BinomialConvolve(unsigned char *pSrc, unsigned char *pDst, int nSrcStep, int nDstStep, int nLen, const int *pKernel, int nKernelLen)
{
	int nKernelRadius = nKernelLen>>1;

	const unsigned char *pS = pSrc;
	unsigned char *pD = pDst + nKernelRadius * nSrcStep;
	for (int i = 0; i < nLen - nKernelLen + 1; i++, pS += nSrcStep, pD += nDstStep)
	{
		int nSum = 0;
		const unsigned char *pS2 = pS;
		for (int j = nKernelLen-1; j >= 0; j--, pS2 += nSrcStep)
			nSum += (int)(*pS2 * pKernel[j]);
		*pD = (unsigned char) (nSum >> (nKernelLen - 1));
	}
}

// smooth image by binomial filter
// pSrcImg - source image
// pDstImg - destination image
// nMaskWidth/Height - mask size (odd number)
// standard variance of Gaussian filter ~= sqrt((nMaskSize-1)/4)
void EdgeDetector::BinomialSmooth(unsigned char *pSrcImg, unsigned char *pDstImg, int nImgWidth, int nImgHeight, int nMaskWidth, int nMaskHeight)
{
	nMaskWidth = ((nMaskWidth>>1)<<1) + 1;
	nMaskHeight = ((nMaskHeight>>1)<<1) + 1;

	if (nMaskWidth > g_kMaxBinomialKernelLength)
		nMaskWidth = g_kMaxBinomialKernelLength;
	if (nMaskHeight > g_kMaxBinomialKernelLength)
		nMaskHeight = g_kMaxBinomialKernelLength;

	if (nMaskWidth <= 1 && nMaskHeight <= 1)
	{
		memcpy(pDstImg, pSrcImg, sizeof(unsigned char) * nImgWidth * nImgHeight);
		return;
	}

	const int rw = nMaskWidth/2, rh = nMaskHeight/2;

	int nBigImgWidth = nImgWidth + rw*2;
	int nBigImgHeight = nImgHeight + rh*2;
	unsigned char *pBigImg1 = new unsigned char[nBigImgWidth * nBigImgHeight * 2];
	unsigned char *pBigImg2 = pBigImg1 + nBigImgWidth * nBigImgHeight;

	if (nMaskWidth == 1)
	{
		for (int y = rh; y < nImgHeight + rh; y++)
			memcpy(pBigImg2 + y * nBigImgWidth + rw, pSrcImg + (y - rh) * nImgWidth, sizeof(unsigned char) * nImgWidth);
	}
	else
	{
		for (int y = rh; y < nImgHeight + rh; y++)
		{
			memcpy(pBigImg1 + y * nBigImgWidth + rw, pSrcImg + (y - rh) * nImgWidth, sizeof(unsigned char) * nImgWidth);
			for (int k = 0; k < rw; k++)
			{
				pBigImg1[y * nBigImgWidth + k] = pBigImg1[y * nBigImgWidth + rw*2 - 1 - k];
				pBigImg1[y * nBigImgWidth + nImgWidth + rw + k] = pBigImg1[y * nBigImgWidth + nImgWidth + rw - 1 - k];
			}
		}

		const int *pnKernel = g_nBinomialKernels[rw - 1];
		for (int y = rh; y < nImgHeight + rh; y++)
			BinomialConvolve(pBigImg1 + y * nBigImgWidth, pBigImg2 + y * nBigImgWidth, 1, 1, nImgWidth + rw*2, pnKernel, nMaskWidth);
	}

	if (nMaskHeight == 1)
	{
		for (int y = 0; y < nImgHeight; y++)
			memcpy(pDstImg + y * nImgWidth, pBigImg2 + (y + rh) * nBigImgWidth + rw, sizeof(unsigned char) * nImgWidth);
	}
	else
	{
		for (int x = rw; x < nImgWidth + rw; x++)
		{
			for (int k = 0; k < rh; k++)
			{
				pBigImg2[k * nBigImgWidth + x] = pBigImg2[(rh*2 - 1 - k) * nBigImgWidth + x];
				pBigImg2[(nImgHeight + rh + k) * nBigImgWidth + x] = pBigImg2[(nImgHeight + rh - 1 - k) * nBigImgWidth + x];
			}
		}

		const int *pnKernel = g_nBinomialKernels[rh - 1];
		for (int x = rw; x < nImgWidth + rw; x++)
			BinomialConvolve(pBigImg2 + x, pBigImg1 + x, nBigImgWidth,  nBigImgWidth, nBigImgHeight, pnKernel, nMaskHeight);

		for (int y = 0; y < nImgHeight; y++)
			memcpy(pDstImg + y * nImgWidth, pBigImg1 + (y + rh) * nBigImgWidth + rw, sizeof(unsigned char) * nImgWidth);
	}

	delete [] pBigImg1;
}

#endif


void EdgeDetector::BoxSmooth(unsigned char *pImg, int nImgWidth, int nImgHeight, int nMaskWidth, int nMaskHeight)
{
	if (nMaskWidth <= 1 && nMaskHeight <= 1)
		return;

	unsigned char *pTmp = new unsigned char[nImgWidth * nImgHeight];
	BoxSmooth(pImg, pTmp, nImgWidth, nImgHeight, nMaskWidth, nMaskHeight);
	memcpy(pImg, pTmp, sizeof(unsigned char) * nImgWidth * nImgHeight);
	delete [] pTmp;
}

void EdgeDetector::BoxSmooth(unsigned char *src, unsigned char *dst, int w, int h, int nMaskWidth, int nMaskHeight)
{
	nMaskWidth = ((nMaskWidth>>1)<<1) + 1;
	nMaskHeight = ((nMaskHeight>>1)<<1) + 1;

	int wr = nMaskWidth/2;
	int hr = nMaskHeight/2;

	if (w < 2*wr+1 || h < 2*hr+1)
		return;

	int *colSum = new int[w];

	int i, j;
	int count, rows;
	int sum;
	int lft, rgt, top, btm;
	// init
	for (i = 0; i < w; i++)
	{
		colSum[i] = 0;
		for (j = 0; j < hr; j++)
			colSum[i] += src[j * w + i];
	}

	// top
	for (j = 0, btm = hr, rows = hr+1; j <= hr; j++, btm++, rows++)
	{
		// init
		for (i = 0; i < w; i++)
			colSum[i] += src[btm * w + i];

		for (i = 0, sum = 0, count = rows; i < wr; i++, count += rows)
			sum += colSum[i];

		// left
		for (i = 0, rgt = wr; i <= wr; i++, rgt++, count += rows)
		{
			sum += colSum[rgt];
			dst[j * w + i] = (unsigned char) (sum/count);
		}
		// middle
		count -= rows;
		for (lft = 0; i < w-wr; i++, lft++, rgt++)
		{
			sum += colSum[rgt] - colSum[lft];
			dst[j * w + i] = (unsigned char) (sum/count);
		}
		// right
		count -= rows;
		for (; i < w; i++, lft++, count -= rows)
		{
			sum -= colSum[lft];
			dst[j * w + i] = (unsigned char) (sum/count);
		}
	}

	// middle
	for (top = 0, rows = hr*2+1; j < h-hr; j++, top++, btm++)
	{
		// init
		for (i = 0; i < w; i++)
			colSum[i] += src[btm * w + i] - src[top * w + i];

		for (i = 0, sum = 0, count = rows; i < wr; i++, count += rows)
			sum += colSum[i];
		// left
		for (i = 0, rgt = wr; i <= wr; i++, rgt++, count += rows)
		{
			sum += colSum[rgt];
			dst[j * w + i] = (unsigned char) (sum/count);
		}
		// middle
		count -= rows;
		for (lft = 0; i < w-wr; i++, lft++, rgt++)
		{
			sum += colSum[rgt] - colSum[lft];
			dst[j * w + i] = (unsigned char) (sum/count);
		}
		// right
		count -= rows;
		for (; i < w; i++, lft++, count -= rows)
		{
			sum -= colSum[lft];
			dst[j * w + i] = (unsigned char) (sum/count);
		}
	}

	// bottom
	for (rows = hr*2; j < h; j++, top++, rows--)
	{
		// init
		for (i = 0; i < w; i++)
			colSum[i] -= src[top * w + i];

		for (i = 0, sum = 0, count = rows; i < wr; i++, count += rows)
			sum += colSum[i];

		// left
		for (i = 0, rgt = wr; i <= wr; i++, rgt++, count += rows)
		{
			sum += colSum[rgt];
			dst[j * w + i] = (unsigned char) (sum/count);
		}
		// middle
		count -= rows;
		for (lft = 0; i < w-wr; i++, lft++, rgt++)
		{
			sum += colSum[rgt] - colSum[lft];
			dst[j * w + i] = (unsigned char) (sum/count);
		}
		// right
		count -= rows;
		for (; i < w; i++, lft++, count -= rows)
		{
			sum -= colSum[lft];
			dst[j * w + i] = (unsigned char) (sum/count);
		}
	}

	delete [] colSum;
}




// static double inline __declspec (naked) __fastcall FastSqrt(double n)
// {
// 	_asm fld qword ptr [esp+4]
// 	_asm fsqrt
// 	_asm ret 8
// } 

void EdgeDetector::ComputeAngle(const int nDx, const int nDy, unsigned char &ucAng)
{
	int nDxAbs = abs(nDx);
	int nDyAbs = abs(nDy);
	int nMaxAbs = max(nDxAbs, nDyAbs);

	int nQuotient = nMaxAbs / 64 + 1;
	nDyAbs /= nQuotient;
	nDxAbs /= nQuotient;
	ucAng = (unsigned char) (g_nArcTan[nDyAbs][nDxAbs]);

	if (nDx > 0 && nDy < 0)
	{
		ucAng = (unsigned char) (UN_ANGLE_2PI - ucAng);
	}
	else if (nDx < 0)
	{
		if (nDy > 0)
			ucAng = UN_ANGLE_PI - ucAng;
		else if (nDy <= 0)
			ucAng = UN_ANGLE_PI + ucAng;
	}
	else
	{
		if (nDy < 0)
			ucAng = UN_ANGLE_PI + ucAng;
		else if (nDy == 0)
			ucAng = 0;
	}
}

int EdgeDetector::GetAngleSin(unsigned char ucAngle)
{
	return g_nAngleSin[ucAngle];
}

int EdgeDetector::GetAngleCos(unsigned char ucAngle)
{
	return g_nAngleCos[ucAngle];
}

// detect edges
// img - input image
// magImg - gradient magnitude
// dirImg - gradient direction
// nLowTh - minimum magnitude of edges
static void ComputeGradientSqrt(unsigned char *pInImg, unsigned char *pMagImg, unsigned char *pDirImg, int nWidth, int nHeight, int nLowTh)
{
	memset(pMagImg, 0, sizeof(unsigned char) * nWidth * nHeight);
	memset(pDirImg, 0, sizeof(unsigned char) * nWidth * nHeight);

	int nLowTh2 = nLowTh * nLowTh * 16;

	for (int j = 1; j < nHeight - 1; j++)
	{
		for (int i = 1; i < nWidth - 1; i++)
		{
			int nOffset = j * nWidth + i;
			// 3x3 Sobel operator
			int nTmp1 = pInImg[nOffset + nWidth + 1] - pInImg[nOffset - nWidth - 1];
			int nTmp2 = pInImg[nOffset - nWidth + 1] - pInImg[nOffset + nWidth - 1];
			int nDx = nTmp1 + nTmp2 + (pInImg[nOffset + 1] - pInImg[nOffset - 1])*2;
			int nDy = nTmp1 - nTmp2 + (pInImg[nOffset + nWidth] - pInImg[nOffset - nWidth])*2;

			int nMag2 = nDx * nDx + nDy * nDy;
			if (nMag2 < nLowTh2)
				continue;

			int nMag = int(sqrt((double)nMag2))>>2;
			if (nMag > 255)
				nMag = 255;
			pMagImg[nOffset] = (unsigned char) (nMag);

			unsigned char ucDir;
			ComputeAngle(nDx, nDy, ucDir);
			pDirImg[nOffset] = ucDir;
		}
	}

	return;
}

static void ComputeGradientAbs(unsigned char *pInImg, unsigned char *pMagImg, unsigned char *pDirImg, int nWidth, int nHeight, int nLowTh)
{
	memset(pMagImg, 0, sizeof(unsigned char) * nWidth * nHeight);
	memset(pDirImg, 0, sizeof(unsigned char) * nWidth * nHeight);

	for (int j = 1; j < nHeight - 1; j++)
	{
		for (int i = 1; i < nWidth - 1; i++)
		{
			int nOffset = j * nWidth + i;
			// 3x3 Sobel operator
			int nTmp1 = pInImg[nOffset + nWidth + 1] - pInImg[nOffset - nWidth - 1];
			int nTmp2 = pInImg[nOffset - nWidth + 1] - pInImg[nOffset + nWidth - 1];
			int nDx = nTmp1 + nTmp2 + (pInImg[nOffset + 1] - pInImg[nOffset - 1])*2;
			int nDy = nTmp1 - nTmp2 + (pInImg[nOffset + nWidth] - pInImg[nOffset - nWidth])*2;

			int nMag = (abs(nDx) + abs(nDy))>>2;
			if (nMag < nLowTh)
				continue;
			else if (nMag > 255)
				nMag = 255;
			pMagImg[nOffset] = (unsigned char) (nMag);

			unsigned char ucDir;
			ComputeAngle(nDx, nDy, ucDir);
			pDirImg[nOffset] = ucDir;
		}
	}

	return;
}

void EdgeDetector::ComputeGradient(unsigned char *pInImg, unsigned char *pMagImg, unsigned char *pDirImg, int nImgWidth, int nImgHeight, int nLowTh, MagType magType /* = kMagSqrt */)
{
	if (magType == kMagSqrt)
		ComputeGradientSqrt(pInImg, pMagImg, pDirImg, nImgWidth, nImgHeight, nLowTh);
	else
		ComputeGradientAbs(pInImg, pMagImg, pDirImg, nImgWidth, nImgHeight, nLowTh);
}


static void ComputeGradientSqrt(unsigned char *pInImg, unsigned char *pMskImg, unsigned char *pMagImg, unsigned char *pDirImg, int nWidth, int nHeight, int nLowTh)
{
	memset(pMagImg, 0, sizeof(unsigned char) * nWidth * nHeight);
	memset(pDirImg, 0, sizeof(unsigned char) * nWidth * nHeight);

	int nLowTh2 = nLowTh * nLowTh * 16;

	for (int j = 1; j < nHeight - 1; j++)
	{
		for (int i = 1; i < nWidth - 1; i++)
		{
			int nOffset = j * nWidth + i;
			if (pMskImg[nOffset] == 0)
				continue;
			// 3x3 Sobel operator
			int nTmp1 = pInImg[nOffset + nWidth + 1] - pInImg[nOffset - nWidth - 1];
			int nTmp2 = pInImg[nOffset - nWidth + 1] - pInImg[nOffset + nWidth - 1];
			int nDx = nTmp1 + nTmp2 + (pInImg[nOffset + 1] - pInImg[nOffset - 1])*2;
			int nDy = nTmp1 - nTmp2 + (pInImg[nOffset + nWidth] - pInImg[nOffset - nWidth])*2;

			int nMag2 = nDx * nDx + nDy * nDy;
			if (nMag2 < nLowTh2)
				continue;

			int nMag = int(sqrt((double)nMag2))>>2;
			if (nMag > 255)
				nMag = 255;
			pMagImg[nOffset] = (unsigned char) (nMag);

			unsigned char ucDir;
			ComputeAngle(nDx, nDy, ucDir);
			pDirImg[nOffset] = ucDir;
		}
	}

	return;
}

static void ComputeGradientAbs(unsigned char *pInImg, unsigned char *pMskImg, unsigned char *pMagImg, unsigned char *pDirImg, int nWidth, int nHeight, int nLowTh)
{
	memset(pMagImg, 0, sizeof(unsigned char) * nWidth * nHeight);
	memset(pDirImg, 0, sizeof(unsigned char) * nWidth * nHeight);

	for (int j = 1; j < nHeight - 1; j++)
	{
		for (int i = 1; i < nWidth - 1; i++)
		{
			int nOffset = j * nWidth + i;
			if (pMskImg[nOffset] == 0)
				continue;

			// 3x3 Sobel operator
			int nTmp1 = pInImg[nOffset + nWidth + 1] - pInImg[nOffset - nWidth - 1];
			int nTmp2 = pInImg[nOffset - nWidth + 1] - pInImg[nOffset + nWidth - 1];
			int nDx = nTmp1 + nTmp2 + (pInImg[nOffset + 1] - pInImg[nOffset - 1])*2;
			int nDy = nTmp1 - nTmp2 + (pInImg[nOffset + nWidth] - pInImg[nOffset - nWidth])*2;

			int nMag = (abs(nDx) + abs(nDy))>>2;
			if (nMag < nLowTh)
				continue;
			else if (nMag > 255)
				nMag = 255;
			pMagImg[nOffset] = (unsigned char) (nMag);

			unsigned char ucDir;
			ComputeAngle(nDx, nDy, ucDir);
			pDirImg[nOffset] = ucDir;
		}
	}

	return;
}
void EdgeDetector::ComputeGradient(unsigned char *pInImg, unsigned char *pMskImg, unsigned char *pMagImg, unsigned char *pDirImg, int nImgWidth, int nImgHeight, int nLowTh, MagType magType /* = kMagSqrt */)
{
	if (magType == kMagSqrt)
		ComputeGradientSqrt(pInImg, pMskImg, pMagImg, pDirImg, nImgWidth, nImgHeight, nLowTh);
	else
		ComputeGradientAbs(pInImg, pMskImg, pMagImg, pDirImg, nImgWidth, nImgHeight, nLowTh);
}


// suppress non maximum
void EdgeDetector::SuppressNonMaximum(unsigned char *pMagImg, unsigned char *pDirImg, unsigned char *pNmsImg, int nWidth, int nHeight)
{
	// init output
	memset(pNmsImg, 0, sizeof(unsigned char) * nWidth * nHeight);

	const int offset[9][2] = {{1, 0}, {1, 1}, {0, 1}, {-1, 1}, {-1, 0}, {-1, -1}, {0, -1}, {1, -1}, {1, 0}};

	int nMag, nDir;
	for (int j = 2; j < nHeight-2; j++)
	{
		for (int i = 2; i < nWidth-2; i++)
		{
			nMag = pMagImg[j*nWidth + i];
			if (nMag == 0)
				continue;
			nDir = pDirImg[j*nWidth + i];
			int nBin = (nDir + 16)/32;

			int nDx = offset[nBin][0];
			int nDy = offset[nBin][1];

			int nX, nY;
			nX = i + nDx;
			nY = j + nDy;
			if (nMag < pMagImg[nY*nWidth + nX])
				continue;
			nX = i - nDx;
			nY = j - nDy;
			if (nMag < pMagImg[nY*nWidth + nX])
				continue;

			pNmsImg[j * nWidth + i] = (unsigned char) (nMag);
		}
	}

	return;
}

// blob tracing
void TraceBlob(unsigned char *pImgIn, unsigned char *pImgMsk, int nWidth, int nHeight, int nSeedX, int nSeedY, int nTh)
{
    if (NULL == pImgIn || NULL == pImgMsk || 0 >= nWidth || 0 >= nHeight
        || 0 > nSeedX || 0 > nSeedY || 0 > nTh)
    {
        return;
    }

	if (pImgIn[nSeedY * nWidth + nSeedX] < nTh)
	{
		pImgMsk[nSeedY * nWidth + nSeedX] = 0;
		return;
	}

	int offset[8][2] = {{1,0},{1,1},{0,1},{-1,1},{-1,0},{-1,-1},{0,-1},{1,-1}};

	// init output
	pImgMsk[nSeedY * nWidth + nSeedX] = 1;

	// init queue
	const int QUEUE_SIZE = 1<<10;
	int queue[QUEUE_SIZE][2];
	int nHead, nTail;
	nHead = 0;
	nTail = 1;
	queue[0][0] = nSeedX;
	queue[0][1] = nSeedY;

	// grow from the seed
	while (nHead != nTail)
	{
		// pick one point from the queue
		int nHeadX, nHeadY;
		nHeadX = queue[nHead][0];
		nHeadY = queue[nHead][1];
		// grow from it
		for (int n = 0; n < 8; n++)
		{
			int nNbrX, nNbrY;
			nNbrX = nHeadX + offset[n][0];
			nNbrY = nHeadY + offset[n][1];
			if (pImgIn[nNbrY * nWidth + nNbrX] < nTh
				|| pImgMsk[nNbrY * nWidth + nNbrX] > 0)
				continue;

			// put it into queue
			queue[nTail][0] = nNbrX;
			queue[nTail][1] = nNbrY;
			nTail = (nTail + 1) & (QUEUE_SIZE - 1);

			// update label field
			pImgMsk[nNbrY * nWidth + nNbrX] = 1;
		}
		nHead = (nHead + 1) & (QUEUE_SIZE - 1);
	}
}


// hysteresis thresholding
void EdgeDetector::HysteresisThreshold(unsigned char *pMagIn, unsigned char *pMagOut, int nWidth, int nHeight, int nLowTh, int nHighTh)
{
	//memset(pMagOut, 0, sizeof(unsigned char) * nWidth * nHeight);
	unsigned char *pImgMsk = new unsigned char[nWidth * nHeight];
	memset(pImgMsk, 0, sizeof(unsigned char) * nWidth * nHeight);

	for (int y = 1; y < nHeight-1; y++)
	{
		for (int x = 1; x < nWidth-1; x++)
		{
			if (pMagIn[y * nWidth + x] < nHighTh)
				continue;

			TraceBlob(pMagIn, pImgMsk, nWidth, nHeight, x, y, nLowTh);
		}
	}

	for (int y = 1; y < nHeight-1; y++)
	{
		for (int x = 1; x < nWidth-1; x++)
		{
			if (pImgMsk[y * nWidth + x] == 0)
				pMagOut[y * nWidth + x] = 0;
			else
				pMagOut[y * nWidth + x] = pMagIn[y * nWidth + x];

		}
	}

	delete [] pImgMsk;
}

// trace bForward
static void TraceEdge(unsigned char *pMagImg, unsigned char *pDirImg, int nWidth, int nHeight, int nX0, int nY0, int nTh, bool bForward, int nDirTol, deque<EdgePoint> &points)
{
	// internal parameters
	//const int nDirTol = 64;

    if (NULL == pMagImg || NULL == pDirImg || 0 >= nWidth || 0 >= nHeight
        || 0 > nX0 || 0 > nY0 || 0 > nTh)
    {
        return;
    }

	if (bForward)
	{
		EdgePoint point;
		point.nX = nX0;
		point.nY = nY0;
		point.nMag = pMagImg[nY0 * nWidth + nX0];
		point.nDir = pDirImg[nY0 * nWidth + nX0];
		if (bForward) 
			points.push_back(point);
		else 
			points.push_front(point);
		pMagImg[nY0 * nWidth + nX0] = 0;
	}

	int offset[8][2] = {{1,0},{1,1},{0,1},{-1,1},{-1,0},{-1,-1},{0,-1},{1,-1}};

	while (true)
	{
		unsigned char dir0 = pDirImg[nY0 * nWidth + nX0];
		unsigned char bin0 = (dir0 + 16)/32;
		bin0 += bForward? 2:6;
		bin0 &= 0x7;

		// create search bin (direction) table
		unsigned char searchBins[5];
		searchBins[0] = bin0;
		searchBins[1] = (bin0 + 1)&0x7;
		searchBins[2] = (bin0 + 7)&0x7;
		searchBins[3] = (bin0 + 2)&0x7;
		searchBins[4] = (bin0 + 6)&0x7;

		int k;
		for (k = 0; k < 5; k++)
		{
			unsigned char bin = searchBins[k];
			int x = nX0 + offset[bin][0];
			int y = nY0 + offset[bin][1];

			unsigned char mag = pMagImg[y * nWidth + x];
			if (mag < nTh)
				continue;
			unsigned char dir = pDirImg[y * nWidth + x];

			// compute difference between two directions
			int nDirDiff = abs(dir0 - dir);
			nDirDiff = min(nDirDiff, 256 - nDirDiff);
			if (nDirDiff <= nDirTol)
			{
				EdgePoint point;
				point.nX = x;
				point.nY = y;
				point.nMag = mag;
				point.nDir = dir;
				if (bForward) 
					points.push_back(point);
				else 
					points.push_front(point);
				pMagImg[y * nWidth + x] = 0;

				nX0 = x;
				nY0 = y;
				break;
			}
		}

		if (k == 5)
			break;
	}
	return;
}



// link edge points to edge chains
// nMag - gradient magnitude after NMS
// nLowEdgeTh - low edge threshold
// nHighTh - high edge threshold
// nMinChainLen - min edge length
// nDir - gradient direction
// chains - found edge chains
void EdgeDetector::LinkEdges(unsigned char *pMagImg, unsigned char *pDirImg, int nWidth, int nHeight, int nLowEdgeTh, int nHighEdgeTh, int nMinChainLen, int nDirTol, vector<EdgeChain> &chains)
{
	// init output
	chains.clear();

	unsigned char *pMagTmp = new unsigned char[nWidth * nHeight];
	memcpy(pMagTmp, pMagImg, sizeof(unsigned char) * nWidth * nHeight);

	deque<EdgePoint> points;
	EdgeChain chain;

	for (int y = 1; y < nHeight - 1; y++)
	{
		for (int x = 1; x < nWidth - 1; x++)
		{
			if (pMagTmp[y * nWidth + x] < nHighEdgeTh)
				continue;

			points.clear();
			TraceEdge(pMagTmp, pDirImg, nWidth, nHeight, x, y, nLowEdgeTh, true, nDirTol, points);
			TraceEdge(pMagTmp, pDirImg, nWidth, nHeight, x, y, nLowEdgeTh, false, nDirTol, points);

			if (points.size() < nMinChainLen)
				continue;

			chain.points.clear();
			while (!points.empty())
			{
				EdgePoint &point = points.front();
				chain.points.push_back(point);
				points.pop_front();
			}
			chains.push_back(chain);
		}
	}

	delete [] pMagTmp;
}


void EdgeDetector::DumpEdgeChains(unsigned char *pImg, int nWidth, int nHeight, vector<EdgeChain> &chains)
{
	memset(pImg, 0, sizeof(unsigned char) * nWidth * nHeight);
	for (int i = 0; i < chains.size(); i++)
	{
		EdgeChain &chain = chains[i];
		int edgeLbl = rand()%230 + 20;
		for (int j = 0; j < chain.points.size(); j++)
		{
			EdgePoint &point = chain.points[j];
			pImg[point.nY * nWidth + point.nX] = (unsigned char) (edgeLbl);
		}
	}
}

// LUT for computing sub-pixel edge positions
//
// suppose coordinate origin is at the top-left corner
// segment numbering (clock-wise):
//    5   6
//4           7
//3           0
//    2   1
// segment  coeff
//  0       1-tan(t)
//  1       tan(pi/2-t)
//  2       1-tan(pi-t)
//  3       tan(pi-t)

// offset = {{1,0},{1,1},{0,1},{-1,1},{-1,0},{-1,-1},{0,-1},{1,-1}}

// segment cell     interpolated value
//  0       0-1     coeff*cell(0)+(1-coeff)*cell(1)
//  1       1-2     coeff*cell(1)+(1-coeff)*cell(2)
//  2       2-3     ...
//  3       3-4     ...
//  4       4-5     ...
//  5       5-6     ...
//  6       6-7     ...
//  7       7-0     coeff*cell(7)+(1-coeff)*cell(0)
class SubEdgeLut
{
public:
	SubEdgeLut()
	{
		m_coeffs.resize(UN_ANGLE_PI);
		m_segments.resize(UN_ANGLE_PI);
	}

	void init()
	{
		int i;
		for (i = 0; i < UN_ANGLE_PI2/2; i++)
		{
			m_segments[i] = 0;
			m_coeffs[i] = 65536 - g_nAngleTan[i];
		}

		for (i = UN_ANGLE_PI2/2; i < UN_ANGLE_PI2; i++)
		{
			m_segments[i] = 1;
			m_coeffs[i] = g_nAngleTan[UN_ANGLE_PI2 - i];
		}

		for (i = UN_ANGLE_PI2; i < UN_ANGLE_PI2/2*3; i++)
		{
			m_segments[i] = 2;
			m_coeffs[i] = 65536 - g_nAngleTan[i-UN_ANGLE_PI2];
		}

		for (i = UN_ANGLE_PI2/2*3; i < UN_ANGLE_PI; i++)
		{
			m_segments[i] = 3;
			m_coeffs[i] = g_nAngleTan[UN_ANGLE_PI-i];
		}
	}
	void lookup(int nDir, int &nSegment, int &nCoeff)
	{
		if (nDir >= UN_ANGLE_PI)
			nDir -= UN_ANGLE_PI;

		nSegment = m_segments[nDir];
		nCoeff = m_coeffs[nDir];
	}
private:
	vector<int> m_coeffs; // coefficient for linear interpolation
	vector<int> m_segments; // start position 
};

void EdgeDetector::GetSubpixelEdge(unsigned char *pMagImg, unsigned char *pDirImg, int nWidth, int nHeight, vector<EdgeChain> &chains)
{
	SubEdgeLut subEdgeLut;
	subEdgeLut.init();

	int offset[8][2] = {{1,0},{1,1},{0,1},{-1,1},{-1,0},{-1,-1},{0,-1},{1,-1}};

	for (int i = 0; i < chains.size(); i++)
	{
		EdgeChain &chain = chains[i];
		for (int j = 0; j < chain.points.size(); j++)
		{
			EdgePoint &point = chain.points[j];
			point.fSubX = (float)point.nX;
			point.fSubY = (float)point.nY;

			if (point.nX <= 0 || point.nX >= nWidth - 1 
				|| point.nY <= 0 || point.nY >= nHeight - 1)
				continue;

			int nMag, nDir;
			nMag = pMagImg[point.nY * nWidth + point.nX];
			if (nMag == 0)
				continue;
			nDir = pDirImg[point.nY * nWidth + point.nX];
			int nSegment;
			int nCoeff;
			subEdgeLut.lookup(nDir, nSegment, nCoeff);
			int nMag1, nMag2;
			int nX, nY;
			int nCell;
			nCell = nSegment;
			nX = point.nX + offset[nCell][0];
			nY = point.nY + offset[nCell][1];
			nMag1 = pMagImg[nY * nWidth + nX];
			nCell = (nSegment+1)&7;
			nX = point.nX + offset[nCell][0];
			nY = point.nY + offset[nCell][1];
			nMag2 = pMagImg[nY * nWidth + nX];
			int nMagA = (nMag1*nCoeff + nMag2*(65536-nCoeff))>>16;

			nCell = (nSegment+4)&7;
			nX = point.nX + offset[nCell][0];
			nY = point.nY + offset[nCell][1];
			nMag1 = pMagImg[nY * nWidth + nX];
			nCell = (nCell+1)&7;
			nX = point.nX + offset[nCell][0];
			nY = point.nY + offset[nCell][1];
			nMag2 = pMagImg[nY * nWidth + nX];
			int nMagB = (nMag1*nCoeff + nMag2*(65536-nCoeff))>>16;

			if (nMag < nMagA)
				nMag = nMagA;
			if (nMag < nMagB)
				nMag = nMagB;

			if (2*nMag == nMagA + nMagB)
				continue;

			float fDelta = 0.5f * (nMagB - nMagA)/(2*nMag - nMagA - nMagB);
			float fDx, fDy;

			if (nDir >= UN_ANGLE_PI)
				nDir -= UN_ANGLE_PI;

			fDelta *= 1.0f/65536;

			if (nSegment == 0)
			{
				fDx = -fDelta * g_nAngleCos[nDir];
				fDy = -fDelta * g_nAngleSin[nDir];
			}
			else if (nSegment == 1)
			{
				fDx = -fDelta * g_nAngleSin[UN_ANGLE_PI2 - nDir];
				fDy = -fDelta * g_nAngleCos[UN_ANGLE_PI2 - nDir];
			}
			else if (nSegment == 2)
			{
				fDx = fDelta * g_nAngleSin[nDir - UN_ANGLE_PI2];
				fDy = -fDelta * g_nAngleCos[nDir - UN_ANGLE_PI2];
			}
			else if (nSegment == 3)
			{
				fDx = fDelta * g_nAngleCos[UN_ANGLE_PI - nDir];
				fDy = -fDelta * g_nAngleSin[UN_ANGLE_PI - nDir];
			}

			assert(fabs(fDx) <= 0.51f && fabs(fDy) <= 0.51f);

			point.fSubX += fDx;
			point.fSubY += fDy;
		}
	}
}

void EdgeDetector::GetSubpixelEdge(unsigned char *pMagImg, unsigned char *pDirImg, int nWidth, int nHeight, vector<EdgePoint> &points)
{
	SubEdgeLut subEdgeLut;
	subEdgeLut.init();

	int offset[8][2] = {{1,0},{1,1},{0,1},{-1,1},{-1,0},{-1,-1},{0,-1},{1,-1}};


	for (int j = 0; j < points.size(); j++)
	{
		EdgePoint &point = points[j];
		point.fSubX = (float)point.nX;
		point.fSubY = (float)point.nY;

		if (point.nX <= 0 || point.nX >= nWidth - 1 
			|| point.nY <= 0 || point.nY >= nHeight - 1)
			continue;

		int nMag, nDir;
		nMag = pMagImg[point.nY * nWidth + point.nX];
		if (nMag == 0)
			continue;
		nDir = pDirImg[point.nY * nWidth + point.nX];
		int nSegment;
		int nCoeff;
		subEdgeLut.lookup(nDir, nSegment, nCoeff);
		int nMag1, nMag2;
		int nX, nY;
		int nCell;
		nCell = nSegment;
		nX = point.nX + offset[nCell][0];
		nY = point.nY + offset[nCell][1];
		nMag1 = pMagImg[nY * nWidth + nX];
		nCell = (nSegment+1)&7;
		nX = point.nX + offset[nCell][0];
		nY = point.nY + offset[nCell][1];
		nMag2 = pMagImg[nY * nWidth + nX];
		int nMagA = (nMag1*nCoeff + nMag2*(65536-nCoeff))>>16;

		nCell = (nSegment+4)&7;
		nX = point.nX + offset[nCell][0];
		nY = point.nY + offset[nCell][1];
		nMag1 = pMagImg[nY * nWidth + nX];
		nCell = (nCell+1)&7;
		nX = point.nX + offset[nCell][0];
		nY = point.nY + offset[nCell][1];
		nMag2 = pMagImg[nY * nWidth + nX];
		int nMagB = (nMag1*nCoeff + nMag2*(65536-nCoeff))>>16;

		if (nMag < nMagA)
			nMag = nMagA;
		if (nMag < nMagB)
			nMag = nMagB;

		if (2*nMag == nMagA + nMagB)
			continue;

		float fDelta = 0.5f * (nMagB - nMagA)/(2*nMag - nMagA - nMagB);
		float fDx, fDy;

		if (nDir >= UN_ANGLE_PI)
			nDir -= UN_ANGLE_PI;

		fDelta *= 1.0f/65536;

		if (nSegment == 0)
		{
			fDx = -fDelta * g_nAngleCos[nDir];
			fDy = -fDelta * g_nAngleSin[nDir];
		}
		else if (nSegment == 1)
		{
			fDx = -fDelta * g_nAngleSin[UN_ANGLE_PI2 - nDir];
			fDy = -fDelta * g_nAngleCos[UN_ANGLE_PI2 - nDir];
		}
		else if (nSegment == 2)
		{
			fDx = fDelta * g_nAngleSin[nDir - UN_ANGLE_PI2];
			fDy = -fDelta * g_nAngleCos[nDir - UN_ANGLE_PI2];
		}
		else if (nSegment == 3)
		{
			fDx = fDelta * g_nAngleCos[UN_ANGLE_PI - nDir];
			fDy = -fDelta * g_nAngleSin[UN_ANGLE_PI - nDir];
		}

		assert(fabs(fDx) <= 0.51f && fabs(fDy) <= 0.51f);

		point.fSubX += fDx;
		point.fSubY += fDy;
	}
}

void EdgeDetector::GetSubpixelEdge(unsigned char *pMagImg, int nImgWidth, int nImgHeight, int nX, int nY, float &fX, float &fY )
{
    if (NULL == pMagImg || 0 >= nImgWidth || 0 >= nImgHeight
        || 0 > nX || 0 > nY)
    {
        return;
    }

	fX = float(nX);
	fY = float(nY);

	float m[9];
	int nOffset = nY * nImgWidth + nX;
	m[0] = float(pMagImg[nOffset - nImgWidth - 1]);
	m[1] = float(pMagImg[nOffset - nImgWidth]);
	m[2] = float(pMagImg[nOffset - nImgWidth + 1]);
	m[3] = float(pMagImg[nOffset - 1]);
	m[4] = float(pMagImg[nOffset]);
	m[5] = float(pMagImg[nOffset + 1]);
	m[6] = float(pMagImg[nOffset + nImgWidth - 1]);
	m[7] = float(pMagImg[nOffset + nImgWidth]);
	m[8] = float(pMagImg[nOffset + nImgWidth + 1]);

	float k1, k2, k3, k4, k5;
	k1 = (m[2] + m[5] + m[8] - m[0] - m[3] - m[6])*2;
	k2 = (m[6] + m[7] + m[8] - m[0] - m[1] - m[2])*2;
	k3 = (m[2] + m[5] + m[8] + m[0] + m[3] + m[6] - (m[1] + m[4] + m[7])*2)*2;
	k4 = (m[0] + m[8] - m[2] - m[6])*3;
	k5 = (m[6] + m[7] + m[8] + m[0] + m[1] + m[2] - (m[3] + m[4] + m[5])*2)*2;

	float d = 4*k3*k5 - k4*k4;
	if (d != 0)
	{
		fX += -(-k2*k4+2*k5*k1)/d;
		fY += (k4*k1-2*k3*k2)/d;
	}
}


#if 0
inline void FindMaxEigen(float H[2][2], float &fMaxEigVal, float &fMaxEigVecX, float &fMaxEigVecY)
{
	MatDoub a(2, 2);
	a[0][0] = H[0][0];
	a[0][1] = H[0][1];
	a[1][0] = H[1][0];
	a[1][1] = H[1][1];

	Jacobi jac(a);

	if (fabs(jac.d[0]) > fabs(jac.d[1]))
	{
		fMaxEigVal = jac.d[0];
		fMaxEigVecX = jac.v[0][0];
		fMaxEigVecY = jac.v[1][0];
	}
	else
	{
		fMaxEigVal = jac.d[1];
		fMaxEigVecX = jac.v[0][1];
		fMaxEigVecY = jac.v[1][1];
	}
}
#endif

// break edge chain to linear sub-chains
// points - chain points
// nStartIndex - start point index of sub-chain
// nEndIndex - end point index of sub-chain
// nMinLength - min line length (number of points)
// terminations - lines' start and end index
void EdgeDetector::BreakChain2Lines(vector<EdgePoint> &points, int nStartIndex, int nEndIndex, int nMinLength, vector<int> &terminations)
{
	assert(nStartIndex >= 0 && nEndIndex < points.size());

	if (nEndIndex - nStartIndex + 1 < nMinLength)
		return;

	float fX0, fY0; // start point
	float fX1, fY1; // end point
	fX0 = points[nStartIndex].fSubX;
	fY0 = points[nStartIndex].fSubY;
	fX1 = points[nEndIndex].fSubX;
	fY1 = points[nEndIndex].fSubY;
	float fDx, fDy;
	fDx = fX0 - fX1;
	fDy = fY0 - fY1;
	float fStartEndDist = sqrt(fDx*fDx + fDy*fDy);

	float fDist, fMaxDist;
	int nMaxDistI = 0, nMaxDistJ = 0;

	if (fStartEndDist < 3)
	{
		// find maximum distance between any two points
		fMaxDist = 0;
		for (int i = nStartIndex; i < nEndIndex; i++)
		{
			EdgePoint &point1 = points[i];
			for (int j = i + 1; j <= nEndIndex; j++)
			{
				EdgePoint &point2 = points[j];
				fDx = point1.fSubX - point2.fSubX;
				fDy = point1.fSubY - point2.fSubY;
				fDist = fDx*fDx + fDy*fDy;
				if (fDist > fMaxDist)
				{
					fMaxDist = fDist;
					nMaxDistI = i;
					nMaxDistJ = j;
				}
			}
		}

		if (fMaxDist < nMinLength * nMinLength)
			return;

		if (nMaxDistI != nStartIndex)
			BreakChain2Lines(points, nStartIndex, nMaxDistI - 1, nMinLength, terminations);
		if (nMaxDistJ != nEndIndex)
			BreakChain2Lines(points, nMaxDistJ + 1, nEndIndex, nMinLength, terminations);

		BreakChain2Lines(points, nMaxDistI, nMaxDistJ, nMinLength, terminations);

		return;
	}

	fMaxDist = 0;
	for (int i = nStartIndex; i <= nEndIndex; i++)
	{
		EdgePoint &point = points[i];
		float fX, fY;
		fX = point.fSubX;
		fY = point.fSubY;
		fDist = fabs((fX - fX0)*(fY0 - fY1) - (fY - fY0)*(fX0 - fX1));
		if (fDist > fMaxDist)
		{
			fMaxDist = fDist;
			nMaxDistI = i;
		}
	}

	float t1, t2;
	fDx = fX0 - fX1;
	fDy = fY0 - fY1;
	float fStartEndDist2 = fStartEndDist * fStartEndDist;
	t1 = t2 = fStartEndDist2 + (fDx + fDy) * (fDx + fDy)/fStartEndDist2;
	t1 = fabs(fDy + fDx) * fabs(t1 - (fDx + fDy));
	t2 = fabs(fDy - fDx) * fabs(t2 + (fDx + fDy));
	float fDistTh = max(t1, t2)/fStartEndDist2;

	if (fMaxDist > fDistTh)
	{
		BreakChain2Lines(points, nStartIndex, nMaxDistI, nMinLength, terminations);
		BreakChain2Lines(points, nMaxDistI + 1, nEndIndex, nMinLength, terminations);
	}
	else
	{
		terminations.push_back(nStartIndex);
		terminations.push_back(nEndIndex);
	}

	return;
}

// compute angle at point (fX[1], fY[1])
static inline float ComputePointAngle(float fX[3], float fY[3])
{
	float fDx1 = fX[0] - fX[1];
	float fDy1 = fY[0] - fY[1];
	float fDx2 = fX[2] - fX[1];
	float fDy2 = fY[2] - fY[1];

	float fNorm = fDx1*fDx1 + fDy1*fDy1;
	fNorm *= fDx2*fDx2 + fDy2*fDy2;
	fNorm = (float)sqrt(double(fNorm));

	assert(fNorm > 0.0001f);

	float fAngle = acos((fDx1*fDx2 + fDy1*fDy2)/fNorm);

	return fAngle;
}

#if 0
// line fitting
// ref - computer and robot vision, vol 1., p588
// NOTE: not finished yet
int EdgeDetector::FitLineLS(float *pX, float *pY, int n, float &a, float &b, float &c)
{
	double x, y, xSum, xxSum, ySum, yySum, xySum;
	xSum = xxSum = ySum = yySum = xySum = 0;
	for (int i = 0; i < n; i++)
	{
		x = double(pX[i]);
		y = double(pY[i]);
		xSum += x;
		ySum += y;
		xxSum += x*x;
		yySum += y*y;
		xySum += x*y;
	}

	double uxx, uxy, uyy;
	uxx = xxSum - xSum*xSum/n;
	uyy = yySum - ySum*ySum/n;
	uxy = xySum - xSum*ySum/n;

	double lambda = uxx + uyy - sqrt((uxx - uyy)*(uxx - uyy) + 4*uxy*uxy);
	lambda *= 0.5;
	double tmp = sqrt(uxy*uxy + (lambda-uxx)*(lambda-uxx));
	if (fabs(tmp) < 0.00001)
		return -1;
	tmp = 1/tmp;
	a = uxy*tmp;
	b = (lambda - uxx)*tmp;
	c = -(a*xSum + b*ySum)/n;
	return 0;
}

#else

// points[2*n] - x
// points[2*n+1] - y
bool EdgeDetector::FitLineLS(vector<float> &points, float &a, float &b, float &c)
{
	int nNumPoints = points.size()/2;

	if (nNumPoints < 2)
		return false;

	float fX = 0.0f, fY = 0.0f, fX2 = 0.0f, fY2 = 0.0f, fXY = 0.0f; 
	int i = 0; 
	for (i = 0; i < points.size() - 1; i += 2)
	{
		float x = points[i];
		float y = points[i+1];
		fX += x; 
		fY += y; 
		fX2 += x * y; 
		fY2 += y * y; 
		fXY += x * y; 
	}

	const float fXSum2 = fX * fX, fXSumYSum = fX * fY, fYSum2 = fY * fY; 
	const float fC11 = nNumPoints * fX2 - fXSum2, fC12 = nNumPoints * fXY - fXSumYSum; 
	const float fC21 = fC12, fC22 = nNumPoints * fY2 - fYSum2; 

	if (fabs(fC11) > fabs(fC22)) // y = kx + b 
	{
		a = fC12 / fC11; 
		b = -1.0f; 
		c = (fY - a * fX) / nNumPoints; 
	}
	else // x = ky + b 
	{
		a = -1.0f; 
		b = fC21 / fC22; 
		c = (fX - b * fY) / nNumPoints; 
	}

	return true;
}

#endif

static void Swap(int &a, int &b)
{
	int t = a;
	a = b;
	b = t;
}

void EdgeDetector::BresenhamLine(unsigned char *pImg, int w, int h, int x0, int y0, int x1, int y1, unsigned char label)
{
	if ((unsigned)x0 >= (unsigned)w || (unsigned)x1 >= (unsigned)w
		|| (unsigned)y0 >= (unsigned)h || (unsigned)y1 >= (unsigned)h)
		return;


	bool steep = abs(y1 - y0) > abs(x1 - x0);
	if (steep) {
		Swap(x0, y0);
		Swap(x1, y1);
	}
	if (x0 > x1) {
		Swap(x0, x1);
		Swap(y0, y1);
	}
	int deltax = x1 - x0;
	int deltay = abs(y1 - y0);
	int error = deltax / 2;
	int ystep;
	int y = y0;
	if (y0 < y1)
		ystep = 1;
	else
		ystep = -1;
	for (int x = x0; x <= x1; x++) {
		if (steep)
			pImg[x * w + y] = label;
		else
			pImg[y * w + x] = label;
		error -= deltay;
		if (error < 0) {
			y += ystep;
			error += deltax;
		}
	}
}

// static int compare(const void *a, const void *b)
// {
// 	float *a1 = (float *)a;
// 	float *b1 = (float *)b;
// 	if (*a1 > *b1)
// 		return 1;
// 	else if (*a1 < *b1)
// 		return -1;
// 	else
// 		return 0;
// }

#if 0
float EdgeDetector::EstimateImageNoiseVariance(unsigned char *img, int w, int h)
{
	if (w < 3 || h < 3)
		return 0;

	const float cste = 1.0f/sqrt(20.0f);

	float *buf = new float[w * h];
	int count = 0;
	for (int y = 1; y < h - 1; y++)
	{
		for (int x = 1; x < w - 1; x++)
		{
			int offset = y * w + x;
			float val = cste * (img[offset - w] + img[offset + w] + img[offset - 1] + img[offset + 1] - 4 * img[offset]);
			buf[count++] = val;
		}
	}
	qsort(buf, count, sizeof(float), compare);

	float med = buf[count>>1];

	for (int i = 0; i < count; i++)
	{
		buf[i] = fabs(buf[i] - med);
	}

	qsort(buf, count, sizeof(float), compare);

	const float sig = 1.4828f*buf[count>>1];
	return sig*sig;
}

#endif

int EdgeDetector::GetZoomSize(int srcSize, float scale)
{
	return int((srcSize-1) * scale);
}

void EdgeDetector::ZoomImage(unsigned char *srcImg, int srcWidth, int srcHeight, unsigned char *dstImg, int dstWidth, int dstHeight)
{
	float scaleWidth = float(dstWidth)/(srcWidth-1);
	float scaleHeight = float(dstHeight)/(srcHeight-1);

	int dstOffset = 0;

	float sy = 0;
	const float SCALE_WIDTH_INV = 1/scaleWidth;
	const float SCALE_HEIGHT_INV = 1/scaleHeight;
	for (int y = 0; y < dstHeight; y++, sy += SCALE_HEIGHT_INV)
	{
		int syi = int(sy);
		float b = sy - syi;
		float sx = 0;
		for (int x = 0; x < dstWidth; x++, dstOffset++, sx += SCALE_WIDTH_INV)
		{
			int sxi = int(sx);
			float a = sx - sxi;
			int srcOffset = srcWidth * syi + sxi;
			int interp = int((1-a)*(1-b)*srcImg[srcOffset] + a*(1-b)*srcImg[srcOffset+1] + (1-a)*b*srcImg[srcOffset+srcWidth] + a*b*srcImg[srcOffset+srcWidth+1] + 0.5f);
			if (interp < 0)
				interp = 0;
			else if (interp > 255)
				interp = 255;

			dstImg[dstOffset] = (unsigned char) (interp);
		}
	}
}

void EdgeDetector::ZoomImage(unsigned char *srcImg, int srcWidth, int srcHeight, unsigned char *dstImg, float scaleWidth, float scaleHeight)
{
	int dstWidth = int((srcWidth-1) * scaleWidth);
	int dstHeight = int((srcHeight-1) * scaleHeight);

	int dstOffset = 0;

	float sy = 0;
	const float SCALE_WIDTH_INV = 1/scaleWidth;
	const float SCALE_HEIGHT_INV = 1/scaleHeight;
	for (int y = 0; y < dstHeight; y++, sy += SCALE_HEIGHT_INV)
	{
		int syi = int(sy);
		float b = sy - syi;
		float sx = 0;
		for (int x = 0; x < dstWidth; x++, dstOffset++, sx += SCALE_WIDTH_INV)
		{
			int sxi = int(sx);
			float a = sx - sxi;
			int srcOffset = srcWidth * syi + sxi;
			int interp = int((1-a)*(1-b)*srcImg[srcOffset] + a*(1-b)*srcImg[srcOffset+1] + (1-a)*b*srcImg[srcOffset+srcWidth] + a*b*srcImg[srcOffset+srcWidth+1] + 0.5f);
			if (interp < 0)
				interp = 0;
			else if (interp > 255)
				interp = 255;

			dstImg[dstOffset] = (unsigned char) (interp);
		}
	}
}



static void SetPixel(unsigned char *img, int w, int h, int x, int y, unsigned char label)
{
	if (unsigned(x) >= (unsigned)w || unsigned(y) >= (unsigned)h)
		return;

	img[w * y + x] = label;
}

void EdgeDetector::BresenhamCircle(unsigned char *img, int w, int h, int cx, int cy, int r, unsigned char label)                                                     
{                                                                                                                        
	int x = -r, y = 0, err = 2-2*r;        /* II. Quadrant */                   
	do {                                                                                                                  
		SetPixel(img, w, h, cx-x, cy+y, label);              /*   I. Quadrant */            
		SetPixel(img, w, h, cx-y, cy-x, label);              /*  II. Quadrant */            
		SetPixel(img, w, h, cx+x, cy-y, label);              /* III. Quadrant */            
		SetPixel(img, w, h, cx+y, cy+x, label);              /*  IV. Quadrant */             
		r = err;                                                                                                        
		if (r >  x) err += ++x*2+1;      /* e_xy+e_x > 0 */                   
		if (r <= y) err += ++y*2+1;      /* e_xy+e_y < 0 */                    
	} while (x < 0);                                                                                             
} 

