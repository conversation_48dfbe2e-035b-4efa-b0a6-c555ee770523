/**
* @date         2012-06-21
* @filename     AlgoInterface.cpp
* @purpose      interface and base class for image processing algorithms
* @version      1.0
* @history      initial draft, improved 2014-06~2014-07
* <AUTHOR> UNIC Tech, Beijing, China
* @copyright    UNIC Technologies, 2005-2015. All rights reserved.
*/

#include "./AlgoInterface.h"

/**
* @method       CAlgoInterface::CAlgoInterface
* @access       public
* @brief        construct function
* @param        void
* @return
* <AUTHOR> <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
CAlgoInterface::CAlgoInterface(void)
{
    // create objects
    m_pDisp = mnew CParaPack();
    m_pResult = mnew CParaPack();
    m_pPara = mnew CParaPack();
    m_pRoi = mnew CROI();
}

/**
* @method       CAlgoInterface::~CAlgoInterface
* @access       public
* @brief        destruct function
* @param        void
* @return
* <AUTHOR> Morgan.Le<PERSON>@unic-tech.com
* @date         2012-06-25
* @history      initial draft
*/
CAlgoInterface::~CAlgoInterface(void)
{
    if (NULL != m_pDisp)
    {
        delete m_pDisp; //lint !e1551
        m_pDisp = NULL;
    }

    if (NULL != m_pResult)
    {
        delete m_pResult; //lint !e1551
        m_pResult = NULL;
    }

    if (NULL != m_pPara)
    {
        delete m_pPara; //lint !e1551
        m_pPara = NULL;
    }

    if (NULL != m_pRoi)
    {
        delete m_pRoi; //lint !e1551
        m_pRoi = NULL;
    }
} //lint !e1740

/**
* @method       CAlgoInterface::StartUnpackPara
* @access       public
* @brief        start unpacking parameters
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::StartUnpackPara(void)
{
    if (NULL == m_pPara)
    {
        return 1;
    }

    return m_pPara->StartUnpack();
}

/**
* @method       CAlgoInterface::GetNextGroupPara
* @access       public
* @brief        get the next group of parameters
* @param        char * pcName
* @param        int & nElem
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::GetNextGroupPara(char *pcName, int &nElem)
{
    if (NULL == m_pPara)
    {
        return 1;
    }

    return m_pPara->GetNextGroup(pcName, nElem);
}

/**
* @method       CAlgoInterface::GetNextElemInfoPara
* @access       public
* @brief        get information of the next element of parameters
* @param        UN_TYPE_MACRO_E & eType
* @param        char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::GetNextElemInfoPara(UN_TYPE_MACRO_E &eType, char *pcName)
{
    if (NULL == m_pPara)
    {
        return 1;
    }

    return m_pPara->GetNextElemInfo(eType, pcName);
}

/**
* @method       CAlgoInterface::GetNextElemDataPara
* @access       public
* @brief        get data of the next element of parameters
* @param        void * pData
* @param        const int nLength
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::GetNextElemDataPara(void *pData, const int nLength)
{
    if (NULL == m_pPara)
    {
        return 1;
    }

    return m_pPara->GetNextElemData(pData, nLength);
}

/**
* @method       CAlgoInterface::SetValuePara
* @access       public
* @brief        set parameter's value according group name and element name
* @param        const char * pcGroupName
* @param        const char * pcElemName
* @param        const void * pData
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::SetValuePara(const char *pcGroupName,
                                 const char *pcElemName, const void *pData)
{
    if (NULL == m_pPara)
    {
        return 1;
    }

    return m_pPara->SetValue(pcGroupName, pcElemName, pData);
}

/**
* @method       CAlgoInterface::GetValueInfo
* @access       public
* @brief        get type information using group name and element name
* @param        const char * pcGroupName
* @param        const char * pcElemName
* @param        UN_TYPE_MACRO_E & eType
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-26
* @history      initial draft
*/
int CAlgoInterface::GetValueInfo(const char *pcGroupName, const char *pcElemName,
                                 UN_TYPE_MACRO_E &eType)
{
    if (NULL == m_pPara)
    {
        return 1;
    }

    return m_pPara->GetInfo(pcGroupName, pcElemName, eType);
}

/**
* @method       CAlgoInterface::StartUnpackResult
* @access       public
* @brief        start unpacking result
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::StartUnpackResult(void)
{
    if (NULL == m_pResult)
    {
        return 1;
    }

    return m_pResult->StartUnpack();
}

/**
* @method       CAlgoInterface::GetNextGroupResult
* @access       public
* @brief        get the next group of results
* @param        char * pcName
* @param        int & nElem
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::GetNextGroupResult(char *pcName, int &nElem)
{
    if (NULL == m_pResult)
    {
        return 1;
    }

    return m_pResult->GetNextGroup(pcName, nElem);
}

/**
* @method       CAlgoInterface::GetNextElemInfoResult
* @access       public
* @brief        get information of the next element of results
* @param        UN_TYPE_MACRO_E & eType
* @param        char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::GetNextElemInfoResult(UN_TYPE_MACRO_E &eType, char *pcName)
{
    if (NULL == m_pResult)
    {
        return 1;
    }

    return m_pResult->GetNextElemInfo(eType, pcName);
}

/**
* @method       CAlgoInterface::GetNextElemDataResult
* @access       public
* @brief        get data of the next element of results
* @param        void * pData
* @param        const int nLength
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::GetNextElemDataResult(void *pData, const int nLength)
{
    if (NULL == m_pResult)
    {
        return 1;
    }

    return m_pResult->GetNextElemData(pData, nLength);
}

/**
* @method       CAlgoInterface::StartUnpackDisp
* @access       public
* @brief        start unpacking displays
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::StartUnpackDisp(void)
{
    if (NULL == m_pDisp)
    {
        return 1;
    }

    return m_pDisp->StartUnpack();
}

/**
* @method       CAlgoInterface::GetNextGroupDisp
* @access       public
* @brief        get the next group of displays
* @param        char * pcName
* @param        int & nElem
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::GetNextGroupDisp(char *pcName, int &nElem)
{
    if (NULL == m_pDisp)
    {
        return 1;
    }

    return m_pDisp->GetNextGroup(pcName, nElem);
}

/**
* @method       CAlgoInterface::GetNextElemInfoDisp
* @access       public
* @brief        get information of the next element of displays
* @param        UN_TYPE_MACRO_E & eType
* @param        char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::GetNextElemInfoDisp(UN_TYPE_MACRO_E &eType, char *pcName)
{
    if (NULL == m_pDisp)
    {
        return 1;
    }

    return m_pDisp->GetNextElemInfo(eType, pcName);
}

/**
* @method       CAlgoInterface::GetNextElemDataDisp
* @access       public
* @brief        get data of the next element of displays
* @param        void * pData
* @param        const int nLength
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::GetNextElemDataDisp(void *pData, const int nLength)
{
    if (NULL == m_pDisp)
    {
        return 1;
    }

    return m_pDisp->GetNextElemData(pData, nLength);
}

/**
* @method       CAlgoInterface::GetValuePara
* @access       protected
* @brief        get value of a parameter according group name and element name
* @param        const char * pcGroupName
* @param        const char * pcElemName
* @param        void * pData
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::GetValuePara(const char *pcGroupName, const char *pcElemName, void *pData)
{
    if (NULL == m_pPara)
    {
        return 1;
    }

    return m_pPara->GetValue(pcGroupName, pcElemName, pData);
}

/**
* @method       CAlgoInterface::StartPackPara
* @access       protected
* @brief        start packing parameters
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::StartPackPara(void)
{
    if (NULL == m_pPara)
    {
        return 1;
    }

    return m_pPara->StartPack();
}

/**
* @method       CAlgoInterface::AddGroupPara
* @access       protected
* @brief        add a group of parameters
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::AddGroupPara(const char *pcName)
{
    if (NULL == m_pPara)
    {
        return 1;
    }

    return m_pPara->AddGroup(pcName);
}

/**
* @method       CAlgoInterface::AddElemPara
* @access       protected
* @brief        add edit for ui
* @param        const UI_EDIT_S & stEdit
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemPara(const UI_EDIT_S &stEdit, const char *pcName)
{
    return AddElemPara(&stEdit, sizeof(stEdit), UI_EDIT, pcName);
}

/**
* @method       CAlgoInterface::AddElemPara
* @access       protected
* @brief        add edit for ui
* @param        const UI_EDIT_F_S & stEdit
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemPara(const UI_EDIT_F_S &stEdit, const char *pcName)
{
    return AddElemPara(&stEdit, sizeof(stEdit), UI_EDIT_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemPara
* @access       protected
* @brief        add check for ui
* @param        const UI_CHECK_S & stCheck
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemPara(const UI_CHECK_S &stCheck, const char *pcName)
{
    return AddElemPara(&stCheck, sizeof(stCheck), UI_CHECK, pcName);
}

/**
* @method       CAlgoInterface::AddElemPara
* @access       protected
* @brief        add radio for ui
* @param        const UI_RADIO_S & stRadio
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemPara(const UI_RADIO_S &stRadio, const char *pcName)
{
    return AddElemPara(&stRadio, sizeof(stRadio), UI_RADIO, pcName);
}

/**
* @method       CAlgoInterface::AddElemPara
* @access       protected
* @brief        add image selection for ui
* @param        const UI_IMAGE_S & stImage
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemPara(const UI_IMAGE_S &stImage, const char *pcName)
{
    return AddElemPara(&stImage, sizeof(stImage), UI_IMAGE, pcName);
}

/**
* @method       CAlgoInterface::AddElemPara
* @access       protected
* @brief        add file selection for ui
* @param        const UI_FILE_S & stFile
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemPara(const UI_FILE_S &stFile, const char *pcName)
{
    return AddElemPara(&stFile, sizeof(stFile), UI_FILE, pcName);
}

/**
* @method       CAlgoInterface::AddElemPara
* @access       protected
* @brief        add folder selection for ui
* @param        const UI_FOLDER_S & stFolder
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemPara(const UI_FOLDER_S &stFolder, const char *pcName)
{
    return AddElemPara(&stFolder, sizeof(stFolder), UI_FOLDER, pcName);
}

/**
* @method       CAlgoInterface::AddElemPara
* @access       protected
* @brief        add string parameter for ui
* @param        const UI_STRING_S & stString
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemPara(const UI_STRING_S &stString, const char *pcName)
{
    return AddElemPara(&stString, sizeof(stString), UI_STRING, pcName);
}

/**
* @method       CAlgoInterface::AddElemPara
* @access       protected
* @brief        add an element of parameters
* @param        const void * pData
* @param        const uint unLength
* @param        const UN_TYPE_MACRO_E eType
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemPara(const void *pData, const uint unLength,
                                const UN_TYPE_MACRO_E eType, const char *pcName)
{
    if (NULL == m_pPara)
    {
        return 1;
    }

    // only UI types allowed for parameters
    if (UI_GROUP > eType || UI_BUTT <= eType)
    {
        return 1;
    }

    return m_pPara->AddElem(pData, unLength, eType, pcName);
}

/**
* @method       CAlgoInterface::StartPackResult
* @access       protected
* @brief        start packing results
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::StartPackResult(void)
{
    if (NULL == m_pResult)
    {
        return 1;
    }

    return m_pResult->StartPack();
}

/**
* @method       CAlgoInterface::AddGroupResult
* @access       protected
* @brief        add a group of results
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::AddGroupResult(const char *pcName)
{
    if (NULL == m_pResult)
    {
        return 1;
    }

    return m_pResult->AddGroup(pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add bool to result
* @param        const bool bValue
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const bool bValue, const char *pcName)
{
    return AddElemResult(&bValue, sizeof(bValue), UN_BOOL, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add char to result
* @param        const char cValue
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const char cValue, const char *pcName)
{
    return AddElemResult(&cValue, sizeof(cValue), UN_CHAR, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add uchar to result
* @param        const uchar ucValue
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const uchar ucValue, const char *pcName)
{
    return AddElemResult(&ucValue, sizeof(ucValue), UN_UCHAR, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add short to result
* @param        const short sValue
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const short sValue, const char *pcName)
{
    return AddElemResult(&sValue, sizeof(sValue), UN_SHORT, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add ushort to result
* @param        const ushort usValue
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const ushort usValue, const char *pcName)
{
    return AddElemResult(&usValue, sizeof(usValue), UN_USHORT, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add int to result
* @param        const int nValue
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const int nValue, const char *pcName)
{
    return AddElemResult(&nValue, sizeof(nValue), UN_INT, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add uint to result
* @param        const uint unValue
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const uint unValue, const char *pcName)
{
    return AddElemResult(&unValue, sizeof(unValue), UN_UINT, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add float to result
* @param        const float fValue
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const float fValue, const char *pcName)
{
    return AddElemResult(&fValue, sizeof(fValue), UN_FLOAT, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add double to result
* @param        const double dValue
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const double dValue, const char *pcName)
{
    return AddElemResult(&dValue, sizeof(dValue), UN_DOUBLE, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add int64 to result
* @param        const int64 lValue
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const int64 lValue, const char *pcName)
{
    return AddElemResult(&lValue, sizeof(lValue), UN_INT64, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add uint64 to result
* @param        const uint64 ulValue
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const uint64 ulValue, const char *pcName)
{
    return AddElemResult(&ulValue, sizeof(ulValue), UN_UINT64, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add point to result
* @param        const UN_POINT_S & stPoint
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_POINT_S &stPoint, const char *pcName)
{
    return AddElemResult(&stPoint, sizeof(stPoint), UN_POINT, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add point to result
* @param        const UN_POINT_F_S & stPoint
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_POINT_F_S &stPoint, const char *pcName)
{
    return AddElemResult(&stPoint, sizeof(stPoint), UN_POINT_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add size to result
* @param        const UN_SIZE_S & stSize
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_SIZE_S &stSize, const char *pcName)
{
    return AddElemResult(&stSize, sizeof(stSize), UN_SIZE, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add size to result
* @param        const UN_SIZE_F_S & stSize
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_SIZE_F_S &stSize, const char *pcName)
{
    return AddElemResult(&stSize, sizeof(stSize), UN_SIZE_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add rectangle to result
* @param        const UN_RECT_S & stRect
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_RECT_S &stRect, const char *pcName)
{
    return AddElemResult(&stRect, sizeof(stRect), UN_RECT, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add rectangle to result
* @param        const UN_RECT_F_S & stRect
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_RECT_F_S &stRect, const char *pcName)
{
    return AddElemResult(&stRect, sizeof(stRect), UN_RECT_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add circle to result
* @param        const UN_CIRCLE_S & stCircle
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_CIRCLE_S &stCircle, const char *pcName)
{
    return AddElemResult(&stCircle, sizeof(stCircle), UN_CIRCLE, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add circle to result
* @param        const UN_CIRCLE_F_S & stCircle
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_CIRCLE_F_S &stCircle, const char *pcName)
{
    return AddElemResult(&stCircle, sizeof(stCircle), UN_CIRCLE_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add ellipse to result
* @param        const UN_ELLIPSE_S & stEllipse
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ELLIPSE_S &stEllipse, const char *pcName)
{
    return AddElemResult(&stEllipse, sizeof(stEllipse), UN_ELLIPSE, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add ellipse to result
* @param        const UN_ELLIPSE_F_S & stEllipse
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ELLIPSE_F_S &stEllipse, const char *pcName)
{
    return AddElemResult(&stEllipse, sizeof(stEllipse), UN_ELLIPSE_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add line to result
* @param        const UN_LINE_S & stLine
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_LINE_S &stLine, const char *pcName)
{
    return AddElemResult(&stLine, sizeof(stLine), UN_LINE, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add line to result
* @param        const UN_LINE_F_S & stLine
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_LINE_F_S &stLine, const char *pcName)
{
    return AddElemResult(&stLine, sizeof(stLine), UN_LINE_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add string to result
* @param        const UN_ARRAY_CHAR_S & stArrayChar
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_CHAR_S &stArrayChar, const char *pcName)
{
    return AddElemResult(&stArrayChar, sizeof(stArrayChar), UN_ARRAY_CHAR, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add array of int to result
* @param        const UN_ARRAY_INT_S & stArrayInt
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_INT_S &stArrayInt, const char *pcName)
{
    return AddElemResult(&stArrayInt, sizeof(stArrayInt), UN_ARRAY_INT, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add array of float to result
* @param        const UN_ARRAY_FLOAT_S & stArrayFloat
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_FLOAT_S &stArrayFloat, const char *pcName)
{
    return AddElemResult(&stArrayFloat, sizeof(stArrayFloat), UN_ARRAY_FLOAT, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add array of double to result
* @param        const UN_ARRAY_DOUBLE_S & stArrayDouble
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_DOUBLE_S &stArrayDouble, const char *pcName)
{
    return AddElemResult(&stArrayDouble, sizeof(stArrayDouble), UN_ARRAY_DOUBLE, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add array of points to result
* @param        const UN_ARRAY_POINT_S & stArrayPoint
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_POINT_S &stArrayPoint, const char *pcName)
{
    return AddElemResult(&stArrayPoint, sizeof(stArrayPoint), UN_ARRAY_POINT, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add array of points to result
* @param        const UN_ARRAY_POINT_F_S & stArrayPoint
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_POINT_F_S &stArrayPoint, const char *pcName)
{
    return AddElemResult(&stArrayPoint, sizeof(stArrayPoint), UN_ARRAY_POINT_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add array of rectangles to result
* @param        const UN_ARRAY_RECT_S & stArrayRect
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_RECT_S &stArrayRect, const char *pcName)
{
    return AddElemResult(&stArrayRect, sizeof(stArrayRect), UN_ARRAY_RECT, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add array of rectangles to result
* @param        const UN_ARRAY_RECT_F_S & stArrayRect
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_RECT_F_S &stArrayRect, const char *pcName)
{
    return AddElemResult(&stArrayRect, sizeof(stArrayRect), UN_ARRAY_RECT_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add array of circles to result
* @param        const UN_ARRAY_CIRCLE_S & stArrayCircle
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_CIRCLE_S &stArrayCircle, const char *pcName)
{
    return AddElemResult(&stArrayCircle, sizeof(stArrayCircle), UN_ARRAY_CIRCLE, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add array of circles to result
* @param        const UN_ARRAY_CIRCLE_F_S & stArrayCircle
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_CIRCLE_F_S &stArrayCircle, const char *pcName)
{
    return AddElemResult(&stArrayCircle, sizeof(stArrayCircle), UN_ARRAY_CIRCLE_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add array of ellipses to result
* @param        const UN_ARRAY_ELLIPSE_S & stArrayEllipse
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_ELLIPSE_S &stArrayEllipse, const char *pcName)
{
    return AddElemResult(&stArrayEllipse, sizeof(stArrayEllipse), UN_ARRAY_ELLIPSE, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add array of ellipses to result
* @param        const UN_ARRAY_ELLIPSE_F_S & stArrayEllipse
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_ELLIPSE_F_S &stArrayEllipse, const char *pcName)
{
    return AddElemResult(&stArrayEllipse, sizeof(stArrayEllipse), UN_ARRAY_ELLIPSE_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add array of lines to result
* @param        const UN_ARRAY_LINE_S & stArrayLine
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_LINE_S &stArrayLine, const char *pcName)
{
    return AddElemResult(&stArrayLine, sizeof(stArrayLine), UN_ARRAY_LINE, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add array of lines to result
* @param        const UN_ARRAY_LINE_F_S & stArrayLine
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const UN_ARRAY_LINE_F_S &stArrayLine, const char *pcName)
{
    return AddElemResult(&stArrayLine, sizeof(stArrayLine), UN_ARRAY_LINE_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemResult
* @access       protected
* @brief        add an element of results
* @param        const void * pData
* @param        const uint unLength
* @param        const UN_TYPE_MACRO_E eType
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemResult(const void *pData, const uint unLength,
                                  const UN_TYPE_MACRO_E eType, const char *pcName)
{
    if (NULL == m_pResult)
    {
        return 1;
    }

    // result types
    int nRv = 1;
    if ((UN_BASIC_START <= eType && UN_BASIC_BUTT > eType)
        || (UN_STRUCT_START <= eType && UN_STRUCT_BUTT > eType)
        || (UN_ARRAY_START <= eType && UN_ARRAY_BUTT > eType))
    {
        nRv = m_pResult->AddElem(pData, unLength, eType, pcName);
    }

    return nRv;
}

/**
* @method       CAlgoInterface::StartPackDisp
* @access       protected
* @brief        start packing displays
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::StartPackDisp(void)
{
    if (NULL == m_pDisp)
    {
        return 1;
    }

    return m_pDisp->StartPack();
}

/**
* @method       CAlgoInterface::AddGroupDisp
* @access       protected
* @brief        add a group of displays
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::AddGroupDisp(const char *pcName)
{
    if (NULL == m_pDisp)
    {
        return 1;
    }

    return m_pDisp->AddGroup(pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add point for display
* @param        const UN_POINT_S & stPoint
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_POINT_S &stPoint, const char *pcName)
{
    return AddElemDisp(&stPoint, sizeof(stPoint), UN_POINT, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add point for display
* @param        const UN_POINT_F_S & stPoint
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_POINT_F_S &stPoint, const char *pcName)
{
    return AddElemDisp(&stPoint, sizeof(stPoint), UN_POINT_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add rectangle for display
* @param        const UN_RECT_S & stRect
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_RECT_S &stRect, const char *pcName)
{
    return AddElemDisp(&stRect, sizeof(stRect), UN_RECT, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add rectangle for display
* @param        const UN_RECT_F_S & stRect
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_RECT_F_S &stRect, const char *pcName)
{
    return AddElemDisp(&stRect, sizeof(stRect), UN_RECT_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add circle for display
* @param        const UN_CIRCLE_S & stCircle
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_CIRCLE_S &stCircle, const char *pcName)
{
    return AddElemDisp(&stCircle, sizeof(stCircle), UN_CIRCLE, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add circle for display
* @param        const UN_CIRCLE_F_S & stCircle
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_CIRCLE_F_S &stCircle, const char *pcName)
{
    return AddElemDisp(&stCircle, sizeof(stCircle), UN_CIRCLE_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add ellipse for display
* @param        const UN_ELLIPSE_S & stEllipse
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_ELLIPSE_S &stEllipse, const char *pcName)
{
    return AddElemDisp(&stEllipse, sizeof(stEllipse), UN_ELLIPSE, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add ellipse for display
* @param        const UN_ELLIPSE_F_S & stEllipse
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_ELLIPSE_F_S &stEllipse, const char *pcName)
{
    return AddElemDisp(&stEllipse, sizeof(stEllipse), UN_ELLIPSE_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add line for display
* @param        const UN_LINE_S & stLine
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_LINE_S &stLine, const char *pcName)
{
    return AddElemDisp(&stLine, sizeof(stLine), UN_LINE, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add line for display
* @param        const UN_LINE_F_S & stLine
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_LINE_F_S &stLine, const char *pcName)
{
    return AddElemDisp(&stLine, sizeof(stLine), UN_LINE_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add image for display
* @param        const UN_IMAGE_INFO_S & stImage
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_IMAGE_INFO_S &stImage, const char *pcName)
{
    return AddElemDisp(&stImage, sizeof(stImage), UN_IMAGE, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add string for display
* @param        const UN_ARRAY_CHAR_S & stArrayChar
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_ARRAY_CHAR_S &stArrayChar, const char *pcName)
{
    return AddElemDisp(&stArrayChar, sizeof(stArrayChar), UN_ARRAY_CHAR, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add array of points for display
* @param        const UN_ARRAY_POINT_S & stArrayPoint
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_ARRAY_POINT_S &stArrayPoint, const char *pcName)
{
    return AddElemDisp(&stArrayPoint, sizeof(stArrayPoint), UN_ARRAY_POINT, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add array of points for display
* @param        const UN_ARRAY_POINT_F_S & stArrayPoint
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_ARRAY_POINT_F_S &stArrayPoint, const char *pcName)
{
    return AddElemDisp(&stArrayPoint, sizeof(stArrayPoint), UN_ARRAY_POINT_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add array of rectangles for display
* @param        const UN_ARRAY_RECT_S & stArrayRect
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_ARRAY_RECT_S &stArrayRect, const char *pcName)
{
    return AddElemDisp(&stArrayRect, sizeof(stArrayRect), UN_ARRAY_RECT, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add array of rectangles for display
* @param        const UN_ARRAY_RECT_F_S & stArrayRect
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_ARRAY_RECT_F_S &stArrayRect, const char *pcName)
{
    return AddElemDisp(&stArrayRect, sizeof(stArrayRect), UN_ARRAY_RECT_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add array of circles for display
* @param        const UN_ARRAY_CIRCLE_S & stArrayCircle
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_ARRAY_CIRCLE_S &stArrayCircle, const char *pcName)
{
    return AddElemDisp(&stArrayCircle, sizeof(stArrayCircle), UN_ARRAY_CIRCLE, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add array of circles for display
* @param        const UN_ARRAY_CIRCLE_F_S & stArrayCircle
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_ARRAY_CIRCLE_F_S &stArrayCircle, const char *pcName)
{
    return AddElemDisp(&stArrayCircle, sizeof(stArrayCircle), UN_ARRAY_CIRCLE_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add array of ellipses for display
* @param        const UN_ARRAY_ELLIPSE_S & stArrayEllipse
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_ARRAY_ELLIPSE_S &stArrayEllipse, const char *pcName)
{
    return AddElemDisp(&stArrayEllipse, sizeof(stArrayEllipse), UN_ARRAY_ELLIPSE, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add array of ellipses for display
* @param        const UN_ARRAY_ELLIPSE_F_S & stArrayEllipse
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_ARRAY_ELLIPSE_F_S &stArrayEllipse, const char *pcName)
{
    return AddElemDisp(&stArrayEllipse, sizeof(stArrayEllipse), UN_ARRAY_ELLIPSE_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add array of lines for display
* @param        const UN_ARRAY_LINE_S & stArrayLine
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_ARRAY_LINE_S &stArrayLine, const char *pcName)
{
    return AddElemDisp(&stArrayLine, sizeof(stArrayLine), UN_ARRAY_LINE, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add array of lines for display
* @param        const UN_ARRAY_LINE_F_S & stArrayLine
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const UN_ARRAY_LINE_F_S &stArrayLine, const char *pcName)
{
    return AddElemDisp(&stArrayLine, sizeof(stArrayLine), UN_ARRAY_LINE_F, pcName);
}

/**
* @method       CAlgoInterface::AddElemDisp
* @access       protected
* @brief        add an element of displays
* @param        const void * pData
* @param        const uint unLength
* @param        const UN_TYPE_MACRO_E eType
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-25
* @history      initial draft
*/
int CAlgoInterface::AddElemDisp(const void *pData, const uint unLength,
                                const UN_TYPE_MACRO_E eType, const char *pcName)
{
    if (NULL == m_pDisp)
    {
        return 1;
    }

    // display types
    int nRv = 1;
    if ((UN_IMAGE == eType) || (UN_ARRAY_CHAR == eType)
        || (UN_STRUCT_START <= eType && UN_STRUCT_BUTT > eType
        && UN_SIZE != eType && UN_SIZE_F != eType)
        || (UN_ARRAY_POINT <= eType && UN_ARRAY_BUTT > eType)) //lint !e772
    {
        nRv = m_pDisp->AddElem(pData, unLength, eType, pcName);
    }

    return nRv;
}

/**
* @method       CAlgoInterface::GetValueResult
* @access       public
* @brief        get value by group name and element name
* @param        const char * pcGroupName
* @param        const char * pcElemName
* @param        void * pData
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2013-03-04
* @history      initial draft
*/
int CAlgoInterface::GetValueResult(const char *pcGroupName, const char *pcElemName, void *pData)
{
    if (NULL == m_pResult)
    {
        return 1;
    }

    return m_pResult->GetValue(pcGroupName, pcElemName, pData);
}

/**
* @method       CAlgoInterface::SetROI
* @access       public
* @brief        set roi drawn by user
* @param        const CROI & roi
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
int CAlgoInterface::SetROI(const CROI &roi)
{
    if (NULL == m_pRoi)
    {
        return 1;
    }

#if 1
    bool bSame = IsTheSame(roi);
    if (bSame)
    {
        return 0;
    }

    m_pRoi->Clear();
    for (int i = 0; i < roi.GetCount(); ++i)
    {
        CROI::eType eT;
        CROI::eStatus eS;
        int nPoints = 0;
        roi.GetInfo(i, eT, eS, nPoints);

        if (CROI::TYPE_POLYGON == eT && 0 < nPoints)
        {
            int *pnX = mnew int[nPoints * 2];
            int *pnY = pnX + nPoints;
            roi.GetROI(i, pnX, pnY, nPoints);
            m_pRoi->Add(pnX, pnY, nPoints, eS);
            mdelete(pnX);
        } //lint !e438
        else
        {
            int nX, nY, nWidth, nHeight;
            roi.GetROI(i, nX, nY, nWidth, nHeight);
            m_pRoi->Add(nX, nY, nWidth, nHeight, eT, eS);
        }
    }
#else
    // can not use this in CAlgoInterface, DO NOT KNOW WHY
    *m_pRoi = roi;
#endif

    return 0;
}

/**
* @method       CAlgoInterface::IsTheSame
* @access       protected
* @brief        check if the input ROI is the same as m_pRoi
* @param        const CROI & roi
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-14
* @history      initial draft
*/
bool CAlgoInterface::IsTheSame(const CROI &roi) const
{
    if (NULL == m_pRoi || m_pRoi->GetCount() != roi.GetCount()
        || m_pRoi->GetIncludeCount() != roi.GetIncludeCount()
        || m_pRoi->GetExcludeCount() != roi.GetExcludeCount())
    {
        return false;
    }

    for (int i = 0; i < roi.GetCount(); ++i)
    {
        CROI::eType eT1, eT2;
        CROI::eStatus eS1, eS2;
        int nPoints1 = 0, nPoints2 = 0;
        m_pRoi->GetInfo(i, eT1, eS1, nPoints1);
        roi.GetInfo(i, eT2, eS2, nPoints2);
        if (eT1 != eT2 || eS1 != eS2 || nPoints1 != nPoints2)
        {
            return false;
        }

        if (CROI::TYPE_POLYGON == eT1 || 0 < nPoints1)
        {
            int *pnX1 = mnew int[nPoints1 * 4];
            if (NULL == pnX1)
            {
                return false;
            }
            int *pnY1 = pnX1 + nPoints1;
            int *pnX2 = pnY1 + nPoints1;
            int *pnY2 = pnX2 + nPoints1;

            m_pRoi->GetROI(i, pnX1, pnY1, nPoints1);
            roi.GetROI(i, pnX2, pnY2, nPoints2);
            for (int j = 0; j < nPoints1; ++j)
            {
                if (pnX1[j] != pnX2[j] || pnY1[j] != pnY2[j])
                {
                    mdelete(pnX1);
                    return false;
                }
            }
            mdelete(pnX1);
        }
        else
        {
            int nX1 = 0, nY1 = 0, nWidth1 = 0, nHeight1 = 0;
            int nX2 = 0, nY2 = 0, nWidth2 = 0, nHeight2 = 0;
            m_pRoi->GetROI(i, nX1, nY1, nWidth1, nHeight1);
            roi.GetROI(i, nX2, nY2, nWidth2, nHeight2);
            if (nX1 != nX2 || nY1 != nY2 || nWidth1 != nWidth2 || nHeight1 != nHeight2)
            {
                return false;
            }
        }
    }

    return true;
}

void CAlgoInterface::SaveImage(const uchar *pucImg, const int nWidth,
                               const int nHeight, const char *pcFile) const
{
    FILE *pfImg = NULL;
    (void) fopen_s(&pfImg, pcFile, "wb");
    if (NULL == pfImg)
    {
        return;
    }
    uchar aucHeader[54] = { 0 }, aucAlignBuf[4] = { 0 };
    const uint unAlign = (4 - (3 * nWidth) % 4) % 4; //lint !e732
    const uint unBufSize = (3 * nWidth + unAlign) * nHeight;
    const uint unFileSize = 54 + unBufSize;
    aucHeader[0] = 'B';
    aucHeader[1] = 'M';
    aucHeader[0x02] = unFileSize & 0xFF;
    aucHeader[0x03] = (unFileSize >> 8) & 0xFF;
    aucHeader[0x04] = (unFileSize >> 16) & 0xFF;
    aucHeader[0x05] = (unFileSize >> 24) & 0xFF;
    aucHeader[0x0A] = 0x36;
    aucHeader[0x0E] = 0x28;
    aucHeader[0x12] = nWidth & 0xFF;
    aucHeader[0x13] = (nWidth >> 8) & 0xFF; //lint !e702
    aucHeader[0x14] = (nWidth >> 16) & 0xFF; //lint !e702
    aucHeader[0x15] = (nWidth >> 24) & 0xFF; //lint !e702
    aucHeader[0x16] = nHeight & 0xFF;
    aucHeader[0x17] = (nHeight >> 8) & 0xFF; //lint !e702
    aucHeader[0x18] = (nHeight >> 16) & 0xFF; //lint !e702
    aucHeader[0x19] = (nHeight >> 24) & 0xFF; //lint !e702
    aucHeader[0x1A] = 1;
    aucHeader[0x1B] = 0;
    aucHeader[0x1C] = 24;
    aucHeader[0x1D] = 0;
    aucHeader[0x22] = unBufSize & 0xFF;
    aucHeader[0x23] = (unBufSize >> 8) & 0xFF;
    aucHeader[0x24] = (unBufSize >> 16) & 0xFF;
    aucHeader[0x25] = (unBufSize >> 24) & 0xFF;
    aucHeader[0x27] = 0x1;
    aucHeader[0x2B] = 0x1;
    fwrite(aucHeader, 1, 54, pfImg);

    const uchar *ptr = pucImg + nWidth * nHeight - nWidth;
    for (int i = 0; i < nHeight; i++)
    {
        for (int j = 0; j < nWidth; j++)
        {
            int val = (int)*(ptr++);
            if (val < 0)
            {
                val = 0;
            }
            else if (val > 255)
            {
                val = 255;
            }
            fputc(val, pfImg);
            fputc(val, pfImg);
            fputc(val, pfImg);
        }

        fwrite(aucAlignBuf, 1, unAlign, pfImg);
        ptr -= 2 * nWidth;
    }

    fclose(pfImg);
}

/**
* @method       GetTypeLength
* @access       public
* @brief        return length of type
* @param        const UN_TYPE_MACRO_E eTypeMacro
* @return       uint
* <AUTHOR> Lei, <EMAIL>
* @date         2012-05-29
* @history      initial draft
*/
uint GetTypeLength(const UN_TYPE_MACRO_E eTypeMacro)
{
    uint unLen = 0;
    if (UN_BASIC_START <= eTypeMacro && UN_BASIC_BUTT > eTypeMacro)
    {
        unLen = GetTypeLengthBasic(eTypeMacro);
    }
    else if (UN_IMAGE == eTypeMacro
        || (UN_STRUCT_START <= eTypeMacro && UN_STRUCT_BUTT > eTypeMacro))
    {
        unLen = GetTypeLengthStruct(eTypeMacro);
    }
    else if (UN_ARRAY_START <= eTypeMacro && UN_ARRAY_BUTT > eTypeMacro)
    {
        unLen = GetTypeLengthArray(eTypeMacro);
    }
    else if (UI_START <= eTypeMacro && UI_BUTT > eTypeMacro)
    {
        unLen = GetTypeLengthUi(eTypeMacro);
    }

    return unLen;
}

uint GetTypeLengthBasic(const UN_TYPE_MACRO_E eTypeMacro)
{
    uint unLen = 0;
    switch (eTypeMacro)
    {
    case UN_BOOL:
        unLen = sizeof(bool);
        break;
    case UN_CHAR:
        unLen = sizeof(char);
        break;
    case UN_UCHAR:
        unLen = sizeof(uchar);
        break;
    case UN_SHORT:
        unLen = sizeof(__int16);
        break;
    case UN_USHORT:
        unLen = sizeof(ushort);
        break;
    case UN_INT:
        unLen = sizeof(int);
        break;
    case UN_UINT:
        unLen = sizeof(uint);
        break;
    case UN_FLOAT:
        unLen = sizeof(float);
        break;
    case UN_DOUBLE:
        unLen = sizeof(double);
        break;
    case UN_INT64:
        unLen = sizeof(int64);
        break;
    case UN_UINT64:
        unLen = sizeof(uint64);
        break;
    default:
        unLen = 0;
        break;
    } //lint !e788

    return unLen;
}

uint GetTypeLengthStruct(const UN_TYPE_MACRO_E eTypeMacro)
{
    uint unLen = 0;
    switch (eTypeMacro)
    {
    case UN_POINT:
        unLen = sizeof(UN_POINT_S);
        break;
    case UN_SIZE:
        unLen = sizeof(UN_SIZE_S);
        break;
    case UN_RECT:
        unLen = sizeof(UN_RECT_S);
        break;
    case UN_CIRCLE:
        unLen = sizeof(UN_CIRCLE_S);
        break;
    case UN_ELLIPSE:
        unLen = sizeof(UN_ELLIPSE_S);
        break;
    case UN_LINE:
        unLen = sizeof(UN_LINE_S);
        break;
    case UN_POINT_F:
        unLen = sizeof(UN_POINT_F_S);
        break;
    case UN_SIZE_F:
        unLen = sizeof(UN_SIZE_F_S);
        break;
    case UN_RECT_F:
        unLen = sizeof(UN_RECT_F_S);
        break;
    case UN_CIRCLE_F:
        unLen = sizeof(UN_CIRCLE_F_S);
        break;
    case UN_ELLIPSE_F:
        unLen = sizeof(UN_ELLIPSE_F_S);
        break;
    case UN_LINE_F:
        unLen = sizeof(UN_LINE_F_S);
        break;

    case UN_IMAGE:
        unLen = sizeof(UN_IMAGE_INFO_S);
        break;
    default:
        unLen = 0;
        break;
    } //lint !e788

    return unLen;
}

uint GetTypeLengthArray(const UN_TYPE_MACRO_E eTypeMacro)
{
    uint unLen = 0;
    switch (eTypeMacro)
    {
    case UN_ARRAY_CHAR:
        unLen = sizeof(UN_ARRAY_CHAR_S);
        break;
    case UN_ARRAY_INT:
        unLen = sizeof(UN_ARRAY_INT_S);
        break;
    case UN_ARRAY_FLOAT:
        unLen = sizeof(UN_ARRAY_FLOAT_S);
        break;
    case UN_ARRAY_DOUBLE:
        unLen = sizeof(UN_ARRAY_DOUBLE_S);
        break;
    case UN_ARRAY_POINT:
        unLen = sizeof(UN_ARRAY_POINT_S);
        break;
    case UN_ARRAY_POINT_F:
        unLen = sizeof(UN_ARRAY_POINT_F_S);
        break;
    case UN_ARRAY_RECT:
        unLen = sizeof(UN_ARRAY_RECT_S);
        break;
    case UN_ARRAY_RECT_F:
        unLen = sizeof(UN_ARRAY_RECT_F_S);
        break;
    case UN_ARRAY_CIRCLE:
        unLen = sizeof(UN_ARRAY_CIRCLE_S);
        break;
    case UN_ARRAY_CIRCLE_F:
        unLen = sizeof(UN_ARRAY_CIRCLE_F_S);
        break;
    case UN_ARRAY_ELLIPSE:
        unLen = sizeof(UN_ARRAY_ELLIPSE_S);
        break;
    case UN_ARRAY_ELLIPSE_F:
        unLen = sizeof(UN_ARRAY_ELLIPSE_F_S);
        break;
    case UN_ARRAY_LINE:
        unLen = sizeof(UN_ARRAY_LINE_S);
        break;
    case UN_ARRAY_LINE_F:
        unLen = sizeof(UN_ARRAY_LINE_F_S);
        break;
    default:
        unLen = 0;
        break;
    } //lint !e788

    return unLen;
}

uint GetTypeLengthUi(const UN_TYPE_MACRO_E eTypeMacro)
{
    uint unLen = 0;
    switch (eTypeMacro)
    {
    case UI_GROUP:
        unLen = 0;
        break;
    case UI_EDIT:
        unLen = sizeof(UI_EDIT_S);
        break;
    case UI_EDIT_F:
        unLen = sizeof(UI_EDIT_F_S);
        break;
    case UI_CHECK:
        unLen = sizeof(UI_CHECK_S);
        break;
    case UI_RADIO:
        unLen = sizeof(UI_RADIO_S);
        break;
    case UI_IMAGE:
        unLen = sizeof(UI_IMAGE_S);
        break;
    case UI_FILE:
        unLen = sizeof(UI_FILE_S);
        break;
    case UI_FOLDER:
        unLen = sizeof(UI_FOLDER_S);
        break;
    case UI_STRING:
        unLen = sizeof(UI_STRING_S);
        break;

    default:
        unLen = 0;
        break;
    } //lint !e788

    return unLen;
}

/**
* @method       IsValidType
* @access       public
* @brief        check if it's a valid type
* @param        const int nType
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
bool IsValidType(const int nType)
{
    if ((int) UN_BASIC_START <= nType && (int) UN_BASIC_BUTT > nType)
    {
        return true;
    }

    if ((int) UN_STRUCT_START <= nType && (int) UN_STRUCT_BUTT > nType)
    {
        return true;
    }

    if ((int) UN_IMAGE == nType)
    {
        return true;
    }

    if ((int) UN_ARRAY_START <= nType && (int) UN_ARRAY_BUTT > nType)
    {
        return true;
    }

    // nType can not equals to UI_START (UI_GROUP)
    if ((int) UI_START < nType && (int) UI_BUTT > nType)
    {
        return true;
    }

    return false;
}

/**
* @method       CParaPack::CParaPack
* @access       public
* @brief        construct function
* @param        void
* @return
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
CParaPack::CParaPack(void)
{
    m_pucData = NULL;
    m_unDataLen = 0;
    m_unPackIndex = 0;
    m_unUnpackIndex = 0;
}

/**
* @method       CParaPack::~CParaPack
* @access       public
* @brief        destruct function
* @param        void
* @return
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
CParaPack::~CParaPack(void)
{
    mdelete(m_pucData);
}

/**
* @method       CParaPack::StartPack
* @access       public
* @brief        start packing
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
int CParaPack::StartPack(void)
{
    m_unPackIndex = 0;
    return 0;
}

/**
* @method       CParaPack::AddGroup
* @access       public
* @brief        add group, it's a must before adding any element
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
int CParaPack::AddGroup(const char *pcName)
{
    if (NULL == pcName)
    {
        return 1;
    }

    // check length of group's name
    uint unLen = strlen(pcName);
    if (UN_VAR_NAME_LEN <= unLen)
    {
        return 1;
    }

    // allocate memory
    if (NULL == m_pucData)
    {
        m_pucData = mnew uchar[_KB_ * 32];
        if (NULL == m_pucData) //lint !e774
        {
            return 1;
        }

        m_unDataLen = _KB_ * 32;
        m_unPackIndex = 0;
        m_unUnpackIndex = 0;
        memset(m_pucData, 0, m_unDataLen);
    }

    // memory is not enough
    while (m_unPackIndex + UN_TLV_HEADER_LEN >= m_unDataLen)
    {
        uchar *pucTmp = mnew uchar[m_unDataLen + _MB_];
        if (NULL == pucTmp)
        {
            return 1;
        }

        // copy previous data and delete previous memory
        memcpy(pucTmp, m_pucData, m_unPackIndex);
        mdelete(m_pucData);
        m_pucData = pucTmp;

        // update length of data
        m_unDataLen += _MB_;
    }

    // generate TLV aucHeader
    UN_TLV_HEADER_S stHeader;
    stHeader.nLength = 0;
    stHeader.nType = (int) UI_GROUP;
    (void) strcpy_s(stHeader.acName, sizeof(stHeader.acName), pcName);

    // copy aucHeader to buffer
    memcpy(m_pucData + m_unPackIndex, &stHeader, sizeof(stHeader)); //lint !e1415
    m_unPackIndex += sizeof(stHeader);

    return 0;
}

/**
* @method       CParaPack::AddElem
* @access       public
* @brief        add element
* @param        const void * pData
* @param        const uint unLength
* @param        const UN_TYPE_MACRO_E eType
* @param        const char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
int CParaPack::AddElem(const void *pData, const uint unLength,
                       const UN_TYPE_MACRO_E eType, const char *pcName)
{
    if (NULL == pData || NULL == pcName)
    {
        return 1;
    }

    // check length of group's name and other parameters
    uint unLen = strlen(pcName);
    if (UN_VAR_NAME_LEN <= unLen || 0 == unLength
        || (uint) unLength != GetTypeLength(eType))
    {
        return 1;
    }

    // it can not be null after adding a group
    if (NULL == m_pucData)
    {
        return 1;
    }

    // memory is not enough
    while (m_unPackIndex + ((uint) unLength) + UN_TLV_HEADER_LEN >= m_unDataLen)
    {
        uchar *pucTmp = mnew uchar[m_unDataLen + _MB_];
        if (NULL == pucTmp)
        {
            return 1;
        }

        // copy previous data and delete previous memory
        memcpy(pucTmp, m_pucData, m_unPackIndex);
        mdelete(m_pucData);
        m_pucData = pucTmp;

        // update length of data
        m_unDataLen += _MB_;
    }

    // generate TLV aucHeader
    UN_TLV_HEADER_S stHeader;
    stHeader.nLength = (int) unLength;
    stHeader.nType = (int) eType;
    (void) strcpy_s(stHeader.acName, sizeof(stHeader.acName), pcName);

    // copy aucHeader to buffer
    memcpy(m_pucData + m_unPackIndex, &stHeader, sizeof(stHeader)); //lint !e1415
    m_unPackIndex += sizeof(stHeader);

    // copy data to buffer
    memcpy(m_pucData + m_unPackIndex, pData, unLength);
    m_unPackIndex += unLength;

    return 0;
}

/**
* @method       CParaPack::StartUnpack
* @access       public
* @brief        start unpacking
* @param        void
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
int CParaPack::StartUnpack(void)
{
    if (NULL == m_pucData || 0 == m_unPackIndex
        || 0 == m_unDataLen)
    {
        return 1;
    }

    m_unUnpackIndex = 0;
    return 0;
}

/**
* @method       CParaPack::GetNextGroup
* @access       public
* @brief        get the name and number of elements of the next group
* @param        char * pcName
* @param        int & nElem
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
int CParaPack::GetNextGroup(char *pcName, int &nElem)
{
    nElem = 0;

    if (NULL == m_pucData || 0 == m_unPackIndex
        || 0 == m_unDataLen)
    {
        return 1;
    }

    if (NULL == pcName)
    {
        return 1;
    }

    pcName[0] = '\0';

    // end of buffer
    if (m_unUnpackIndex >= m_unPackIndex)
    {
        return 1;
    }

    // get the nearest group
    uint unTmp = m_unUnpackIndex;
    UN_TLV_HEADER_S *pstHeader = (UN_TLV_HEADER_S *) (m_pucData + unTmp); //lint !e826
    while (((int) UI_GROUP) != pstHeader->nType && unTmp < m_unPackIndex)
    {
        unTmp += UN_TLV_HEADER_LEN;
        unTmp += (uint) pstHeader->nLength;
        pstHeader = (UN_TLV_HEADER_S *) (m_pucData + unTmp); //lint !e826
    }

    if (((int) UI_GROUP) != pstHeader->nType || unTmp > m_unPackIndex)
    {
        return 1;
    }

    // update unpack index
    m_unUnpackIndex = unTmp + UN_TLV_HEADER_LEN;

    // copy group's name
    (void) strcpy_s(pcName, sizeof(pstHeader->acName), pstHeader->acName);

    // get the number of elements belongs to this group
    unTmp = m_unUnpackIndex;
    pstHeader = (UN_TLV_HEADER_S *) (m_pucData + unTmp);
    while (((int) UI_GROUP) != pstHeader->nType && unTmp < m_unPackIndex)
    {
        nElem++;
        unTmp += UN_TLV_HEADER_LEN;
        unTmp += (uint) pstHeader->nLength;
        pstHeader = (UN_TLV_HEADER_S *) (m_pucData + unTmp); //lint !e826
    }

    return 0;
}

/**
* @method       CParaPack::GetNextElemInfo
* @access       public
* @brief        get information of the next element
* @param        UN_TYPE_MACRO_E & eType
* @param        char * pcName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
int CParaPack::GetNextElemInfo(UN_TYPE_MACRO_E &eType, char *pcName)
{
    if (NULL == pcName || NULL == m_pucData)
    {
        return 1;
    }

    pcName[0] = '\0';
    eType = UI_BUTT;

    // end of buffer
    if (m_unUnpackIndex >= m_unPackIndex)
    {
        return 0;
    }

    // get the nearest group
    UN_TLV_HEADER_S *pstHeader = (UN_TLV_HEADER_S *) (m_pucData + m_unUnpackIndex); //lint !e826
    if (!IsValidType(pstHeader->nType))
    {
        return 1;
    }

    eType = (UN_TYPE_MACRO_E) pstHeader->nType; //lint !e838

    // update unpack index
    m_unUnpackIndex += UN_TLV_HEADER_LEN;

    // copy group's name
    (void) strcpy_s(pcName, sizeof(pstHeader->acName), pstHeader->acName);

    return 0;
}

/**
* @method       CParaPack::GetNextElemData
* @access       public
* @brief        get data of element, for image and array, only address of the pointer was copied
* @param        void * pData
* @param        const int nLength
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
int CParaPack::GetNextElemData(void *pData, const int nLength)
{
    if (NULL == pData || 0 >= nLength || NULL == m_pucData)
    {
        return 1;
    }

    // end of buffer
    if (m_unUnpackIndex + ((uint) nLength) > m_unPackIndex)
    {
        return 1;
    }

    // copy data out
    memcpy(pData, m_pucData + m_unUnpackIndex, (uint) nLength);
    m_unUnpackIndex += (uint) nLength;

    return 0;
}

/**
* @method       CParaPack::SetValue
* @access       public
* @brief        set parameter's value
* @param        const char * pcGroupName
* @param        const char * pcElemName
* @param        const void * pData
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
int CParaPack::SetValue(const char *pcGroupName, const char *pcElemName, const void *pData)
{
    if (NULL == pcGroupName || NULL == pcElemName || NULL == pData)
    {
        return 1;
    }

    // try to find group
    int nIndex = FindElem(pcGroupName, pcElemName);
    if (-1 == nIndex || ((uint) nIndex) + UN_TLV_HEADER_LEN >= m_unPackIndex)
    {
        return 1;
    }

    UN_TLV_HEADER_S *pstHeader = (UN_TLV_HEADER_S *) (m_pucData + nIndex); //lint !e826
    memcpy(m_pucData + nIndex + UN_TLV_HEADER_LEN, pData, (uint) pstHeader->nLength);

    return 0;
}

/**
* @method       CParaPack::GetValue
* @access       public
* @brief        get parameter's value
* @param        const char * pcGroupName
* @param        const char * pcElemName
* @param        void * pData
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
int CParaPack::GetValue(const char *pcGroupName, const char *pcElemName, void *pData)
{
    if (NULL == pcGroupName || NULL == pcElemName || NULL == pData)
    {
        return 1;
    }

    int nIndex = FindElem(pcGroupName, pcElemName);
    if (-1 == nIndex || ((uint) nIndex) + UN_TLV_HEADER_LEN >= m_unPackIndex)
    {
        return 1;
    }

    UN_TLV_HEADER_S *pstHeader = (UN_TLV_HEADER_S *) (m_pucData + nIndex); //lint !e826
    memcpy(pData, m_pucData + nIndex + UN_TLV_HEADER_LEN, (uint) pstHeader->nLength);

    return 0;
}

/**
* @method       CParaPack::GetInfo
* @access       public
* @brief        get type information using group name and element name
* @param        const char * pcGroupName
* @param        const char * pcElemName
* @param        UN_TYPE_MACRO_E & eType
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-26
* @history      initial draft
*/
int CParaPack::GetInfo(const char *pcGroupName, const char *pcElemName, UN_TYPE_MACRO_E &eType)
{
    if (NULL == pcGroupName || NULL == pcElemName)
    {
        return 1;
    }

    int nIndex = FindElem(pcGroupName, pcElemName);
    if (-1 == nIndex || ((uint) nIndex) + UN_TLV_HEADER_LEN >= m_unPackIndex)
    {
        return 1;
    }

    UN_TLV_HEADER_S *pstHeader = (UN_TLV_HEADER_S *) (m_pucData + nIndex); //lint !e826
    eType = (UN_TYPE_MACRO_E) pstHeader->nType;

    return 0;
}

/**
* @method       CParaPack::FindGroup
* @access       private
* @brief        try to find group, -1 means didn't find
* @param        const char * pcGroupName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
int CParaPack::FindGroup(const char *pcGroupName)
{
    if (NULL == pcGroupName || NULL == m_pucData || 0 == m_unDataLen)
    {
        return -1;
    }

    uint unIndex = 0;
    UN_TLV_HEADER_S *pstHeader = (UN_TLV_HEADER_S *) (m_pucData);
    while (unIndex < m_unPackIndex)
    {
        // if current block is not a group or the name is not match
        if ((int) UI_GROUP != pstHeader->nType
            || 0 != strcmp(pcGroupName, pstHeader->acName))
        {
            unIndex += UN_TLV_HEADER_LEN;
            unIndex += GetTypeLength((UN_TYPE_MACRO_E) pstHeader->nType);
            pstHeader = (UN_TLV_HEADER_S *) (m_pucData + unIndex); //lint !e826
        }
        else
        {
            break;
        }
    }

    if (unIndex >= m_unPackIndex)
    {
        return -1;
    }

    return (int) unIndex;
}

/**
* @method       CParaPack::FindElem
* @access       private
* @brief        try to find element, -1 means didn't find
* @param        const char * pcGroupName
* @param        const char * pcElemName
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2012-06-12
* @history      initial draft
*/
int CParaPack::FindElem(const char *pcGroupName, const char *pcElemName)
{
    if (NULL == pcGroupName || NULL == pcElemName)
    {
        return -1;
    }

    uint unIndex = (uint) FindGroup(pcGroupName);
    if (((uint) -1) == unIndex || unIndex + UN_TLV_HEADER_LEN >= m_unPackIndex)
    {
        return -1;
    }

    // try to find element
    unIndex += UN_TLV_HEADER_LEN;
    UN_TLV_HEADER_S *pstHeader = (UN_TLV_HEADER_S *) (m_pucData + unIndex); //lint !e826
    while ((int) UI_GROUP != pstHeader->nType
        && 0 != strcmp(pcElemName, pstHeader->acName)
        && unIndex < m_unPackIndex)
    {
        unIndex += UN_TLV_HEADER_LEN;
        unIndex += GetTypeLength((UN_TYPE_MACRO_E) pstHeader->nType);
        pstHeader = (UN_TLV_HEADER_S *) (m_pucData + unIndex); //lint !e826
    }

    // get to the end of buffer or to the next group
    if (unIndex >= m_unPackIndex || (int) UI_GROUP == pstHeader->nType)
    {
        return -1;
    }

    return (int) unIndex;
}

/**
* @method       CROI::CROI
* @access       public
* @brief        construct function
* @param        void
* @return
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
CROI::CROI(void)
{
    m_nCountInc = 0;
    m_nCountExc = 0;
    m_nMskSize = 0;
    m_pucMsk = NULL;
    m_bChanged = true;
    m_nMaxNum = 32;
    m_nPolySize = 256;

    m_pstRois = mnew Roi_[m_nMaxNum];
    m_pnPolygon = mnew int[m_nPolySize];
    m_nPolyIndex = 0;
}

/**
* @method       CROI::~CROI
* @access       public
* @brief        destruct function
* @param        void
* @return
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
CROI::~CROI(void)
{
    Clear(); //lint !e1551

    // this should after Clear()
    mdelete(m_pnPolygon);
    mdelete(m_pstRois);
    mdelete(m_pucMsk);
}

/**
* @method       CROI::CROI
* @access       public
* @brief        construct roi object using another one
* @param        const CROI & other
* @return
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
CROI::CROI(const CROI &other)
{
    m_pucMsk = NULL;
    m_nCountInc = other.m_nCountInc;
    m_nCountExc = other.m_nCountExc;
    m_nMskSize = other.m_nMskSize;
    m_bChanged = other.m_bChanged;
    m_nMaxNum = other.m_nMaxNum;
    m_nPolySize = other.m_nPolySize;
    m_nPolyIndex = other.m_nPolyIndex;
    m_pstRois = mnew Roi_[m_nMaxNum];
    m_pnPolygon = mnew int[m_nPolySize];
    memcpy(m_pstRois, other.m_pstRois, sizeof(m_pstRois[0]) * m_nMaxNum);
    memcpy(m_pnPolygon, other.m_pnPolygon, sizeof(m_pnPolygon[0]) * m_nPolySize);
    if (0 < m_nMskSize)
    {
        m_pucMsk = mnew uchar[m_nMskSize];
        memcpy(m_pucMsk, other.m_pucMsk, sizeof(m_pucMsk[0]) * m_nMskSize);
    }
}

/**
* @method       CROI::operator=
* @access       public
* @brief        assign operator
* @param        const CROI & other
* @return       CROI &
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-25
* @history      initial draft
*/
CROI & CROI::operator =(const CROI &other)
{
    if (this == &other)
    {
        return *this;
    }

    // release memory first
    mdelete(m_pnPolygon);
    mdelete(m_pstRois);
    mdelete(m_pucMsk);

    m_nCountInc = other.m_nCountInc;
    m_nCountExc = other.m_nCountExc;
    m_nMskSize = other.m_nMskSize;
    m_bChanged = other.m_bChanged;
    m_nMaxNum = other.m_nMaxNum;
    m_nPolySize = other.m_nPolySize;
    m_nPolyIndex = other.m_nPolyIndex;
    m_pstRois = mnew Roi_[m_nMaxNum];
    m_pnPolygon = mnew int[m_nPolySize];
    memcpy(m_pstRois, other.m_pstRois, sizeof(m_pstRois[0]) * m_nMaxNum);
    memcpy(m_pnPolygon, other.m_pnPolygon, sizeof(m_pnPolygon[0]) * m_nPolySize);
    if (0 < m_nMskSize)
    {
        m_pucMsk = mnew uchar[m_nMskSize];
        memcpy(m_pucMsk, other.m_pucMsk, sizeof(m_pucMsk[0]) * m_nMskSize);
    }

    return *this;
}

/**
* @method       CROI::Add
* @access       public
* @brief        add rectangle, circle or ellipse
* @param        const int nX
* @param        const int nY
* @param        const int nWidth
* @param        const int nHeight
* @param        const eType eT
* @param        const eStatus eS
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
bool CROI::Add(const int nX, const int nY, const int nWidth, const int nHeight,
               const eType eT, const eStatus eS)
{
    if (TYPE_POLYGON == eT || TYPE_ALL <= eT || STATUS_ALL <= eS
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    const int nCount = GetCount();

    // memory is not enough
    if (nCount >= m_nMaxNum)
    {
        bool bOk = ExtendMemory();
        if (!bOk)
        {
            return false;
        }
    }

    m_pstRois[nCount].nX = nX;
    m_pstRois[nCount].nY = nY;
    m_pstRois[nCount].nWidth = nWidth;
    m_pstRois[nCount].nHeight = nHeight;
    m_pstRois[nCount].eT = eT;
    m_pstRois[nCount].eS = eS;

    // make it a real circle if nWidth not equals to nHeight
    if (TYPE_CIRCLE == eT)
    {
        m_pstRois[nCount].nWidth = min(nWidth, nHeight);
        m_pstRois[nCount].nHeight = min(nWidth, nHeight);
    }

    m_nCountInc += STATUS_INCLUDE == eS; //lint !e514
    m_nCountExc += STATUS_EXCLUDE == eS; //lint !e514
    m_bChanged = true;
    return true;
}

/**
* @method       CROI::Add
* @access       public
* @brief        add polygon
* @param        const int * pnXs
* @param        const int * pnYs
* @param        const int nPoints
* @param        const eStatus eS
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
bool CROI::Add(const int *pnXs, const int *pnYs, const int nPoints, const eStatus eS)
{
    if (STATUS_ALL <= eS || NULL == pnXs || NULL == pnYs || 0 >= nPoints)
    {
        return false;
    }

    const int nCount = GetCount();

    // memory is not enough
    if (nCount >= m_nMaxNum)
    {
        bool bOk = ExtendMemory();
        if (!bOk)
        {
            return false;
        }
    }

    if (m_nPolyIndex + nPoints * 2 > m_nPolySize)
    {
        int nSize = max(m_nPolySize * 2, m_nPolySize + nPoints * 2);
        int *pnTmp = mnew int[nSize];
        if (NULL == pnTmp)
        {
            return false;
        }

        memcpy(pnTmp, m_pnPolygon, sizeof(m_pnPolygon[0]) * m_nPolySize);
        mdelete(m_pnPolygon);
        m_pnPolygon = pnTmp;
        m_nPolySize = nSize;
    }

    memcpy(m_pnPolygon + m_nPolyIndex, pnXs, sizeof(int) * nPoints); //lint !e662 !e669
    memcpy(m_pnPolygon + m_nPolyIndex + nPoints, pnYs, sizeof(int) * nPoints); //lint !e662 !e669

    // save memory address index and number of points
    m_pstRois[nCount].nX = m_nPolyIndex;
    m_pstRois[nCount].nY = m_nPolyIndex + nPoints;
    m_pstRois[nCount].nWidth = nPoints;
    m_pstRois[nCount].nHeight = nPoints;
    m_pstRois[nCount].eT = TYPE_POLYGON;
    m_pstRois[nCount].eS = eS;
    m_nPolyIndex += nPoints * 2;

    m_nCountInc += STATUS_INCLUDE == eS; //lint !e514
    m_nCountExc += STATUS_EXCLUDE == eS; //lint !e514
    m_bChanged = true;
    return true;
}

/**
* @method       CROI::Remove
* @access       public
* @brief        remove the nIndex-th roi
* @param        const int nIndex
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
bool CROI::Remove(const int nIndex)
{
    const int nCount = GetCount();
    if (0 > nIndex || nCount <= nIndex)
    {
        return false;
    }

    m_nCountInc -= STATUS_INCLUDE == m_pstRois[nIndex].eS; //lint !e514
    m_nCountExc -= STATUS_EXCLUDE == m_pstRois[nIndex].eS; //lint !e514
    m_bChanged = true;

    // move the ones after nIndex a step forward
    for (int i = nIndex + 1; i < nCount; ++i)
    {
        m_pstRois[i - 1] = m_pstRois[i];
    }

    return true;
}

/**
* @method       CROI::Clear
* @access       public
* @brief        clear all ROIs
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
void CROI::Clear()
{
    // clear count
    m_nCountInc = 0;
    m_nCountExc = 0;
    m_nPolyIndex = 0;
    m_bChanged = true;
}

/**
* @method       CROI::GetCount
* @access       public
* @brief        return number of ROIs (both include and exclude)
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
int CROI::GetCount() const
{
    return m_nCountInc + m_nCountExc;
}

/**
* @method       CROI::GetIncludeCount
* @access       public
* @brief        return number of include ROIs
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
int CROI::GetIncludeCount() const
{
    return m_nCountInc;
}

/**
* @method       CROI::GetExcludeCount
* @access       public
* @brief        return number of exclude ROIs
* @return       int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
int CROI::GetExcludeCount() const
{
    return m_nCountExc;
}

/**
* @method       CROI::GetIncludeUnion
* @access       public
* @brief        get the union of all include ROIs
* @param        int & nLeft
* @param        int & nTop
* @param        int & nRight
* @param        int & nBottom
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
bool CROI::GetIncludeUnion(int &nLeft, int &nTop, int &nRight, int &nBottom) const
{
    bool bInit = false;
    const int nCount = GetCount();
    for (int i = 0; i < nCount; ++i)
    {
        if (STATUS_EXCLUDE == m_pstRois[i].eS)
        {
            continue;
        }

        if (TYPE_POLYGON == m_pstRois[i].eT)
        {
            const int nPoints = m_pstRois[i].nWidth;
            int *pnX = m_pnPolygon + m_pstRois[i].nX;
            int *pnY = m_pnPolygon + m_pstRois[i].nY;

            for (int j = 0; j < nPoints; ++j)
            {
                if (bInit)
                {
                    nLeft = min(nLeft, pnX[j]);
                    nRight = max(nRight, pnX[j]);
                    nTop = min(nTop, pnY[j]);
                    nBottom = max(nBottom, pnY[j]);
                }
                else
                {
                    bInit = true;
                    nLeft = pnX[j];
                    nRight = pnX[j];
                    nTop = pnY[j];
                    nBottom = pnY[j];
                }
            }
            nRight++;
            nBottom++;
        }
        else
        {
            if (bInit)
            {
                nLeft = min(nLeft, m_pstRois[i].nX);
                nRight = max(nRight, m_pstRois[i].nX + m_pstRois[i].nWidth);
                nTop = min(nTop, m_pstRois[i].nY);
                nBottom = max(nBottom, m_pstRois[i].nY + m_pstRois[i].nHeight);
            }
            else
            {
                bInit = true;
                nLeft = m_pstRois[i].nX;
                nRight = nLeft + m_pstRois[i].nWidth;
                nTop = m_pstRois[i].nY;
                nBottom = nTop + m_pstRois[i].nHeight;
            }
        }
    }

    return bInit;
}

/**
* @method       CROI::GetInfo
* @access       public
* @brief        get the information of the nIndex-th roi
* @param        const int nIndex
* @param        eType & eT
* @param        eStatus & eS
* @param        int & nPoints
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
bool CROI::GetInfo(const int nIndex, eType &eT, eStatus &eS, int &nPoints) const
{
    const int nCount = GetCount();
    if (0 > nIndex || nCount <= nIndex)
    {
        return false;
    }

    eT = m_pstRois[nIndex].eT;
    eS = m_pstRois[nIndex].eS;
    nPoints = 0;
    if (TYPE_POLYGON == eT)
    {
        nPoints = m_pstRois[nIndex].nWidth;
    }

    return true;
}

/**
* @method       CROI::GetROI
* @access       public
* @brief        get detailed information of nIndex-roi (rectangle, circle, ellipse)
* @param        const int nIndex
* @param        int & nX
* @param        int & nY
* @param        int & nWidth
* @param        int & nHeight
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
bool CROI::GetROI(const int nIndex, int &nX, int &nY, int &nWidth, int &nHeight) const
{
    const int nCount = GetCount();
    if (0 > nIndex || nCount <= nIndex || TYPE_POLYGON == m_pstRois[nIndex].eT)
    {
        return false;
    }

    nX = m_pstRois[nIndex].nX;
    nY = m_pstRois[nIndex].nY;
    nWidth = m_pstRois[nIndex].nWidth;
    nHeight = m_pstRois[nIndex].nHeight;

    return true;
}

/**
* @method       CROI::GetROI
* @access       public
* @brief        get detailed information of nIndex-th roi (polygon only)
* @param        const int nIndex
* @param        int * pnXs
* @param        int * pnYs
* @param        const int nPoints
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
bool CROI::GetROI(const int nIndex, int *pnXs, int *pnYs, const int nPoints) const
{
    const int nCount = GetCount();
    if (0 > nIndex || nCount <= nIndex || TYPE_POLYGON != m_pstRois[nIndex].eT
        || NULL == pnXs || NULL == pnYs)
    {
        return false;
    }

    const int nNum = min(nPoints, m_pstRois[nIndex].nWidth);
    memcpy(pnXs, m_pnPolygon + m_pstRois[nIndex].nX, sizeof(int) * nNum);
    memcpy(pnYs, m_pnPolygon + m_pstRois[nIndex].nY, sizeof(int) * nNum);

    return true;
} //lint !e429

/**
* @method       CROI::GetMask
* @access       public
* @brief        get the whole mask
* @param        const int nWidth
* @param        const int nHeight
* @return       uchar *
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-04
* @history      initial draft
*/
uchar *CROI::GetMask(const int nWidth, const int nHeight)
{
    if (0 >= nWidth || 0 >= nHeight)
    {
        return NULL;
    }

    // try to reuse the memory allocated previously
    if (m_nMskSize != nWidth * nHeight)
    {
        mdelete(m_pucMsk);
        m_nMskSize = 0;
    }

    if (NULL == m_pucMsk)
    {
        m_pucMsk = mnew uchar[nWidth * nHeight];
        if (NULL == m_pucMsk)
        {
            return NULL;
        }
        m_nMskSize = nWidth * nHeight;
        m_bChanged = true;
    }

    // not changed from the last time, return the mask already generated
    if (!m_bChanged)
    {
        return m_pucMsk;
    }

    const int nCount = GetCount();
    if (0 >= nCount)
    {
        // no ROI
        memset(m_pucMsk, 255, sizeof(uchar) * m_nMskSize);
    }
    else if (0 == m_nCountInc)
    {
        // no include ROI
        memset(m_pucMsk, 255, sizeof(uchar) * m_nMskSize);

        GeneExcludeMask(m_pucMsk, nWidth, nHeight);
    }
    else if (0 == m_nCountExc)
    {
        // no exclude ROI
        memset(m_pucMsk, 0, sizeof(uchar) * m_nMskSize);

        GeneIncludeMask(m_pucMsk, nWidth, nHeight);
    }
    else
    {
        // generate include mask
        memset(m_pucMsk, 0, sizeof(uchar) * m_nMskSize);

        // include first
        GeneIncludeMask(m_pucMsk, nWidth, nHeight);

        // then exclude
        GeneExcludeMask(m_pucMsk, nWidth, nHeight);
    }
    m_bChanged = false;

    return m_pucMsk;
}

/**
* @method       CROI::GetSingleMask
* @access       public
* @brief        get mask of the nIndex-th roi
* @param        const int nIndex
* @param        int & nStartX
* @param        int & nStartY
* @param        int & nWidth
* @param        int & nHeight
* @return       uchar *
* <AUTHOR> Lei, <EMAIL>
* @date         2014-06-20
* @history      initial draft
*/
uchar *CROI::GetSingleMask(const int nIndex, int &nStartX, int &nStartY,
                           int &nWidth, int &nHeight) const
{
    const int nCount = GetCount();
    if (0 > nIndex || nCount <= nIndex)
    {
        return NULL;
    }

    uchar *pucMsk = NULL;
    int nPoints = 0;
    eType eT;
    eStatus eS;
    GetInfo(nIndex, eT, eS, nPoints);

    bool bOk = false;
    if (TYPE_POLYGON == eT)
    {
        int *pnXs = m_pnPolygon + m_pstRois[nIndex].nX;
        int *pnYs = m_pnPolygon + m_pstRois[nIndex].nY;
        int nLeft = pnXs[0], nRight = pnXs[0];
        int nTop = pnYs[0], nBottom = pnYs[0];
        for (int i = 1; i < nPoints; ++i)
        {
            nLeft = min(nLeft, pnXs[i]);
            nRight = max(nRight, pnXs[i]);
            nTop = min(nTop, pnYs[i]);
            nBottom = max(nBottom, pnYs[i]);
        }

        nWidth = nRight - nLeft + 1;
        nHeight = nBottom - nTop + 1;
        nStartX = nLeft;
        nStartY = nTop;
        pucMsk = mnew uchar[nWidth * nHeight];
        if (NULL == pucMsk)
        {
            return pucMsk;
        }

        bOk = GeneMaskPolygon(pnXs, pnYs, nPoints, nLeft, nTop, nRight, nBottom, pucMsk);
    }
    else
    {
        GetROI(nIndex, nStartX, nStartY, nWidth, nHeight);
        pucMsk = mnew uchar[nWidth * nHeight];
        if (NULL == pucMsk)
        {
            return pucMsk;
        }
        memset(pucMsk, 0, sizeof(pucMsk[0]) * nWidth * nHeight);

        if (TYPE_RECT == eT)
        {
            bOk = true;
            memset(pucMsk, 255, sizeof(pucMsk[0]) * nWidth * nHeight);
        }
        else if (TYPE_CIRCLE == eT)
        {
            bOk = GeneMaskCircle(nWidth, nHeight, pucMsk);
        }
        else if (TYPE_ELLIPSE == eT)
        {
            bOk = GeneMaskEllipse(nWidth, nHeight, pucMsk);
        }
    }

    if (!bOk)
    {
        mdelete(pucMsk);
        return NULL;
    }

    // invert mask for exclude mode
    if (STATUS_EXCLUDE == eS)
    {
        for (int i = 0; i < nWidth * nHeight; ++i)
        {
            pucMsk[i] = 255 - pucMsk[i];
        }
    }

    return pucMsk;
}

/**
* @method       CROI::GeneMaskPolygon
* @access       private
* @brief        generate mask for polygon
* @param        const int * pnXs
* @param        const int * pnYs
* @param        const int nPoints
* @param        const int nLeft
* @param        const int nTop
* @param        const int nRight
* @param        const int nBottom
* @param        uchar * pucMsk
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-03
* @history      initial draft
*/
bool CROI::GeneMaskPolygon(const int *pnXs, const int *pnYs, const int nPoints,
                           const int nLeft, const int nTop, const int nRight,
                           const int nBottom, uchar *pucMsk) const
{
    if (NULL == pnXs || NULL == pnYs || NULL == pucMsk || 3 > nPoints
        || 0 > nLeft || 0 > nTop || nRight <= nLeft || nBottom <= nTop)
    {
        return false;
    }

    const int nWidth = nRight - nLeft + 1;
    const int nHeight = nBottom - nTop + 1;
    // expanded two pixels
    const int nDelta = 2;
    const int nW = nWidth + nDelta * 2, nH = nHeight + nDelta * 2;
    uchar *pucTmp = mnew uchar[nW * nH * 2];
    if (NULL == pucTmp)
    {
        return false;
    }
    memset(pucTmp, 0, sizeof(pucTmp[0]) * nW * nH * 2);
    uchar *pucTmp2 = pucTmp + nW * nH;

    const int N = nW + nH;
    Point_ *pstPoints = mnew Point_[N];
    if (NULL == pstPoints)
    {
        mdelete(pucTmp);
        return false;
    }

    // initial mask with polygon lines filled
    for (int i = 0; i < nPoints; ++i)
    {
        Point_ p1, p2;
        p1.nX = pnXs[i] - nLeft + nDelta;
        p1.nY = pnYs[i] - nTop + nDelta;
        p2.nX = pnXs[(i + 1) % nPoints] - nLeft + nDelta;
        p2.nY = pnYs[(i + 1) % nPoints] - nTop + nDelta;
        int nNum = ConnectTwoPoints(p1, p2, pstPoints, N);
        for (int j = 0; j < nNum; ++j)
        {
            pucTmp[pstPoints[j].nY * nW + pstPoints[j].nX] = 255;
        }
    }

    // trace
    TraceOneRegion(pucTmp, pucTmp2, nW, nH, 1, 1, 0, 0, 255);

    for (int i = 0; i < nHeight; ++i)
    {
        memcpy(pucMsk + i * nWidth, pucTmp2 + (i + nDelta) * nW + nDelta, (uint) nWidth);
    }

    // invert mask
    for (int i = 0; i < nHeight * nWidth; ++i)
    {
        pucMsk[i] = 255 - pucMsk[i];
    }

    mdelete(pstPoints);
    mdelete(pucTmp);
    return true;
} //lint !e429

/**
* @method       CROI::ConnectTwoPoints
* @access       private
* @brief        fill the gaps between to points
* @param        const Point_ & stPoint1
* @param        const Point_ & stPoint2
* @param        Point_ * pstPoints
* @param        const int nLength
* @return       const int
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-03
* @history      initial draft
*/
const int CROI::ConnectTwoPoints(const Point_ &stPoint1, const Point_ &stPoint2,
                                 Point_* pstPoints, const int nLength) const
{
    // too much points need to fill between these two points
    if (abs(stPoint1.nX - stPoint2.nX) + abs(stPoint1.nY - stPoint2.nY) + 1 >= nLength
        || NULL == pstPoints || stPoint1.nX < 0 || stPoint1.nY < 0
        || stPoint2.nX < 0 || stPoint2.nY < 0)
    {
        return -1; // error occurred
    }
    int nAdd = 0;
    int x1 = stPoint1.nX, y1 = stPoint1.nY;
    int x2 = stPoint2.nX, y2 = stPoint2.nY;
    int xDiff = x2 - x1, yDiff = y2 - y1;
    int xDelta = (xDiff == 0 ) ? 0 : ((xDiff > 0) ? 1 : -1);
    int yDelta = (yDiff == 0 ) ? 0 : ((yDiff > 0) ? 1 : -1);

    // add the first point
    pstPoints[nAdd] = stPoint1;
    nAdd++;
    // four situations
    // 1:    |
    //       |
    //       |
    if (xDiff == 0 && yDiff != 0)
    {
        for (int j = 1; j < abs(yDiff); j++)
        {
            pstPoints[nAdd].nX = x1;
            pstPoints[nAdd].nY = y1 + yDelta * j;
            nAdd++;
        }
    }
    // 2:    ---
    else if (xDiff != 0 && yDiff == 0)
    {
        for (int j = 1; j < abs(xDiff); j++)
        {
            pstPoints[nAdd].nX = x1 + xDelta * j;
            pstPoints[nAdd].nY = y1;
            nAdd++;
        }
    }
    // 3:     /    \
    //       /  or  \
    //      /        \

    else if (xDiff != 0 && yDiff != 0)
    {
        float xk = 0.0f, yk = 0.0f;
        if (abs(xDiff) > abs(yDiff))
        {
            xk =1.0f; yk = (float) fabs(((float) yDiff) / xDiff);
        }
        else
        {
            yk =1.0f; xk = (float) fabs(((float) xDiff) / yDiff);
        }
        for (int j = 1; j < max(abs(xDiff), abs(yDiff)); j++)
        {
            pstPoints[nAdd].nX = (int) (x1 + xDelta * xk * j);
            pstPoints[nAdd].nY = (int) (y1 + yDelta * yk * j);
            nAdd++;
        }
    }
    else if (xDiff == 0 && yDiff == 0)
    {
        // no points need to be filled
    }
    // add the last point
    pstPoints[nAdd] = stPoint2;
    nAdd++;

    return nAdd;
} //lint !e429

/**
* @method       CROI::TraceOneRegion
* @access       private
* @brief        trace region to generate polygon mask
* @param        const uchar * pucImg
* @param        uchar * pucMask
* @param        const int nWidth
* @param        const int nHeight
* @param        const int nX_
* @param        const int nY_
* @param        const int nMinTh
* @param        const int nMaxTh
* @param        const uchar ucLabel
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-04
* @history      initial draft
*/
bool CROI::TraceOneRegion(const uchar *pucImg, uchar *pucMask, const int nWidth,
                          const int nHeight, const int nX_, const int nY_, const int nMinTh,
                          const int nMaxTh, const uchar ucLabel) const
{
    if (NULL == pucMask || NULL == pucImg
        || 0 >= nWidth || 0 >= nHeight)
    {
        return false;
    }

    const int g_anDirections[4][2] = {{-1, 0}, {1, 0}, {0, 1}, {0, -1}};
    int nHead = 0;
    int nTail = 1;
    int nX, nY;
    const int QUEUESIZE = 2048;
    int anQueue[QUEUESIZE][2];
    anQueue[nHead][1] = nY_;
    anQueue[nHead][0] = nX_;
    if (pucMask[nY_ * nWidth + nX_] == ucLabel) // already traced
    {
        return false;
    }
    pucMask[nY_ * nWidth + nX_] = ucLabel;
    int nGray = pucImg[nY_ * nWidth + nX_] + 1; // add one for calculating gravity

    while (nHead != nTail)
    {
        for (int k = 0; k < 4; k ++)
        {
            nY = anQueue[nHead][1] + g_anDirections[k][1];
            nX = anQueue[nHead][0] + g_anDirections[k][0];
            if (nX <= 0 || nY <= 0 || nX >= nWidth - 1 || nY >= nHeight - 1)
            {
                continue;
            }

            int nIndex = nY * nWidth + nX;
            nGray = pucImg[nIndex];
            if (nGray >= nMinTh && nGray <= nMaxTh && pucMask[nIndex] != ucLabel)
            {
                nGray++; // at least one for calculating gravity
                pucMask[nIndex] = ucLabel;

                anQueue[nTail][0] = nX;
                anQueue[nTail][1] = nY;
                nTail++;
                nTail &= QUEUESIZE-1;
            }
        }
        nHead++;
        nHead &= QUEUESIZE-1;
    }

    return true;
}

/**
* @method       CROI::GeneMaskCircle
* @access       public
* @brief        generate mask for circle
* @param        const int nWidth
* @param        const int nHeight
* @param        uchar * pucMsk
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-04
* @history      initial draft
*/
bool CROI::GeneMaskCircle(const int nWidth, const int nHeight, uchar *pucMsk) const
{
    // nWidth should equals to nHeight
    if (nWidth != nHeight || NULL == pucMsk)
    {
        return false;
    }

    // x * x + y * y = r * r
    const float fX = (nWidth - 1.0f) / 2;
    const float fDiameter2 = (nWidth / 2.0f) * (nWidth / 2.0f);
    for (int i = 0; i < nWidth; ++i)
    {
        const float fYDist = i - fX;
        for (int j = 0; j < nWidth; ++j)
        {
            const float fXDist = j - fX;
            if (fXDist * fXDist + fYDist * fYDist <= fDiameter2)
            {
                pucMsk[i * nWidth + j] = 255;
            }
        }
    }

    return true;
} //lint !e429

/**
* @method       CROI::GeneMaskEllipse
* @access       public
* @brief        generate mask for ellipse
* @param        const int nWidth
* @param        const int nHeight
* @param        uchar * pucMsk
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-04
* @history      initial draft
*/
bool CROI::GeneMaskEllipse(const int nWidth, const int nHeight, uchar *pucMsk) const
{
    if (0 >= nWidth || 0 >= nHeight || NULL == pucMsk)
    {
        return false;
    }

    // x * x / (a * a) + y * y / (b * b) = 1
    // => b * b * x * x + a * a * y * y = a * a * b * b
    // => fA = a * a, fB = b * b
    const float fXC = (nWidth - 1.0f) / 2;
    const float fYC = (nHeight - 1.0f) / 2;
    const float fA = (nWidth / 2.0f) * (nWidth / 2.0f);
    const float fB = (nHeight / 2.0f) * (nHeight / 2.0f);
    const float fDist = fA * fB;

    for (int i = 0; i < nHeight; ++i)
    {
        const float fY = (i - fYC) * (i - fYC);
        for (int j = 0; j < nWidth; ++j)
        {
            const float fX = (j - fXC) * (j - fXC);
            if (fB * fX + fA * fY <= fDist)
            {
                pucMsk[i * nWidth + j] = 255;
            }
        }
    }

    return true;
} //lint !e429

/**
* @method       CROI::GeneIncludeMask
* @access       public
* @brief        generate mask for include ROIs
* @param        uchar * pucMask
* @param        const int nWidth
* @param        const int nHeight
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-04
* @history      initial draft
*/
void CROI::GeneIncludeMask(uchar *pucMask, const int nWidth, const int nHeight) const
{
    if (NULL == pucMask || 0 >= nWidth || 0 >= nHeight)
    {
        return;
    }

    int nX = 0, nY = 0, nW = 0, nH = 0;
    for (int k = 0; k < GetCount(); ++k)
    {
        if (STATUS_EXCLUDE == m_pstRois[k].eS)
        {
            continue;
        }

        uchar *pucSingle = GetSingleMask(k, nX, nY, nW, nH);
        if (NULL == pucSingle)
        {
            continue;
        }

        for (int i = 0; i < nH; ++i)
        {
            if (0 > i + nY || nHeight - 1 < i + nY)
            {
                continue;
            }
            for (int j = 0; j < nW; ++j)
            {
                if (0 > j + nX || nWidth - 1 < j + nX)
                {
                    continue;
                }
                uchar ucValue = pucSingle[i * nW + j];
                if (0 < ucValue)
                {
                    pucMask[(i + nY) * nWidth + nX + j] = ucValue;
                }
            }
        }

        mdelete(pucSingle);
    }
} //lint !e429

/**
* @method       CROI::GeneExcludeMask
* @access       public
* @brief        generate mask for exclude ROIs
* @param        uchar * pucMask
* @param        const int nWidth
* @param        const int nHeight
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-04
* @history      initial draft
*/
void CROI::GeneExcludeMask(uchar *pucMask, const int nWidth, const int nHeight) const
{
    if (NULL == pucMask || 0 >= nWidth || 0 >= nHeight)
    {
        return;
    }

    int nX = 0, nY = 0, nW = 0, nH = 0;
    for (int k = 0; k < GetCount(); ++k)
    {
        if (STATUS_INCLUDE == m_pstRois[k].eS)
        {
            continue;
        }

        uchar *pucSingle = GetSingleMask(k, nX, nY, nW, nH);
        if (NULL == pucSingle)
        {
            continue;
        }

        for (int i = 0; i < nH; ++i)
        {
            if (0 > i + nY || nHeight - 1 < i + nY)
            {
                continue;
            }
            for (int j = 0; j < nW; ++j)
            {
                if (0 > j + nX || nWidth - 1 < j + nX)
                {
                    continue;
                }
                uchar ucValue = pucSingle[i * nW + j];
                if (0 == ucValue)
                {
                    pucMask[(i + nY) * nWidth + nX + j] = ucValue;
                }
            }
        }

        mdelete(pucSingle);
    }
} //lint !e429

/**
* @method       CROI::ExtendMemory
* @access       public
* @brief        extend memory
* @return       bool
* <AUTHOR> Lei, <EMAIL>
* @date         2014-07-04
* @history      initial draft
*/
bool CROI::ExtendMemory()
{
    // extend memory
    int nTemp = m_nMaxNum * 2;
    Roi_ *pstRoi = mnew Roi_[nTemp];
    if (NULL == pstRoi)
    {
        return false;
    }

    memcpy(pstRoi, m_pstRois, sizeof(m_pstRois[0]) * m_nMaxNum); //lint !e670
    mdelete(m_pstRois);
    m_nMaxNum = nTemp;
    m_pstRois = pstRoi;

    return true;
}
