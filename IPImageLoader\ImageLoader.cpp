/******************************************************************************
	COPYRIGHTS (C), 2005-2006, UNIC TECHNOLOGIES.

	FILE NAME:	ImageLoader.cpp
	AUTHOR:		Xie Yonghui
	VERSION:	2.0
	DATE:		05-28-2004
	COMMENTS:	General Interface of loading any format of Image.
	REVISION:	All rights reversed by UNIC TECHNOLOGIES
*******************************************************************************/

//#include "stdafx.h"
#include "ImageLoader.h"

//#include "RawOptionDlg.h"
//#include "Dib.h"
// #include "Gif89a.h"
//#include "QuickJpeg.h"
// #include "Jpeg.h"
//#include "j2klib.h"
// #include "Tiff.h"
// #include "CPng.h"
// #include "jp2.h"
#include "../FastJpeg/FastJpeg.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CImageLoader::CImageLoader()
{
	m_nJpegQlt = 75;
}

CImageLoader::CImageLoader(char * filename, CDib ** pDib)
{
	LoadImg(filename, pDib);
}

CImageLoader::~CImageLoader()
{

}

BOOL CImageLoader::LoadImg(char * filename, CDib ** pDib)
{
	return FALSE;
}

BOOL CImageLoader::LoadImgAs(char *filename, CDib ** pDib, int nType)
{
	return FALSE;
}

BOOL CImageLoader::SaveImg(char * filename, CDib * pDib)
{
	return FALSE;
}

BOOL CImageLoader::SaveImgAs(char *filename, CDib * pDib, int nType)
{
	return FALSE;
}

BOOL CImageLoader::LoadBMP(char * filename, CDib ** pDib)
{
	return FALSE;
}

BOOL CImageLoader::SaveBMP(char * filename, CDib * pDib)
{
	return FALSE;
}


// RAW
BOOL CImageLoader::LoadRAW(char *filename, CDib **pDib)
{
	return FALSE;
}

BOOL CImageLoader::SaveRAW(char *filename, CDib *pDib)
{
	return FALSE;
}

// GIF
BOOL CImageLoader::LoadGIF(char *filename, CDib **pDib)
{
	return FALSE;
}

BOOL CImageLoader::SaveGIF(char *filename, CDib *pDib)
{
	return FALSE;
}

// JPG
BOOL CImageLoader::LoadJPG(char *filename, CDib **pDib)
{
	/*CQuickJpeg jpeg;

	if (jpeg.Load((LPCTSTR)filename) == FALSE)
		return FALSE;

	CDib	*pTempDib = NULL;
	pTempDib = new CDib;
	pTempDib->Copy(jpeg.GetDib());
	*pDib = pTempDib;*/

	return TRUE;
}

BOOL CImageLoader::SaveJPG(char *filename, CDib *pDib)
{
	/*CQuickJpeg jpeg;
	return jpeg.Save((LPCTSTR)filename, pDib, m_nJpegQlt);*/
	return false;
}

// PCX
BOOL CImageLoader::LoadPCX(char *filename, CDib **pDib)
{
	return FALSE;
}

BOOL CImageLoader::SavePCX(char *filename, CDib *pDib)
{
	return FALSE;
}

// TGA
BOOL CImageLoader::LoadTGA(char *filename, CDib **pDib)
{
	return TRUE;
}

BOOL CImageLoader::SaveTGA(char *filename, CDib *pDib)
{
	return FALSE;
}

// TIF
BOOL CImageLoader::LoadTIF(char *filename, CDib **pDib)
{
	return FALSE;
}

BOOL CImageLoader::SaveTIF(char *filename, CDib *pDib)
{
	return FALSE;
}

// PNG
BOOL CImageLoader::LoadPNG(char *filename, CDib **pDib)
{
	return FALSE;
}
BOOL CImageLoader::SavePNG(char *filename, CDib *pDib)
{
	return FALSE;
}

// J2K
BOOL CImageLoader::LoadJ2K(char *filename, CDib **pDib)
{
	return FALSE;
}

BOOL CImageLoader::SaveJ2K(char *filename, CDib *pDib)
{
	return FALSE;
}

// for loading one tile from datafile 
BOOL CImageLoader::
LoadJpgBlock(FILE *fpdata, long lOffest, unsigned int dwLength, BYTE *pData,
	int nWidth, int nHeight, int nImgType, int nAlgo, BOOL bChangeRB)
{
	//CQuickJpeg jpeg;
	CFastJpeg jpeg;
	return jpeg.LoadOneBlock(fpdata, lOffest, dwLength, pData, nWidth, nHeight, nImgType, FALSE);

}

BOOL CImageLoader::
LoadJpgBlock(BYTE *pInputData, BYTE *pOutputData, int nDataSize, int nWidth, int nHeight,
	int nImgType, int nAlgo, BOOL bChangeRB)
{

	CFastJpeg jpeg;
	return jpeg.LoadOneBlock(pInputData, pOutputData, nDataSize, nWidth, nHeight, nImgType, FALSE);
}

BOOL CImageLoader::SaveJpgBlock(FILE *fpdata, BYTE *pData, int nWidth, int nHeight,
	int nImgType, int nQuality /*=75*/, int nAlgo)
{
	CFastJpeg jpeg;
	return jpeg.SaveOneBlock(fpdata, pData, nWidth, nHeight, nImgType, nQuality);
	
}

BOOL CImageLoader::SaveJpgData(char *filename, BYTE *pData, int nWidth, int nHeight,
	BOOL bColor, BOOL bChangeRB, int nQuality /*=75*/, int nAlgo)
{
	CFastJpeg jpeg;
	return jpeg.SaveJpgData(filename, pData, nWidth, nHeight, bColor, FALSE, nQuality);

}

BOOL CImageLoader::LoadJpgData(char *filename, BYTE *pData, int nWidth, int nHeight,
	BOOL bColor, int nAlgo)
{
	CFastJpeg jpeg;
	return jpeg.LoadJpgData(filename, pData, nWidth, nHeight, bColor);
}

BOOL CImageLoader::JpegTransform(BYTE *pRGBData, int nWidth, int nHeight, int nImgType, BOOL bChangeRB,
	BYTE *pJpegData, int *pDataLen, int nQuality)
{
	CFastJpeg jpeg;
	return jpeg.compress(pRGBData, nWidth, nHeight, nWidth*nImgType / 3, &pJpegData, (unsigned long*)pDataLen, nQuality);

}