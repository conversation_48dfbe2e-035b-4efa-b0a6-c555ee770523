/******************************************************************************
	COPYRIGHTS (C), 2005-2006, UNIC TECHNOLOGIES..

	FILE NAME:	ImageLoader.h
	AUTHOR:		Xie Yonghui
	VERSION:	2.0
	DATE:		05-28-2004
	COMMENTS:	General Interface of loading any format of Image.
	REVISION:	All rights reversed by UNIC TECHNOLOGIES
*******************************************************************************/
#ifndef __IMAGELOADER_H__
#define __IMAGELOADER_H__

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
#include <afx.h>
#include "./Dib.h"

typedef int BOOL;

enum IMAGEFILE_TYPE {
	IFT_BMP,
	IFT_RAW,
	IFT_GIF,
	IFT_JPG,
	IFT_PCX,
	IFT_TGA,
	IFT_TIF,
	IFT_PNG,
	IFT_J2K
};

enum JPEG_ALOG
{
	JPEG_QUICK = 0,
	JPEG_NOMAL,
	JPEG_2000,

	JPEG_TOTAL
};

class IPIMAGELOADER_API CImageLoader
{
public:
	
	CImageLoader();
	CImageLoader(char *filename, CDib** pDib);

	~CImageLoader();

//  for TMap file
	BOOL LoadJpgBlock(FILE *fpdata, long lOffest, unsigned int dwLength,
					BYTE *pData, int nWidth, int nHeight, int nImgType, 
					int nAlgo = JPEG_QUICK, BOOL bChangeRB = false);
	BOOL LoadJpgBlock(BYTE *pInputData, BYTE *pOutputData, int nDataSize, int nWidth,
					int nHeight, int nImgType, int nAlgo = JPEG_QUICK, BOOL bChangeRB = false);	
	BOOL SaveJpgBlock(FILE *pfdata, BYTE *pData, int nWidth, int nHeight,
					int nImgType, int nQuality = 75, int nAlgo = JPEG_QUICK);
	
	BOOL LoadJpgData(char *filename, BYTE *pData, int nWidth, int nHeight,
					BOOL bColor = true, int nAlgo = JPEG_QUICK);
	BOOL SaveJpgData(char *filename, BYTE *pData, int nWidth, int nHeight,
					BOOL bColor = true, BOOL bChangeRB = false, int nQuality = 75, int nAlgo = JPEG_QUICK);

// General Interface
	BOOL LoadImg(char *filename, CDib** pDib);
	BOOL LoadImgAs(char *filename, CDib** pDib, int nType = IFT_BMP);
	BOOL SaveImg(char *filename, CDib* pDib);
	BOOL SaveImgAs(char *filename, CDib* pDib, int nType = IFT_BMP);
	BOOL JpegTransform(BYTE *pRGBData, int nWidth, int nheight, int nImgType, BOOL bChangeRB, 
				BYTE *pJpegData, int *pDataLen, int nQuality = 75);
	void SetJPGQlt(int nQuality){m_nJpegQlt = nQuality;}
	
private:
	// BMP
	BOOL LoadBMP(char *filename, CDib **pDib);
	BOOL SaveBMP(char *filename, CDib *pDib);

	// RAW
	BOOL LoadRAW(char *filename, CDib **pDib);
	BOOL SaveRAW(char *filename, CDib *pDib);

	// GIF
	BOOL LoadGIF(char *filename, CDib **pDib);
	BOOL SaveGIF(char *filename, CDib *pDib);

	// JPG
	int	 m_nJpegQlt;
	BOOL LoadJPG(char *filename, CDib **pDib);
	BOOL SaveJPG(char *filename, CDib *pDib);
	
	// for compressed data
	BOOL SaveComp(char *filename, CDib *pDib);
	BOOL LoadComp(char *filename, CDib **pDib);	
	
	// PCX
	BOOL LoadPCX(char *filename, CDib **pDib);
	BOOL SavePCX(char *filename, CDib *pDib);

	// TGA
	BOOL LoadTGA(char *filename, CDib **pDib);
	BOOL SaveTGA(char *filename, CDib *pDib);

	// TIF
	BOOL LoadTIF(char *filename, CDib **pDib);
	BOOL SaveTIF(char *filename, CDib *pDib);

	// PNG
	BOOL LoadPNG(char *filename, CDib **pDib) ;
	BOOL SavePNG(char *filename, CDib *pDib) ;

	// J2K
	BOOL LoadJ2K(char *filename, CDib **pDib) ;
	BOOL SaveJ2K(char *filename, CDib *pDib) ;
};

#endif // __IMAGELOADER_H__
