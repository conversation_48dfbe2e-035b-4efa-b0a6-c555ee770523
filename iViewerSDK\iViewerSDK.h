/**
* @date         2015-11-26
* @filename     iViewerSDK.h
* @purpose      interface for TMAP file
* @version      2.5.1
* @history      initial draft
* <AUTHOR> UNIC, Beijing, China
* @copyright    <EMAIL>, UNIC Technologies Inc, 2005-2016. All rights reserved.
*/

#ifndef __IVIEWERSDK__
#define __IVIEWERSDK__

#ifdef IVIEWERSDK_EXPORTS
#define IVIEWERSDK_API __declspec(dllexport)
#else
#define IVIEWERSDK_API __declspec(dllimport)
#endif

#ifdef x64
//#include "../ualgo/BaseAlgo.h"
#endif //x64

#include <memory>
enum PF_CALLBACK_MSG
{
    PF_SUCCESS, // success, nothing in the buffer
    PF_FAILED, // failed, return code (int) is stored in the buffer
    PF_INPROCESS, // progress, [0, 100], int
    PF_NOTICE, // string message
    PF_DATA // any type of data (decided by both client and developer)
};
class TIFFFile;

/**
* @method       PFCallback
* @access       public 
* @brief        callback function of creating TMAP file
* @param        const int nID, ID of current event
* @param        const PF_CALLBACK_MSG eType, type of message
* @param        const int nSize, length of message
* @param        const void* pData, message data
* @return       void
* <AUTHOR> Lei, <EMAIL>
* @date         2015-12-14
* @history      initial draft
*/
typedef void (__stdcall* PFCallback)(const int nID, const PF_CALLBACK_MSG eType,
                                     const int nSize, const void* pData);

class IVIEWERSDK_API iViewerSDK
{
 public:
    enum IMAGE_TYPE
    {
        eImageThumbnail = 0, // thumbnail image <= 256
        eImageNavigate, // navigate image <= 640
        eImageMacro, // macro image
        eImageLabel, // label image <= 256
        eImageMacroLabel, // image with macro and label <= 256
        eImageTile, // tile image = 256
        eImageWhole, // the whole image
        eImageAll
    };

    enum EXT_TYPE
    {
        eExtCodeID = 3, // string, code
        eExtSlideInfo, // string, slide information
        eExtSystemInfo, // string, system information
        eExtRuntimeInfo, // string, runtime information
        eExtMarkInfo, // string, mark information
        eExtAll
    };
	enum Tile_Type
	{
		Tmap_Tiles,
		Tiff_Tiles
	};

    iViewerSDK(void);

    ~iViewerSDK(void);

    // return 0 if success, support local TMAP only
    int OpenFile(const char *pcFile, const bool bReadOnly = true, const Tile_Type eType = Tmap_Tiles);

    // close file
    void CloseFile();

    // get maximal number of users
    int GetMaxUserNum(void) const;

    // get the main version
    int GetVersion(void) const;

    // get the scanning scale
    int GetScanScale(void) const;

    // get number of layers
    int GetLayerNumber(void) const;

    // get number of tiles
    int GetTileNumber(void) const;

    // get number of focus layers
    int GetFocusNumber(void) const;

    // get current focus layer
    int GetFocusLayer(void) const;

    // set current focus layer
    bool SetFocusLayer(const int nFocus = 0) const;

    // get scanning time (s)
    float GetScanTime(void) const;

    // get pixel size (mm, 100x)
    float GetPixelSize(void) const;

    // get compression factor
    int GetQuality(void) const;

    // get background color
    unsigned char GetBkgColor(void) const;

    // get OCR, return NULL if there is no OCR
    const char *GetOCR(void) const;

    // get code in label, return NULL if there is no code
    const char *GetCode(void) const;

    // get image information (width, height and depth)
    // supports all IMAGE_TYPE
    // size of bitmap data will be returned
    __int64 GetImageInfo(const IMAGE_TYPE eType, int &nWidth, int &nHeight, int &nDepth) const;

    // get bitmap data of thumbnail image, label image,
    // macro image or image with both label and macro
    // eType can NOT be eImageTile or eImageWhole
    // pucBuffer should be allocated by user
    // nBufferLength should no less than the size returned by the above function
    bool GetImageData(const IMAGE_TYPE eType, unsigned char *pucBuffer,
        const int nBufferLength) const;

    // get JPEG data of thumbnail image, label image, macro image or image with both label and macro
    // eType can NOT be eImageTile or eImageWhole
    // pucBuffer is allocated inside and can NOT be released outside
    // nBufferLength will be changed inside to indicate the size of JPEG image data
    // user could set the JPEG compression quality (invalid for eImageThumbnail)
    bool GetJpegData(const IMAGE_TYPE eType, unsigned char *&pucBuffer,
        int &nBufferLength, const int nQuality = 65) const;

    // get size of bitmap data of the whole image at a certain scale
    // nLeft, nTop, nRight and nBottom should be the ROI at the scan scale
    // the image size at the scale fScale will be saved in nWidth and nHeight
    // return 0 means failed
    __int64 GetImageSize(const int nLeft, const int nTop, const int nRight,
        const int nBottom, const float fScale, int &nWidth, int &nHeight) const;

    // get bitmap data of the whole image at a certain scale
    // nLeft, nTop, nRight and nBottom should be the ROI at the scan scale
    // pucBuffer should be allocated by user
    // nBufferLength should no less than the size returned by the above function, 2GB at most
    // nIndex [0, GetMaxUserNum() - 1]
    bool GetImageData(const int nIndex, const int nLeft, const int nTop,
        const int nRight, const int nBottom, const float fScale,
        unsigned char *pucBuffer, const int nBufferLength) const;

	unsigned char* GetCropImageDataEx(const int nID, const int nLeft, const int nTop,
		const int nRight, const int nBottom, const float fScale, const int nBufferLength);

    // get JPEG data of the whole image at a certain scale
    // nLeft, nTop, nRight and nBottom should be the ROI at the scan scale
    // pucBuffer is allocated inside and can NOT be released outside
    // nBufferLength will be changed inside to indicate the size of JPEG image data
    // user could set the JPEG compression quality
    // nIndex [0, GetMaxUserNum() - 1]
    bool GetJpegData(const int nIndex, const int nLeft, const int nTop,
        const int nRight, const int nBottom, const float fScale,
        unsigned char *&pucBuffer, int &nBufferLength, const int nQuality = 65) const;

    // get JPEG data of a tile
    // nDownsampleScale: 1, 2, 4, 8, 16, 32, 64, ..., 1 means the highest scale
    bool GetTileJpeg(const int nDownsampleScale, const int nTileRow, const int nTileCol,
        unsigned char *&pucBuffer, int &nBufferLength) const;

	const unsigned char* GetTileJpeg(const int  nLayerNum,
		const int nTileRow, const int nTileCol, unsigned long& length);

    // get bitmap data of a tile
    // nDownsampleScale: 1, 2, 4, 8, 16, 32, 64, ..., 1 means the highest scale
    unsigned char *GetTileData(const int nDownsampleScale,
        const int nTileRow, const int nTileCol) const;

	unsigned char *GetV6TileData(const int nDownsampleScale, const int nTileRow,
		const int nTileCol, bool &bOk, int nLayer) const;

    // get size of extent information
    // return 0 means there is no such information or failed
    // valid for TMAP 6.0 and below
    int GetExtSize(const EXT_TYPE eExt) const;

    // get extent information
    // pcBuffer should by allocated by user
    // nBufferLength should no less than the size returned by the above function
    // valid for TMAP 6.0 and below
    bool GetExtData(const EXT_TYPE eExt, char *pcBuffer, const int nBufferLength) const;

    // extent information management for TMAP 7.0 and above
    // add item
    bool Add(const char *pcGroup, const char *pcItem,
        const int nType, const void *pData, const int nLength);

    // remove item
    bool Remove(const char *pcGroup, const char *pcItem);

    // change item
    bool Change(const char *pcGroup, const char *pcItem,
        const int nType, const void *pData, const int nLength);

    // get item
    const void* GetInfo(const char *pcGroup, const char *pcItem, int &nType, int &nLength) const;
    const void* GetInfo(const char *pcGroup, const int nItem, int &nType, int &nLength) const;
    const void* GetInfo(const int nGroup, const int nItem, int &nType, int &nLength) const;
    const void* GetInfo(const int nGroup, const char *pcItem, int &nType, int &nLength) const;

    // clear all extent informations
    void Clear();

    // get group number
    int GetGroupCnt() const;

    // get item number
    int GetItemCnt(const char *pcGroup) const;
    int GetItemCnt(const int nGroup) const;

    // get group name
    const char *GetGroupName(const int nGroup) const;

    // get item name
    const char *GetItemName(const int nGroup, const int nItem) const;

    // crop a region and save it as the latest TMAP (7.0 or above)
    int SaveAsTmap(const char *pcNewTmap, const int nLeft, const int nTop,
        const int nRight, const int nBottom, const int nScale, const int nQuality,
        const PFCallback pfFunc, const char *stSN) const;

    int SaveAsTiff(const char *pcNewTmap, int nLeft, int nTop,
        int nRight, int nBottom, int nScale, int nQuality,
        const PFCallback pfFunc, bool bChangeRB = true) const;
    int SaveAsTiffV2(const char *pcNewTmap, int nLeft, int nTop,
        int nRight, int nBottom, int nScale, int nQuality,
        const PFCallback pfFunc, bool bChangeRB = true) const;
	bool writeLabelImgToTiff(TIFFFile& crop) const;
	bool writeMacroImgToTiff(TIFFFile& crop) const;
	bool writeThumbnailImgToTiff(TIFFFile& crop,int dir) const;
	int SaveAsTmapResize(const char *pcNewTmap, const int nLeft, const int nTop, const int nRight, const int nBottom, const int nScale,
		const int nQuality, float fZoom, const PFCallback pfFunc) const;

//     int SaveAsTmapEx(const char *pcNewTmap, const int nLeft, const int nTop,
//         const int nRight, const int nBottom, const int nScale, const int nQuality, 
//         const PFCallback pfFunc) const;

    // release some memory
    void ReleaseMemory();

    bool SetProcessParas(const void *para, const int nCount);
	float GetSaveAsTiffPercen();
 private:
    // make it private to prevent copying
    iViewerSDK(const iViewerSDK &other);

    // make it private to prevent copying
    iViewerSDK &operator=(const iViewerSDK &other);
	int saveTiffLayer(TIFFFile& crop, int nLeft, int nTop,
		int nRight, int nBottom, float scale, int layer_id, bool need_lut, int slice_type) const;


    void Initialize(void);

    class Data;
    Data *m_pD;
};



#if 0
// get JPEG data of a tile
// nDownsampleScale: 1, 2, 4, 8, 16, 32, 64, ..., 1 means the highest scale
IVIEWERSDK_API
unsigned char* GetTileJpeg(const char *pcFile, const int nDownsampleScale,
                           const int nTileRow, const int nTileCol, int &nBufferLength);

// get bitmap data of a tile
// nDownsampleScale: 1, 2, 4, 8, 16, 32, 64, ..., 1 means the highest scale
IVIEWERSDK_API
unsigned char *GetTileData(const char *pcFile, const int nDownsampleScale,
                           const int nTileRow, const int nTileCol);

IVIEWERSDK_API
void ReleaseTile(void **pData);

// get bitmap data of a tile
// nDownsampleScale: 1, 2, 4, 8, 16, 32, 64, ..., 1 means the highest scale
IVIEWERSDK_API
bool GetTileData(const char *pcFile, const int nDownsampleScale, 
                 const int nTileRow, const int nTileCol, unsigned char *pucRGB);
#endif

#endif // __IVIEWERSDK__
