/*
 */

#ifndef __TGT_H
#define __TGT_H

typedef struct tgt_node {
	struct tgt_node *parent;
	int value;
	int low;
	int known;
} tgt_node_t;

typedef struct {
	int numleafsh;
	int numleafsv;
	int numnodes;
	tgt_node_t *nodes;
} tgt_tree_t;

void tgt_reset(tgt_tree_t *tree);
tgt_tree_t *tgt_create(int numleafsh, int numleafsv);
void tgt_destroy(tgt_tree_t *t);
void tgt_setvalue(tgt_tree_t *tree, int leafno, int value);
void tgt_encode(tgt_tree_t *tree, int leafno, int threshold);
int tgt_decode(tgt_tree_t *tree, int leafno, int threshold);

#endif
