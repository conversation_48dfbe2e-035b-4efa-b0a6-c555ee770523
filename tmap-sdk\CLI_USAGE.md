# TMAP CLI 工具使用指南

TMAP CLI 是一个命令行工具，用于快速分析TMAP文件并提取图像。

## 功能特性

- ✅ **文件信息分析** - 显示TMAP文件的详细信息
- ✅ **图像自动提取** - 提取缩略图、导航图、宏观图、标签图等
- ✅ **瓦片示例提取** - 提取示例瓦片数据
- ✅ **彩色输出** - 友好的命令行界面
- ✅ **性能统计** - 显示处理时间和文件大小
- ✅ **自动目录创建** - 自动创建images文件夹保存图片

## 安装和使用

### 方法1: 直接使用Node.js

```bash
# 确保项目已构建
npm run build

# 使用CLI工具
node cli.js <tmap-file-path>
```

### 方法2: 使用npm脚本

```bash
# 使用npm脚本
npm run cli <tmap-file-path>
```

### 方法3: 全局安装（可选）

```bash
# 全局安装
npm install -g .

# 全局使用
tmap-cli <tmap-file-path>
```

## 使用示例

### Windows系统

```bash
# 使用绝对路径
node cli.js "E:\TMAP\Test_1.TMAP"

# 使用npm脚本
npm run cli "C:\path\to\your\file.tmap"

# 使用相对路径
node cli.js ".\test-files\sample.tmap"
```

### Linux/macOS系统

```bash
# 使用绝对路径
node cli.js "/home/<USER>/tmap-files/Test_1.TMAP"

# 使用npm脚本
npm run cli "/path/to/your/file.tmap"

# 使用相对路径
node cli.js "./test-files/sample.tmap"
```

## 输出说明

### 1. 基本信息
CLI工具会显示以下TMAP文件信息：
- 版本号
- 图像尺寸（宽×高）
- 像素大小（μm/pixel）
- 扫描倍率
- 扫描层数
- 焦点数量
- 瓦片总数
- 压缩算法和质量

### 2. 层级信息
对于支持的版本，会显示：
- 层级ID和缩放比例
- 每层的图像尺寸
- 瓦片网格信息

### 3. 图像提取
自动提取以下类型的图像：
- **缩略图** (thumbnail.jpg)
- **导航图** (navigate.jpg)
- **宏观图** (macro.jpg)
- **标签图** (label.jpg)
- **宏观标签图** (macro-label.jpg)

### 4. 瓦片示例
提取前几个瓦片作为示例：
- **瓦片文件** (tile_0_0_0.jpg, tile_0_0_1.jpg, 等)

## 输出目录

所有提取的图片都保存在项目根目录的 `images/` 文件夹中：

```
tmap-sdk/
├── images/                 # 自动创建的图片目录
│   ├── thumbnail.jpg       # 缩略图
│   ├── navigate.jpg        # 导航图
│   ├── macro.jpg          # 宏观图
│   ├── label.jpg          # 标签图
│   ├── macro-label.jpg    # 宏观标签图
│   ├── tile_0_0_0.jpg     # 瓦片示例
│   └── ...
├── cli.js                 # CLI工具
└── ...
```

## 示例输出

```
=== TMAP 文件分析工具 ===

📁 文件路径: E:\TMAP\Test_1.TMAP
📊 文件大小: 1.25 GB

⏳ 正在打开TMAP文件...
✓ 文件打开成功 (1.23s)

=== 基本信息 ===
版本号:     07
图像尺寸:   46000 × 32000 像素
像素大小:   0.25 μm/pixel
扫描倍率:   40X
扫描层数:   5
焦点数量:   21
瓦片总数:   2304
压缩算法:   1 (JPEG)
压缩质量:   85

=== 层级信息 ===
层级 0:
  - ID: 0
  - 缩放比例: 1
  - 尺寸: 46000 × 32000
  - 瓦片网格: 180 × 125 = 22500 个瓦片

=== 图像提取 ===
⏳ 正在提取缩略图...
✓ 缩略图已保存: thumbnail.jpg (45.2 KB)
⏳ 正在提取导航图...
✓ 导航图已保存: navigate.jpg (128.5 KB)
⚠ 宏观图不存在或为空
⚠ 标签图不存在或为空
⚠ 宏观标签图不存在或为空

✓ 图像提取完成: 2/5 个图像成功 (2.45s)

=== 瓦片提取示例 ===
⏳ 正在提取示例瓦片...
✓ 瓦片已保存: tile_0_0_0.jpg (15.8 KB)
✓ 瓦片已保存: tile_0_0_1.jpg (16.2 KB)
✓ 瓦片已保存: tile_0_1_0.jpg (14.9 KB)
✓ 瓦片已保存: tile_0_1_1.jpg (15.5 KB)
✓ 瓦片提取完成: 4/4 个瓦片成功

=== 处理完成 ===
✓ 总处理时间: 4.12s
✓ 图片保存目录: D:\iviewersdk\tmap-sdk\images

📁 已保存的文件:
  - thumbnail.jpg (45.2 KB)
  - navigate.jpg (128.5 KB)
  - tile_0_0_0.jpg (15.8 KB)
  - tile_0_0_1.jpg (16.2 KB)
  - tile_0_1_0.jpg (14.9 KB)
  - tile_0_1_1.jpg (15.5 KB)
```

## 错误处理

### 常见错误和解决方案

1. **文件不存在**
   ```
   ❌ 错误: 文件不存在: path/to/file.tmap
   ```
   - 检查文件路径是否正确
   - 确保文件确实存在

2. **文件格式错误**
   ```
   ❌ 处理失败: Invalid TMAP header
   ```
   - 确保文件是有效的TMAP格式
   - 检查文件是否损坏

3. **版本不支持**
   ```
   ❌ 处理失败: Unsupported TMAP version
   ```
   - 当前主要支持TMAP 7.x版本
   - 旧版本功能有限

4. **权限问题**
   ```
   ❌ 处理失败: Permission denied
   ```
   - 确保有读取TMAP文件的权限
   - 确保有写入images目录的权限

## 性能说明

- **小文件** (< 100MB): 通常几秒内完成
- **中等文件** (100MB - 1GB): 可能需要10-30秒
- **大文件** (> 1GB): 可能需要1-5分钟

处理时间主要取决于：
- 文件大小
- 图像数量和复杂度
- 磁盘I/O性能
- 系统内存

## 注意事项

1. **内存使用**: 处理大文件时注意系统内存使用
2. **磁盘空间**: 确保有足够空间保存提取的图片
3. **文件路径**: Windows系统建议使用双引号包围路径
4. **版本兼容**: 某些功能仅支持TMAP 7.x版本

## 故障排除

如果遇到问题，请：

1. 确保项目已正确构建：`npm run build`
2. 检查Node.js版本：`node --version` (建议 >= 16.0.0)
3. 检查文件路径和权限
4. 查看详细错误信息
5. 尝试使用较小的测试文件

## 扩展功能

CLI工具可以轻松扩展，例如：
- 添加更多输出格式
- 支持批量处理
- 添加配置文件支持
- 集成到CI/CD流程中
