# TMAP SDK

一个用于解析TMAP医学图像文件的Node.js/TypeScript SDK。TMAP是一种专有的医学病理图像格式，支持多层级瓦片结构，广泛用于数字病理学应用。

## 功能特性

- ✅ **多版本支持** - 支持TMAP 2.x - 7.x版本
- ✅ **自动版本检测** - 自动识别TMAP文件版本并选择合适的解析器
- ✅ **版本信息获取** - 获取TMAP文件版本号
- ✅ **图像信息提取** - 获取图像尺寸、像素大小、扫描倍率等
- ✅ **多类型图像提取** - 支持缩略图、导航图、宏观图、标签图、宏观标签图
- ✅ **瓦片数据访问** - 获取指定层级和坐标的瓦片数据
- ✅ **批量瓦片获取** - 支持批量获取多个瓦片数据
- ✅ **瓦片信息查询** - 获取层级瓦片信息和存在性检查
- ✅ **多种输出格式** - 支持JPEG、PNG、RAW等格式输出
- ✅ **命令行工具** - 提供CLI工具快速分析TMAP文件并提取图像
- ✅ **TypeScript支持** - 完整的类型定义
- ✅ **向后兼容** - 支持旧版本TMAP文件格式

## 安装

```bash
npm install tmap-sdk
```

## 快速开始

### 基本使用

```typescript
import { TmapSDK } from 'tmap-sdk';

async function main() {
  // 创建SDK实例
  const sdk = new TmapSDK('path/to/your/file.tmap');
  
  try {
    // 打开TMAP文件
    await sdk.open();
    
    // 获取基本信息
    const info = sdk.getBasicInfo();
    console.log('版本:', info.version);
    console.log('图像尺寸:', info.imageSize);
    console.log('像素大小:', info.pixelSize);
    console.log('扫描倍率:', info.scanScale);
    console.log('层数:', info.layerCount);
    
    // 提取缩略图
    const thumbnail = await sdk.getThumbnail('jpeg');
    if (thumbnail) {
      require('fs').writeFileSync('thumbnail.jpg', thumbnail);
    }
    
    // 提取瓦片
    const tile = await sdk.getTileData(0, 0, 0, 'jpeg');
    if (tile) {
      require('fs').writeFileSync('tile.jpg', tile);
    }

    // 批量获取瓦片
    const tileRequests = [
      { layer: 0, row: 0, col: 0, format: 'jpeg' as const },
      { layer: 0, row: 0, col: 1, format: 'jpeg' as const },
      { layer: 0, row: 1, col: 0, format: 'jpeg' as const }
    ];
    const tiles = await sdk.getTileDataBatch(tileRequests);

    // 获取层级瓦片信息
    const layerInfo = sdk.getLayerTileInfo(0);
    if (layerInfo) {
      console.log(`层级0有 ${layerInfo.totalTiles} 个瓦片`);
      console.log(`瓦片网格: ${layerInfo.tileCols} x ${layerInfo.tileRows}`);
    }

    // 检查瓦片是否存在
    const exists = await sdk.tileExists(0, 0, 0);
    console.log('瓦片存在:', exists);

  } finally {
    // 关闭文件
    sdk.close();
  }
}
```

### 瓦片数据操作

```typescript
import { TmapSDK, TileRequest } from 'tmap-sdk';

async function tileOperations() {
  const sdk = new TmapSDK('path/to/your/file.tmap');

  await sdk.open();

  // 获取单个瓦片
  const tile = await sdk.getTileData(0, 0, 0, 'jpeg');

  // 批量获取瓦片
  const requests: TileRequest[] = [
    { layer: 0, row: 0, col: 0, format: 'jpeg' },
    { layer: 0, row: 0, col: 1, format: 'png' },
    { layer: 1, row: 0, col: 0, format: 'raw' }
  ];
  const tiles = await sdk.getTileDataBatch(requests);

  // 获取层级信息
  const layerInfo = sdk.getLayerTileInfo(0);
  console.log('层级0瓦片信息:', layerInfo);

  // 检查瓦片存在性
  const exists = await sdk.tileExists(0, 0, 0);
  console.log('瓦片存在:', exists);

  sdk.close();
}
```

### 命令行工具

TMAP SDK 提供了一个命令行工具，可以快速分析TMAP文件并提取图像：

```bash
# 使用CLI工具分析TMAP文件
npm run cli "path/to/your/file.tmap"

# 或者直接使用Node.js
node cli.js "path/to/your/file.tmap"

# 示例
npm run cli "E:\TMAP\Test_1.TMAP"
```

CLI工具会：
- 显示TMAP文件的详细信息
- 自动提取所有可用的图像（缩略图、导航图、宏观图等）
- 提取示例瓦片数据
- 将所有图片保存到 `images/` 文件夹

详细使用说明请参考 [CLI_USAGE.md](CLI_USAGE.md)。

## API 文档

### TmapSDK 类

#### 构造函数
```typescript
constructor(tmapFilePath: string)
```

#### 方法

##### 文件操作
- `open(): Promise<void>` - 打开TMAP文件
- `close(): void` - 关闭TMAP文件

##### 信息获取
- `getVersion(): string` - 获取版本号
- `getImageSize(): {width: number, height: number}` - 获取图像尺寸
- `getPixelSize(): number` - 获取像素大小 (μm/pixel)
- `getScanScale(): number` - 获取扫描倍率
- `getLayerCount(): number` - 获取扫描层数
- `getFocusCount(): number` - 获取焦点数量
- `getTileCount(): number` - 获取瓦片总数
- `getBasicInfo(): TmapBasicInfo` - 获取完整基本信息

##### 图像提取
- `getThumbnail(format?: 'jpeg' | 'png'): Promise<Buffer | null>` - 获取缩略图
- `getNavigateImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>` - 获取导航图
- `getMacroImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>` - 获取宏观图
- `getLabelImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>` - 获取标签图
- `getMacroLabelImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>` - 获取宏观标签图

##### 瓦片操作
- `getTileData(layer: number, row: number, col: number, format?: 'raw' | 'jpeg' | 'png', focusId?: number): Promise<Buffer | null>` - 获取瓦片数据
- `getTileDataBatch(requests: TileRequest[]): Promise<(Buffer | null)[]>` - 批量获取瓦片数据
- `getLayerTileInfo(layerId: number): {totalTiles: number, tileRows: number, tileCols: number} | null` - 获取层级瓦片信息
- `tileExists(layer: number, row: number, col: number): Promise<boolean>` - 检查瓦片是否存在

### 瓦片请求接口

```typescript
interface TileRequest {
  layer: number;            // 层级
  row: number;              // 行
  col: number;              // 列
  focusId?: number;         // 焦点ID (可选)
  format?: 'raw' | 'jpeg' | 'png'; // 输出格式
}
```

## 开发

### 安装依赖

```bash
npm install
```

### 构建

```bash
npm run build
```

### 运行测试

```bash
npm test
```

### 运行示例

```bash
# 更新 example.ts 中的文件路径
npm run dev
```

## 支持的TMAP版本

| 版本 | 支持状态 | 功能说明 |
|------|----------|----------|
| TMAP 7.x | ✅ 完全支持 | 所有功能，包括图像提取、瓦片服务、压缩算法等 |
| TMAP 6.x | ✅ 基本支持 | 基本信息读取，图像提取功能有限 |
| TMAP 5.x | ✅ 基本支持 | 基本信息读取，扩展信息支持 |
| TMAP 4.x | ⚠️ 部分支持 | 基本信息读取，融合层支持 |
| TMAP 3.x | ⚠️ 部分支持 | 基本信息读取，融合层支持 |
| TMAP 2.x | ⚠️ 部分支持 | 基本信息读取 |

### 版本特性对比

- **TMAP 7.x**: 最新版本，支持完整的压缩算法、OCR、条码信息等
- **TMAP 5.x/6.x**: 支持扩展信息、多文件结构
- **TMAP 2.x-4.x**: 基础版本，支持基本的瓦片结构

## 依赖项

- **sharp** - 图像处理（可选，用于图像格式转换）

## 注意事项

1. **DT文件支持**: TMAP文件可能有对应的DT附属文件，用于存储超过2GB的数据。当前版本会自动处理DT文件。

2. **内存使用**: 处理大型TMAP文件时请注意内存使用情况，特别是批量获取瓦片时。

3. **压缩格式**: 支持JPEG压缩，J2K压缩格式正在开发中。

4. **文件路径**: 确保TMAP文件路径正确且文件可读。

5. **瓦片坐标**: 瓦片坐标从0开始，请确保坐标在有效范围内。

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 项目结构

```
tmap-sdk/
├── src/
│   ├── types/              # TypeScript类型定义
│   │   ├── tmap-structures.ts
│   │   └── index.ts
│   ├── core/               # 核心功能模块
│   │   ├── tmap-reader.ts  # TMAP文件读取器
│   │   ├── image-extractor.ts # 图像提取器
│   │   └── tile-server.ts  # 瓦片服务器
│   ├── utils/              # 工具类
│   │   ├── buffer-reader.ts # 二进制数据读取
│   │   └── compression.ts  # 压缩/解压缩
│   └── index.ts            # 主入口文件
├── tests/                  # 测试文件
├── dist/                   # 编译输出
├── example.ts              # 使用示例
├── demo.ts                 # 功能演示
├── example.html            # Web示例
└── README.md
```

## 快速测试

如果您没有TMAP文件，可以运行演示程序查看SDK功能：

```bash
npm run demo
```

如果您有TMAP文件，请更新 `example.ts` 中的文件路径：

```bash
npm run dev
```

## Web示例

1. 启动TMAP服务器（需要真实的TMAP文件）
2. 在浏览器中打开 `example.html`
3. 查看OpenSeadragon集成效果

## 更新日志

### v1.0.0
- 初始版本发布
- 支持TMAP 7.x格式
- 基本图像提取功能
- OpenSeadragon瓦片服务器
- 完整的TypeScript类型定义
- Web API接口
- 示例和文档
