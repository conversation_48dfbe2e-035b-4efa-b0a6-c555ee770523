# TMAP SDK 瓦片API文档

本文档详细说明了TMAP SDK中瓦片操作相关的API接口。

## 概述

TMAP SDK提供了强大的瓦片数据访问功能，支持：
- 单个瓦片获取
- 批量瓦片获取
- 瓦片信息查询
- 瓦片存在性检查
- 多种输出格式
- 焦点ID支持

## 核心API

### 1. getTileData()

获取单个瓦片数据。

```typescript
async getTileData(
  layer: number,           // 层级（从0开始）
  row: number,             // 行（从0开始）
  col: number,             // 列（从0开始）
  format: 'raw' | 'jpeg' | 'png' = 'jpeg',  // 输出格式
  focusId: number = 0      // 焦点ID（-10到10）
): Promise<Buffer | null>
```

**示例：**
```typescript
// 获取第0层，第0行，第0列的瓦片，JPEG格式
const tile = await sdk.getTileData(0, 0, 0, 'jpeg');

// 获取指定焦点的瓦片
const focusedTile = await sdk.getTileData(0, 0, 0, 'png', 5);
```

### 2. getTileDataBatch()

批量获取多个瓦片数据。

```typescript
async getTileDataBatch(requests: TileRequest[]): Promise<(Buffer | null)[]>
```

**TileRequest接口：**
```typescript
interface TileRequest {
  layer: number;            // 层级
  row: number;              // 行
  col: number;              // 列
  focusId?: number;         // 焦点ID (可选)
  format?: 'raw' | 'jpeg' | 'png'; // 输出格式
}
```

**示例：**
```typescript
const requests: TileRequest[] = [
  { layer: 0, row: 0, col: 0, format: 'jpeg' },
  { layer: 0, row: 0, col: 1, format: 'png' },
  { layer: 1, row: 0, col: 0, format: 'raw', focusId: 3 }
];

const tiles = await sdk.getTileDataBatch(requests);
// tiles数组包含对应的瓦片数据，失败的为null
```

### 3. getLayerTileInfo()

获取指定层级的瓦片信息。

```typescript
getLayerTileInfo(layerId: number): {
  totalTiles: number;       // 总瓦片数
  tileRows: number;         // 瓦片行数
  tileCols: number;         // 瓦片列数
} | null
```

**示例：**
```typescript
const layerInfo = sdk.getLayerTileInfo(0);
if (layerInfo) {
  console.log(`层级0有 ${layerInfo.totalTiles} 个瓦片`);
  console.log(`瓦片网格: ${layerInfo.tileCols} x ${layerInfo.tileRows}`);
}
```

### 4. tileExists()

检查指定瓦片是否存在。

```typescript
async tileExists(layer: number, row: number, col: number): Promise<boolean>
```

**示例：**
```typescript
const exists = await sdk.tileExists(0, 0, 0);
if (exists) {
  console.log('瓦片存在，可以获取');
} else {
  console.log('瓦片不存在或为空');
}
```

## 使用示例

### 基本瓦片获取

```typescript
import { TmapSDK } from 'tmap-sdk';

async function basicTileExample() {
  const sdk = new TmapSDK('path/to/file.tmap');
  
  try {
    await sdk.open();
    
    // 获取单个瓦片
    const tile = await sdk.getTileData(0, 0, 0, 'jpeg');
    if (tile) {
      // 保存瓦片到文件
      require('fs').writeFileSync('tile_0_0_0.jpg', tile);
    }
    
  } finally {
    sdk.close();
  }
}
```

### 批量瓦片处理

```typescript
async function batchTileExample() {
  const sdk = new TmapSDK('path/to/file.tmap');
  
  try {
    await sdk.open();
    
    // 获取层级信息
    const layerInfo = sdk.getLayerTileInfo(0);
    if (!layerInfo) return;
    
    // 构建瓦片请求列表
    const requests: TileRequest[] = [];
    for (let row = 0; row < Math.min(layerInfo.tileRows, 3); row++) {
      for (let col = 0; col < Math.min(layerInfo.tileCols, 3); col++) {
        requests.push({
          layer: 0,
          row,
          col,
          format: 'jpeg'
        });
      }
    }
    
    // 批量获取瓦片
    const tiles = await sdk.getTileDataBatch(requests);
    
    // 处理结果
    tiles.forEach((tile, index) => {
      if (tile) {
        const request = requests[index];
        const filename = `tile_${request.layer}_${request.row}_${request.col}.jpg`;
        require('fs').writeFileSync(filename, tile);
        console.log(`保存瓦片: ${filename}`);
      }
    });
    
  } finally {
    sdk.close();
  }
}
```

### 瓦片遍历

```typescript
async function traverseTiles() {
  const sdk = new TmapSDK('path/to/file.tmap');
  
  try {
    await sdk.open();
    
    // 遍历所有层级
    const basicInfo = sdk.getBasicInfo();
    for (let layer = 0; layer < basicInfo.layerCount; layer++) {
      const layerInfo = sdk.getLayerTileInfo(layer);
      if (!layerInfo) continue;
      
      console.log(`层级 ${layer}: ${layerInfo.tileCols}x${layerInfo.tileRows} = ${layerInfo.totalTiles} 瓦片`);
      
      // 检查几个瓦片的存在性
      for (let row = 0; row < Math.min(layerInfo.tileRows, 2); row++) {
        for (let col = 0; col < Math.min(layerInfo.tileCols, 2); col++) {
          const exists = await sdk.tileExists(layer, row, col);
          console.log(`  瓦片(${row},${col}): ${exists ? '存在' : '不存在'}`);
        }
      }
    }
    
  } finally {
    sdk.close();
  }
}
```

## 性能优化建议

### 1. 批量操作
- 优先使用 `getTileDataBatch()` 而不是多次调用 `getTileData()`
- 批量大小建议控制在50-100个瓦片以内

### 2. 存在性检查
- 在获取瓦片前使用 `tileExists()` 检查可以避免不必要的处理
- 对于已知存在的瓦片，可以跳过检查

### 3. 内存管理
- 及时处理获取的瓦片数据，避免内存积累
- 对于大批量操作，考虑分批处理

### 4. 错误处理
```typescript
try {
  const tile = await sdk.getTileData(layer, row, col);
  if (tile) {
    // 处理瓦片数据
  } else {
    console.log('瓦片为空或不存在');
  }
} catch (error) {
  console.error('获取瓦片失败:', error);
}
```

## 注意事项

1. **坐标系统**: 瓦片坐标从(0,0)开始，行和列都是从0开始计数
2. **层级顺序**: 层级0通常是最高分辨率，数字越大分辨率越低
3. **焦点ID**: 范围是-10到10，0表示默认焦点
4. **格式支持**: 
   - `raw`: 原始压缩数据
   - `jpeg`: JPEG格式（推荐用于显示）
   - `png`: PNG格式（支持透明度）
5. **版本兼容**: 瓦片操作功能仅支持TMAP 7.x版本

## 错误码

| 错误类型 | 说明 | 解决方案 |
|----------|------|----------|
| `Invalid layer ID` | 层级ID超出范围 | 检查层级数量 |
| `Invalid tile coordinates` | 瓦片坐标超出范围 | 检查瓦片网格大小 |
| `Tile index out of range` | 瓦片索引超出范围 | 检查瓦片总数 |
| `TMAP file not loaded` | 文件未加载 | 先调用 `open()` |
| `Unsupported TMAP version` | 版本不支持 | 使用TMAP 7.x文件 |
