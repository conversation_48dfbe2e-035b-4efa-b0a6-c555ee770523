# TMAP SDK 故障排除指南

本文档帮助您解决使用TMAP SDK时遇到的常见问题。

## 常见错误及解决方案

### 1. "Not enough bytes to read Int32LE"

**错误描述**:
```
处理失败: Failed to open TMAP file: Error: Not enough bytes to read Int32LE at position XXXXX
```

**可能原因**:
- 文件损坏或不完整
- 文件格式不正确
- 文件头部结构与预期不匹配
- 文件大小小于最小要求
- TMAP 5.x/6.x文件的图像数据不完整

**解决步骤**:

1. **检查文件完整性**:
   ```bash
   # 使用调试工具检查文件
   npm run debug "path/to/your/file.tmap"
   ```

2. **验证文件大小**:
   - TMAP 7.x文件最小应为256字节（头部大小）
   - TMAP 5.x/6.x文件最小应为128字节
   - 检查文件是否在传输过程中被截断

3. **检查文件标识**:
   - 文件前4个字节应为"TMAP"
   - 第5-6字节应为版本号（如"07", "06", "05"等）

4. **TMAP 6.x特殊情况**:
   - 如果错误发生在位置206848附近，这通常是TMAP 6.x文件的图像数据区域
   - SDK现在会自动检测并处理不完整的图像数据
   - 查看控制台输出中的警告信息，了解实际读取的数据量

5. **尝试不同版本**:
   - 如果是TMAP 7.x文件，确保文件完整
   - 如果是旧版本文件，检查是否支持

**修复状态**: ✅ 已修复
- SDK现在能够优雅地处理不完整的TMAP 5.x/6.x文件
- 提供详细的警告信息而不是崩溃
- 自动调整读取的数据量以匹配实际文件大小

### 1.1 TMAP 6.x 图像尺寸和扫描倍率问题

**错误描述**:
```
图像尺寸错误: 得到 3105 × 4522024，应该是 109066 × 58476
扫描倍率错误: 得到 10X，应该是 40X
```

**解决方案**: ✅ 已修复
- 修正了TMAP 6.x的扫描倍率字段映射（使用ratioStep而不是maxLayerSize）
- 改进了图像尺寸计算算法，支持从瓦片信息推算真实尺寸
- 添加了多种尺寸计算策略的备选方案

**测试方法**:
```bash
# 测试TMAP 6.x文件的完整功能
npm run test:tmap6 "path/to/your/tmap6-file.tmap"
```

### 2. "Unsupported TMAP version"

**错误描述**:
```
处理失败: Failed to open TMAP file: Error: Unsupported TMAP version: XX
```

**解决方案**:
- 检查文件版本是否在支持范围内（2.x - 7.x）
- 使用调试工具查看实际版本号
- 确认文件不是其他格式的文件

### 3. "TMAP file not found"

**错误描述**:
```
处理失败: Failed to open TMAP file: Error: TMAP file not found: path
```

**解决方案**:
- 检查文件路径是否正确
- 确保文件确实存在
- 在Windows系统中使用双引号包围路径
- 检查文件权限

### 4. "File too small for TMAP format"

**错误描述**:
```
File too small for TMAP 7.x format. Expected at least 256 bytes, got XX bytes
```

**解决方案**:
- 检查文件是否完整下载/复制
- 确认文件没有被截断
- 验证文件来源的可靠性

### 5. "Failed to read complete header"

**错误描述**:
```
Failed to read complete header. Expected 256 bytes, read XX bytes
```

**解决方案**:
- 文件可能被其他程序占用
- 检查磁盘空间和权限
- 尝试复制文件到其他位置

## 调试工具使用

### 基本调试

使用内置的调试工具检查文件：

```bash
# 调试TMAP文件
npm run debug "path/to/your/file.tmap"
```

调试工具会显示：
- 文件基本信息（大小、路径）
- 文件头部十六进制内容
- TMAP标识和版本号
- 详细的错误信息

### 高级调试

如果基本调试无法解决问题，可以：

1. **检查文件十六进制内容**:
   ```bash
   # Windows
   certutil -encodehex file.tmap file.hex 1
   
   # Linux/macOS
   hexdump -C file.tmap | head -20
   ```

2. **验证文件结构**:
   - 前4字节应为: `54 4D 41 50` ("TMAP")
   - 第5-6字节为版本号的ASCII码
   - 检查是否有明显的数据损坏

## 版本兼容性问题

### TMAP 7.x 文件

- **完全支持**: 所有功能可用
- **最小文件大小**: 256字节
- **头部结构**: 固定256字节

### TMAP 5.x/6.x 文件

- **基本支持**: 信息读取功能
- **最小文件大小**: 128字节
- **限制**: 图像提取和瓦片操作功能有限

### TMAP 2.x-4.x 文件

- **部分支持**: 仅基本信息读取
- **建议**: 升级到新版本格式

## 性能问题

### 大文件处理慢

**解决方案**:
- 确保有足够的系统内存
- 避免同时处理多个大文件
- 考虑分批处理瓦片数据

### 内存使用过高

**解决方案**:
- 使用批量瓦片获取时控制批次大小
- 及时释放不需要的图像数据
- 监控系统内存使用情况

## 环境问题

### Node.js 版本

**要求**: Node.js >= 16.0.0

**检查版本**:
```bash
node --version
```

### 依赖问题

**解决方案**:
```bash
# 重新安装依赖
npm install

# 清理并重新构建
npm run build
```

### Sharp 库问题

如果遇到Sharp相关错误：

```bash
# 重新安装Sharp
npm uninstall sharp
npm install sharp

# 或者使用预编译版本
npm install --platform=win32 --arch=x64 sharp
```

## 获取帮助

### 收集调试信息

在报告问题时，请提供：

1. **系统信息**:
   - 操作系统版本
   - Node.js版本
   - TMAP SDK版本

2. **文件信息**:
   - 文件大小
   - TMAP版本
   - 文件来源

3. **错误信息**:
   - 完整的错误消息
   - 调试工具输出
   - 重现步骤

### 调试命令

```bash
# 检查环境
node --version
npm --version

# 检查项目状态
npm run build
npm run test

# 调试特定文件
npm run debug "problematic-file.tmap"
```

### 常用检查清单

- [ ] 文件路径正确且文件存在
- [ ] 文件大小合理（> 256字节用于7.x）
- [ ] 文件前4字节为"TMAP"
- [ ] Node.js版本 >= 16.0.0
- [ ] 项目已正确构建
- [ ] 依赖已正确安装
- [ ] 有足够的系统内存
- [ ] 文件权限正确

## 联系支持

如果以上方法都无法解决问题，请：

1. 使用调试工具收集详细信息
2. 准备最小重现案例
3. 提供完整的错误日志
4. 说明预期行为和实际行为的差异

这将帮助快速定位和解决问题。
