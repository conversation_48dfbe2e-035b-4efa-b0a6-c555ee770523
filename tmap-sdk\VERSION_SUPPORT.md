# TMAP 版本支持文档

本文档详细说明了TMAP SDK对不同TMAP版本的支持情况。

## 版本支持概览

| 版本 | 支持状态 | 解析器 | 功能完整度 | 说明 |
|------|----------|--------|------------|------|
| TMAP 7.x | ✅ 完全支持 | V7解析器 | 100% | 所有功能完整支持 |
| TMAP 6.x | ✅ 基本支持 | V5解析器 | 60% | 基本信息读取，图像提取有限 |
| TMAP 5.x | ✅ 基本支持 | V5解析器 | 60% | 基本信息读取，扩展信息支持 |
| TMAP 4.x | ⚠️ 部分支持 | V5解析器 | 40% | 基本信息读取，融合层支持 |
| TMAP 3.x | ⚠️ 部分支持 | V5解析器 | 40% | 基本信息读取，融合层支持 |
| TMAP 2.x | ⚠️ 部分支持 | V5解析器 | 30% | 基本信息读取 |

## 功能支持矩阵

### 基本功能

| 功能 | 7.x | 6.x | 5.x | 4.x | 3.x | 2.x |
|------|-----|-----|-----|-----|-----|-----|
| 版本检测 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| 文件验证 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| 基本信息读取 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| 图像尺寸获取 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| 像素大小获取 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |

### 高级功能

| 功能 | 7.x | 6.x | 5.x | 4.x | 3.x | 2.x |
|------|-----|-----|-----|-----|-----|-----|
| 图像提取 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| 瓦片提取 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| 压缩算法支持 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| OCR信息 | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ |
| 条码信息 | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ |
| 扩展信息 | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ |

### 瓦片操作功能

| 功能 | 7.x | 6.x | 5.x | 4.x | 3.x | 2.x |
|------|-----|-----|-----|-----|-----|-----|
| 单个瓦片获取 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| 批量瓦片获取 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| 瓦片信息查询 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| 瓦片存在性检查 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |

## 版本特性详解

### TMAP 7.x（完全支持）
- **解析器**: TmapReader + TmapReaderV7
- **数据结构**: TmapInfo7, TmapHeader7, ImageInfo7, TileInfo7
- **特性**:
  - 完整的压缩算法支持（JPEG, J2K, Quick）
  - 完整的图像类型支持
  - 瓦片数据提取（单个和批量）
  - 瓦片信息查询和存在性检查
  - OCR和条码信息
  - 多种输出格式（JPEG, PNG, RAW）
  - 焦点ID支持

### TMAP 5.x/6.x（基本支持）
- **解析器**: TmapReaderV5
- **数据结构**: TmapInfo5, TmapHeader5, ImageInfo5, TileInfo5
- **特性**:
  - 基本信息读取
  - 扩展信息支持（5.x+）
  - 多文件结构支持
  - 缩略瓦片信息
- **限制**:
  - 图像提取功能有限
  - 不支持瓦片操作
  - 压缩算法支持有限

### TMAP 2.x-4.x（部分支持）
- **解析器**: TmapReaderV5（兼容模式）
- **数据结构**: TmapInfo5（简化）
- **特性**:
  - 基本信息读取
  - 融合层支持（3.x+）
- **限制**:
  - 功能非常有限
  - 主要用于信息查看
  - 不支持图像提取
  - 不支持瓦片操作

## 自动版本检测

SDK使用`VersionDetector`类自动检测TMAP文件版本：

```typescript
const versionInfo = VersionDetector.detectVersion(filePath);
console.log(`版本: ${versionInfo.version}`);
console.log(`支持状态: ${versionInfo.isSupported}`);
console.log(`解析器类型: ${versionInfo.parserType}`);
```

## 版本兼容性检查

```typescript
const isCompatible = VersionDetector.isVersionCompatible(
  TmapVersion.VERSION_6, 
  ['basic_tiles', 'ext_info']
);
```

## 使用建议

### 对于TMAP 7.x文件
- 使用所有功能
- 推荐用于生产环境
- 支持完整的瓦片操作和图像提取

### 对于TMAP 5.x/6.x文件
- 主要用于信息查看
- 可以获取基本的文件信息
- 适合文件分析和元数据提取

### 对于TMAP 2.x-4.x文件
- 仅用于基本信息查看
- 建议升级到新版本格式
- 功能非常有限

## 迁移指南

如果您有旧版本的TMAP文件，建议：

1. **评估需求**: 确定需要哪些功能
2. **版本检测**: 使用SDK检测当前文件版本
3. **功能限制**: 了解当前版本的功能限制
4. **升级考虑**: 如需完整功能，考虑升级文件格式

## 开发注意事项

### 类型安全
- 使用联合类型处理多版本数据结构
- 运行时版本检查
- 类型断言用于版本特定功能

### 错误处理
- 版本不支持时的优雅降级
- 功能不可用时的警告信息
- 详细的错误消息

### 性能考虑
- 版本检测缓存
- 按需加载解析器
- 内存优化

## 未来计划

- 完善5.x/6.x版本的图像提取功能
- 增加更多压缩算法支持
- 优化旧版本的兼容性
- 增加版本转换工具
