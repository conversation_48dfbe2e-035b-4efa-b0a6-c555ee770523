#!/usr/bin/env node

/**
 * TMAP CLI 工具
 * 命令行调用TMAP SDK，输出基本信息并保存图片
 * 
 * 使用方法:
 * node cli.js <tmap-file-path>
 * 或者: npm run cli <tmap-file-path>
 */

const { TmapSDK } = require('./dist/index.js');
const fs = require('fs');
const path = require('path');

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(color + message + colors.reset);
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDuration(ms) {
  if (ms < 1000) return `${ms}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
}

async function main() {
  // 检查命令行参数
  const args = process.argv.slice(2);
  if (args.length === 0) {
    colorLog(colors.red, '❌ 错误: 请提供TMAP文件路径');
    console.log('');
    console.log('使用方法:');
    console.log('  node cli.js <tmap-file-path>');
    console.log('  npm run cli <tmap-file-path>');
    console.log('');
    console.log('示例:');
    console.log('  node cli.js E:\\TMAP\\Test_1.TMAP');
    console.log('  npm run cli "C:\\path\\to\\file.tmap"');
    process.exit(1);
  }

  const tmapFilePath = args[0];

  // 检查文件是否存在
  if (!fs.existsSync(tmapFilePath)) {
    colorLog(colors.red, `❌ 错误: 文件不存在: ${tmapFilePath}`);
    process.exit(1);
  }

  // 创建image文件夹
  const imageDir = path.join(__dirname, 'images');
  if (!fs.existsSync(imageDir)) {
    fs.mkdirSync(imageDir, { recursive: true });
    colorLog(colors.green, `✓ 创建图片目录: ${imageDir}`);
  }

  colorLog(colors.cyan, '=== TMAP 文件分析工具 ===');
  console.log('');
  colorLog(colors.blue, `📁 文件路径: ${tmapFilePath}`);
  
  // 获取文件大小
  const fileStats = fs.statSync(tmapFilePath);
  colorLog(colors.blue, `📊 文件大小: ${formatBytes(fileStats.size)}`);
  console.log('');

  const sdk = new TmapSDK(tmapFilePath);
  const startTime = Date.now();

  try {
    // 打开TMAP文件
    colorLog(colors.yellow, '⏳ 正在打开TMAP文件...');
    await sdk.open();
    const openTime = Date.now() - startTime;
    colorLog(colors.green, `✓ 文件打开成功 (${formatDuration(openTime)})`);
    console.log('');

    // 获取基本信息
    colorLog(colors.cyan, '=== 基本信息 ===');
    const basicInfo = sdk.getBasicInfo();
    
    console.log(`版本号:     ${basicInfo.version}`);
    console.log(`图像尺寸:   ${basicInfo.imageSize.width} × ${basicInfo.imageSize.height} 像素`);
    console.log(`像素大小:   ${basicInfo.pixelSize} μm/pixel`);
    console.log(`扫描倍率:   ${basicInfo.scanScale}X`);
    console.log(`扫描层数:   ${basicInfo.layerCount}`);
    console.log(`焦点数量:   ${basicInfo.focusCount}`);
    console.log(`瓦片总数:   ${basicInfo.tileCount}`);
    console.log(`压缩算法:   ${basicInfo.compressAlgo} (${getCompressAlgoName(basicInfo.compressAlgo)})`);
    console.log(`压缩质量:   ${basicInfo.quality}`);
    console.log('');

    // 获取层级信息
    colorLog(colors.cyan, '=== 层级信息 ===');
    const layers = sdk.getAllLayers();
    if (layers.length > 0) {
      layers.forEach((layer, index) => {
        console.log(`层级 ${index}:`);
        console.log(`  - ID: ${layer.layerId}`);
        console.log(`  - 缩放比例: ${layer.scale}`);
        console.log(`  - 尺寸: ${layer.width} × ${layer.height}`);
        console.log(`  - 瓦片网格: ${layer.tileCol} × ${layer.tileRow} = ${layer.tileCol * layer.tileRow} 个瓦片`);
      });
    } else {
      colorLog(colors.yellow, '⚠ 当前版本不支持层级信息查询');
    }
    console.log('');

    // 提取图像
    colorLog(colors.cyan, '=== 图像提取 ===');
    const imageTypes = [
      { name: 'thumbnail', displayName: '缩略图', method: 'getThumbnail' },
      { name: 'navigate', displayName: '导航图', method: 'getNavigateImage' },
      { name: 'macro', displayName: '宏观图', method: 'getMacroImage' },
      { name: 'label', displayName: '标签图', method: 'getLabelImage' },
      { name: 'macro-label', displayName: '宏观标签图', method: 'getMacroLabelImage' }
    ];

    let extractedCount = 0;
    const extractStartTime = Date.now();

    for (const imageType of imageTypes) {
      try {
        colorLog(colors.yellow, `⏳ 正在提取${imageType.displayName}...`);
        const imageData = await sdk[imageType.method]('jpeg');
        
        if (imageData && imageData.length > 0) {
          const filename = `${imageType.name}.jpg`;
          const filepath = path.join(imageDir, filename);
          fs.writeFileSync(filepath, imageData);
          colorLog(colors.green, `✓ ${imageType.displayName}已保存: ${filename} (${formatBytes(imageData.length)})`);
          extractedCount++;
        } else {
          colorLog(colors.yellow, `⚠ ${imageType.displayName}不存在或为空`);
        }
      } catch (error) {
        colorLog(colors.red, `✗ ${imageType.displayName}提取失败: ${error.message}`);
      }
    }

    const extractTime = Date.now() - extractStartTime;
    console.log('');
    colorLog(colors.green, `✓ 图像提取完成: ${extractedCount}/5 个图像成功 (${formatDuration(extractTime)})`);

    // 提取示例瓦片
    console.log('');
    colorLog(colors.cyan, '=== 瓦片提取示例 ===');
    try {
      // 检查是否支持瓦片提取
      const layerInfo = sdk.getLayerTileInfo(0);
      if (layerInfo) {
        colorLog(colors.yellow, '⏳ 正在提取示例瓦片...');
        
        // 提取几个示例瓦片
        const maxSamples = Math.min(4, layerInfo.totalTiles);
        const tileRequests = [];
        
        for (let i = 0; i < maxSamples; i++) {
          const row = Math.floor(i / 2);
          const col = i % 2;
          if (row < layerInfo.tileRows && col < layerInfo.tileCols) {
            tileRequests.push({
              layer: 0,
              row: row,
              col: col,
              format: 'jpeg'
            });
          }
        }

        if (tileRequests.length > 0) {
          const tiles = await sdk.getTileDataBatch(tileRequests);
          let tileCount = 0;
          
          tiles.forEach((tile, index) => {
            if (tile && tile.length > 0) {
              const request = tileRequests[index];
              const filename = `tile_${request.layer}_${request.row}_${request.col}.jpg`;
              const filepath = path.join(imageDir, filename);
              fs.writeFileSync(filepath, tile);
              colorLog(colors.green, `✓ 瓦片已保存: ${filename} (${formatBytes(tile.length)})`);
              tileCount++;
            }
          });
          
          colorLog(colors.green, `✓ 瓦片提取完成: ${tileCount}/${tileRequests.length} 个瓦片成功`);
        }
      } else {
        colorLog(colors.yellow, '⚠ 当前版本不支持瓦片提取');
      }
    } catch (error) {
      colorLog(colors.red, `✗ 瓦片提取失败: ${error.message}`);
    }

    // 总结
    const totalTime = Date.now() - startTime;
    console.log('');
    colorLog(colors.cyan, '=== 处理完成 ===');
    colorLog(colors.green, `✓ 总处理时间: ${formatDuration(totalTime)}`);
    colorLog(colors.green, `✓ 图片保存目录: ${imageDir}`);
    
    // 列出保存的文件
    const savedFiles = fs.readdirSync(imageDir).filter(file => file.endsWith('.jpg'));
    if (savedFiles.length > 0) {
      console.log('');
      colorLog(colors.blue, '📁 已保存的文件:');
      savedFiles.forEach(file => {
        const filepath = path.join(imageDir, file);
        const fileSize = fs.statSync(filepath).size;
        console.log(`  - ${file} (${formatBytes(fileSize)})`);
      });
    }

  } catch (error) {
    colorLog(colors.red, `❌ 处理失败: ${error.message}`);
    console.error(error);
    process.exit(1);
  } finally {
    sdk.close();
  }
}

function getCompressAlgoName(algo) {
  const names = {
    0: 'Quick',
    1: 'JPEG',
    2: 'J2K'
  };
  return names[algo] || 'Unknown';
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    colorLog(colors.red, `❌ 未处理的错误: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

module.exports = { main };
