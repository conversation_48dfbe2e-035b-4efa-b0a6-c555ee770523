#!/usr/bin/env node

/**
 * 调试版CLI工具
 * 用于调试TMAP文件解析问题
 */

const { TmapSDK } = require('./dist/index.js');
const fs = require('fs');
const path = require('path');

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(color + message + colors.reset);
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function debugTmapFile(filePath) {
  colorLog(colors.cyan, '=== TMAP 文件调试工具 ===');
  console.log('');

  // 检查文件是否存在
  if (!fs.existsSync(filePath)) {
    colorLog(colors.red, `❌ 错误: 文件不存在: ${filePath}`);
    return;
  }

  // 获取文件信息
  const fileStats = fs.statSync(filePath);
  colorLog(colors.blue, `📁 文件路径: ${filePath}`);
  colorLog(colors.blue, `📊 文件大小: ${formatBytes(fileStats.size)}`);
  console.log('');

  // 读取文件头部信息
  try {
    colorLog(colors.yellow, '⏳ 正在读取文件头部...');
    
    const fileHandle = fs.openSync(filePath, 'r');
    try {
      // 读取前256字节（TMAP 7.x头部大小）
      const headerSize = Math.min(256, fileStats.size);
      const headerBuffer = Buffer.alloc(headerSize);
      const bytesRead = fs.readSync(fileHandle, headerBuffer, 0, headerSize, 0);
      
      colorLog(colors.green, `✓ 读取了 ${bytesRead} 字节的头部数据`);
      
      // 检查TMAP标识
      const tmapSignature = headerBuffer.toString('ascii', 0, 4);
      colorLog(colors.blue, `🔍 文件标识: "${tmapSignature}"`);
      
      if (tmapSignature === 'TMAP') {
        colorLog(colors.green, '✓ TMAP文件标识正确');
        
        // 读取版本信息
        const version = headerBuffer.toString('ascii', 4, 6);
        colorLog(colors.blue, `📋 版本号: "${version}"`);
        
        // 显示头部的十六进制内容（前64字节）
        console.log('');
        colorLog(colors.cyan, '🔍 头部数据（十六进制，前64字节）:');
        const hexData = headerBuffer.subarray(0, Math.min(64, headerBuffer.length));
        for (let i = 0; i < hexData.length; i += 16) {
          const chunk = hexData.subarray(i, Math.min(i + 16, hexData.length));
          const hex = chunk.toString('hex').match(/.{2}/g).join(' ');
          const ascii = chunk.toString('ascii').replace(/[^\x20-\x7E]/g, '.');
          console.log(`${i.toString(16).padStart(4, '0')}: ${hex.padEnd(47)} | ${ascii}`);
        }
      } else {
        colorLog(colors.red, '❌ 不是有效的TMAP文件（缺少TMAP标识）');
        return;
      }
      
    } finally {
      fs.closeSync(fileHandle);
    }
  } catch (error) {
    colorLog(colors.red, `❌ 读取文件头部失败: ${error.message}`);
    return;
  }

  console.log('');
  colorLog(colors.yellow, '⏳ 正在尝试使用SDK打开文件...');
  
  const sdk = new TmapSDK(filePath);
  
  try {
    await sdk.open();
    colorLog(colors.green, '✓ SDK成功打开文件');
    
    // 获取基本信息
    const basicInfo = sdk.getBasicInfo();
    console.log('');
    colorLog(colors.cyan, '=== 基本信息 ===');
    console.log(`版本号:     ${basicInfo.version}`);
    console.log(`图像尺寸:   ${basicInfo.imageSize.width} × ${basicInfo.imageSize.height} 像素`);
    console.log(`像素大小:   ${basicInfo.pixelSize} μm/pixel`);
    console.log(`扫描倍率:   ${basicInfo.scanScale}X`);
    console.log(`扫描层数:   ${basicInfo.layerCount}`);
    console.log(`焦点数量:   ${basicInfo.focusCount}`);
    console.log(`瓦片总数:   ${basicInfo.tileCount}`);
    console.log(`压缩算法:   ${basicInfo.compressAlgo}`);
    console.log(`压缩质量:   ${basicInfo.quality}`);
    
  } catch (error) {
    colorLog(colors.red, `❌ SDK打开文件失败: ${error.message}`);
    console.log('');
    colorLog(colors.yellow, '🔍 错误详情:');
    console.error(error);
  } finally {
    sdk.close();
  }
  
  console.log('');
  colorLog(colors.cyan, '=== 调试完成 ===');
}

async function main() {
  const args = process.argv.slice(2);
  if (args.length === 0) {
    colorLog(colors.red, '❌ 错误: 请提供TMAP文件路径');
    console.log('');
    console.log('使用方法:');
    console.log('  node debug-cli.js <tmap-file-path>');
    console.log('');
    console.log('示例:');
    console.log('  node debug-cli.js E:\\TMAP\\Test_1.TMAP');
    process.exit(1);
  }

  const tmapFilePath = args[0];
  await debugTmapFile(tmapFilePath);
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    colorLog(colors.red, `❌ 未处理的错误: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

module.exports = { debugTmapFile };
