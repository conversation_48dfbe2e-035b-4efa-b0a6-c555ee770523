/**
 * TMAP SDK 演示程序
 * 展示SDK的功能和使用方法（无需真实TMAP文件）
 */

import { TmapSDK, ImageType, CompressAlgo } from './src';

async function demo() {
  console.log('=== TMAP SDK 演示程序 ===\n');

  console.log('🎯 TMAP SDK 功能概览:');
  console.log('');
  
  console.log('📁 文件操作:');
  console.log('  ✓ 打开/关闭TMAP文件');
  console.log('  ✓ 验证文件格式和版本');
  console.log('  ✓ 处理DT附属文件');
  console.log('');

  console.log('📊 信息获取:');
  console.log('  ✓ 获取版本号');
  console.log('  ✓ 获取图像尺寸');
  console.log('  ✓ 获取像素大小 (μm/pixel)');
  console.log('  ✓ 获取扫描倍率');
  console.log('  ✓ 获取扫描层数');
  console.log('  ✓ 获取焦点数量');
  console.log('  ✓ 获取瓦片总数');
  console.log('');

  console.log('🖼️ 图像提取:');
  console.log('  ✓ 缩略图 (Thumbnail)');
  console.log('  ✓ 导航图 (Navigate)');
  console.log('  ✓ 宏观图 (Macro)');
  console.log('  ✓ 标签图 (Label)');
  console.log('  ✓ 宏观标签图 (Macro Label)');
  console.log('');

  console.log('🧩 瓦片操作:');
  console.log('  ✓ 按层级、行、列获取瓦片');
  console.log('  ✓ 支持多种输出格式 (JPEG, PNG, RAW)');
  console.log('  ✓ 自动处理空白瓦片');
  console.log('');

  console.log('🌐 Web服务:');
  console.log('  ✓ HTTP瓦片服务器');
  console.log('  ✓ OpenSeadragon DZI支持');
  console.log('  ✓ RESTful API接口');
  console.log('  ✓ CORS跨域支持');
  console.log('');

  console.log('📋 支持的TMAP版本:');
  console.log('  ✅ TMAP 7.x - 完全支持（所有功能）');
  console.log('  ✅ TMAP 6.x - 基本支持（信息读取）');
  console.log('  ✅ TMAP 5.x - 基本支持（扩展信息）');
  console.log('  ⚠️ TMAP 4.x - 部分支持（融合层）');
  console.log('  ⚠️ TMAP 3.x - 部分支持（融合层）');
  console.log('  ⚠️ TMAP 2.x - 部分支持（基础功能）');
  console.log('');

  console.log('📋 支持的图像类型:');
  Object.entries(ImageType).forEach(([key, value]) => {
    if (typeof value === 'number') {
      console.log(`  ${value}: ${key}`);
    }
  });
  console.log('');

  console.log('🗜️ 支持的压缩算法:');
  Object.entries(CompressAlgo).forEach(([key, value]) => {
    if (typeof value === 'number') {
      console.log(`  ${value}: ${key}`);
    }
  });
  console.log('');

  console.log('💻 基本使用示例:');
  console.log('');
  console.log('```typescript');
  console.log('import { TmapSDK } from "tmap-sdk";');
  console.log('');
  console.log('async function example() {');
  console.log('  const sdk = new TmapSDK("path/to/file.tmap");');
  console.log('  ');
  console.log('  try {');
  console.log('    // 打开文件');
  console.log('    await sdk.open();');
  console.log('    ');
  console.log('    // 获取基本信息');
  console.log('    const info = sdk.getBasicInfo();');
  console.log('    console.log("版本:", info.version);');
  console.log('    console.log("尺寸:", info.imageSize);');
  console.log('    console.log("像素大小:", info.pixelSize);');
  console.log('    ');
  console.log('    // 提取图像');
  console.log('    const thumbnail = await sdk.getThumbnail("jpeg");');
  console.log('    const navigate = await sdk.getNavigateImage("png");');
  console.log('    ');
  console.log('    // 获取瓦片');
  console.log('    const tile = await sdk.getTileData(0, 0, 0, "jpeg");');
  console.log('    ');
  console.log('    // 启动Web服务器');
  console.log('    await sdk.startTileServer(3000);');
  console.log('    ');
  console.log('  } finally {');
  console.log('    sdk.close();');
  console.log('  }');
  console.log('}');
  console.log('```');
  console.log('');

  console.log('🌐 Web API 端点:');
  console.log('  GET /image.dzi           - OpenSeadragon DZI描述');
  console.log('  GET /image_files/{l}/{c}_{r}.{f} - 瓦片数据');
  console.log('  GET /info                - TMAP文件信息');
  console.log('  GET /thumbnail           - 缩略图');
  console.log('  GET /navigate            - 导航图');
  console.log('  GET /macro               - 宏观图');
  console.log('  GET /label               - 标签图');
  console.log('');

  console.log('🔧 开发工具:');
  console.log('  npm run build            - 构建项目');
  console.log('  npm run test             - 运行测试');
  console.log('  npm run dev              - 开发模式');
  console.log('  npm start                - 启动示例');
  console.log('');

  console.log('📚 技术特性:');
  console.log('  ✓ TypeScript支持');
  console.log('  ✓ 完整类型定义');
  console.log('  ✓ 异步/Promise API');
  console.log('  ✓ 错误处理');
  console.log('  ✓ 内存优化');
  console.log('  ✓ 跨平台兼容');
  console.log('');

  console.log('⚠️ 注意事项:');
  console.log('  • 支持TMAP 2.x - 7.x版本，自动版本检测');
  console.log('  • TMAP 7.x版本功能最完整');
  console.log('  • 旧版本（2.x-6.x）功能有限，主要支持信息读取');
  console.log('  • 图像处理功能需要sharp库（可选）');
  console.log('  • 大文件处理时注意内存使用');
  console.log('  • DT附属文件会自动处理');
  console.log('');

  console.log('🚀 快速开始:');
  console.log('  1. 安装: npm install tmap-sdk');
  console.log('  2. 导入: import { TmapSDK } from "tmap-sdk"');
  console.log('  3. 创建实例: const sdk = new TmapSDK("file.tmap")');
  console.log('  4. 打开文件: await sdk.open()');
  console.log('  5. 使用功能: sdk.getBasicInfo()');
  console.log('');

  console.log('📖 更多信息请查看 README.md 文件');
  console.log('');
  console.log('=== 演示完成 ===');
}

// 运行演示
if (require.main === module) {
  demo().catch(console.error);
}
