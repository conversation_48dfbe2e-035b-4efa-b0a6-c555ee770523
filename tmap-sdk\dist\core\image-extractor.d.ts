/**
 * 图像提取器
 * 负责从TMAP文件中提取各种类型的图像数据
 */
import { TmapReader } from './tmap-reader';
import { ImageType, ImageInfo, LayerInfo } from '../types';
export declare class ImageExtractor {
    private tmapReader;
    constructor(tmapReader: TmapReader);
    /**
     * 获取指定类型的图像信息
     */
    getImageInfo(imageType: ImageType): ImageInfo | null;
    /**
     * 提取指定类型的图像数据
     */
    extractImage(imageType: ImageType, format?: 'raw' | 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取缩略图
     */
    getThumbnail(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取导航图
     */
    getNavigateImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取宏观图
     */
    getMacroImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取标签图
     */
    getLabelImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取宏观标签图
     */
    getMacroLabelImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取瓦片数据
     */
    getTileData(layerId: number, row: number, col: number, format?: 'raw' | 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 读取图像数据
     */
    private readImageData;
    /**
     * 读取瓦片数据
     */
    private readTileData;
    /**
     * 计算瓦片索引
     */
    private calculateTileIndex;
    /**
     * 创建空白瓦片
     */
    private createEmptyTile;
    /**
     * 获取层信息
     */
    getLayerInfo(layerId: number): LayerInfo | null;
    /**
     * 获取所有层信息
     */
    getAllLayers(): LayerInfo[];
}
//# sourceMappingURL=image-extractor.d.ts.map