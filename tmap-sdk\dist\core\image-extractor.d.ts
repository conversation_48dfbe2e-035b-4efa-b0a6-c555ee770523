/**
 * 图像提取器
 * 负责从TMAP文件中提取各种类型的图像数据
 */
import { TmapReader } from './tmap-reader';
import { ImageType, ImageInfo, LayerInfo } from '../types';
export declare class ImageExtractor {
    private tmapReader;
    constructor(tmapReader: TmapReader);
    /**
     * 获取指定类型的图像信息（仅支持TMAP 7.x）
     */
    getImageInfo(imageType: ImageType): ImageInfo | null;
    /**
     * 提取指定类型的图像数据（仅支持TMAP 7.x）
     */
    extractImage(imageType: ImageType, format?: 'raw' | 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取缩略图
     */
    getThumbnail(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取导航图
     */
    getNavigateImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取宏观图
     */
    getMacroImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取标签图
     */
    getLabelImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取宏观标签图
     */
    getMacroLabelImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取瓦片数据（仅支持TMAP 7.x）
     */
    getTileData(layerId: number, row: number, col: number, format?: 'raw' | 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 读取图像数据（仅支持TMAP 7.x）
     */
    private readImageData;
    /**
     * 读取瓦片数据（仅支持TMAP 7.x）
     */
    private readTileData;
    /**
     * 计算瓦片索引（仅支持TMAP 7.x）
     */
    private calculateTileIndex;
    /**
     * 创建空白瓦片（仅支持TMAP 7.x）
     */
    private createEmptyTile;
    /**
     * 获取层信息（仅支持TMAP 7.x）
     */
    getLayerInfo(layerId: number): LayerInfo | null;
    /**
     * 获取所有层信息（仅支持TMAP 7.x）
     */
    getAllLayers(): LayerInfo[];
}
//# sourceMappingURL=image-extractor.d.ts.map