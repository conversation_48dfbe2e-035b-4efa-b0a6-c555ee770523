"use strict";
/**
 * 图像提取器
 * 负责从TMAP文件中提取各种类型的图像数据
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageExtractor = void 0;
const fs = __importStar(require("fs"));
const compression_1 = require("../utils/compression");
const types_1 = require("../types");
class ImageExtractor {
    constructor(tmapReader) {
        this.tmapReader = tmapReader;
    }
    /**
     * 获取指定类型的图像信息
     */
    getImageInfo(imageType) {
        const tmapInfo = this.tmapReader.getTmapInfo();
        for (const image of tmapInfo.images) {
            if (image.type === imageType) {
                return image;
            }
        }
        return null;
    }
    /**
     * 提取指定类型的图像数据
     */
    async extractImage(imageType, format = 'jpeg') {
        const imageInfo = this.getImageInfo(imageType);
        if (!imageInfo) {
            return null;
        }
        if (!this.tmapReader.isFileOpen()) {
            throw new Error('TMAP file is not open');
        }
        // 读取压缩的图像数据
        const compressedData = await this.readImageData(imageInfo);
        if (format === 'raw') {
            return compressedData;
        }
        // 解压缩图像数据
        const tmapInfo = this.tmapReader.getTmapInfo();
        const rawData = await compression_1.CompressionUtils.decompressTileData(compressedData, tmapInfo.header.compressAlgo, imageInfo.width, imageInfo.height);
        // 转换为指定格式
        const channels = imageInfo.depth / 8;
        if (format === 'jpeg') {
            return await compression_1.CompressionUtils.convertToJpeg(rawData, imageInfo.width, imageInfo.height, channels, tmapInfo.header.quality);
        }
        else if (format === 'png') {
            return await compression_1.CompressionUtils.convertToPng(rawData, imageInfo.width, imageInfo.height, channels);
        }
        return rawData;
    }
    /**
     * 获取缩略图
     */
    async getThumbnail(format = 'jpeg') {
        return this.extractImage(types_1.ImageType.THUMBNAIL, format);
    }
    /**
     * 获取导航图
     */
    async getNavigateImage(format = 'jpeg') {
        return this.extractImage(types_1.ImageType.NAVIGATE, format);
    }
    /**
     * 获取宏观图
     */
    async getMacroImage(format = 'jpeg') {
        return this.extractImage(types_1.ImageType.MACRO, format);
    }
    /**
     * 获取标签图
     */
    async getLabelImage(format = 'jpeg') {
        return this.extractImage(types_1.ImageType.LABEL, format);
    }
    /**
     * 获取宏观标签图
     */
    async getMacroLabelImage(format = 'jpeg') {
        return this.extractImage(types_1.ImageType.MACRO_LABEL, format);
    }
    /**
     * 获取瓦片数据
     */
    async getTileData(layerId, row, col, format = 'jpeg') {
        const tmapInfo = this.tmapReader.getTmapInfo();
        // 验证层ID
        if (layerId < 0 || layerId >= tmapInfo.layers.length) {
            throw new Error(`Invalid layer ID: ${layerId}`);
        }
        const layer = tmapInfo.layers[layerId];
        // 验证瓦片坐标
        if (row < 0 || row >= layer.tileRow || col < 0 || col >= layer.tileCol) {
            throw new Error(`Invalid tile coordinates: (${row}, ${col})`);
        }
        // 计算瓦片索引
        const tileIndex = this.calculateTileIndex(layerId, row, col);
        if (tileIndex >= tmapInfo.tiles.length) {
            throw new Error(`Tile index out of range: ${tileIndex}`);
        }
        const tile = tmapInfo.tiles[tileIndex];
        if (tile.length === 0) {
            // 返回空白瓦片
            return this.createEmptyTile(tile.width, tile.height, format);
        }
        // 读取瓦片数据
        const compressedData = await this.readTileData(tile);
        if (format === 'raw') {
            return compressedData;
        }
        // 解压缩瓦片数据
        const rawData = await compression_1.CompressionUtils.decompressTileData(compressedData, tmapInfo.header.compressAlgo, tile.width, tile.height);
        // 转换为指定格式
        const channels = 3; // 假设RGB格式
        if (format === 'jpeg') {
            return await compression_1.CompressionUtils.convertToJpeg(rawData, tile.width, tile.height, channels, tmapInfo.header.quality);
        }
        else if (format === 'png') {
            return await compression_1.CompressionUtils.convertToPng(rawData, tile.width, tile.height, channels);
        }
        return rawData;
    }
    /**
     * 读取图像数据
     */
    async readImageData(imageInfo) {
        // 这里需要访问文件句柄，暂时使用文件路径重新打开
        const filePath = this.tmapReader.filePath;
        const fileHandle = fs.openSync(filePath, 'r');
        try {
            const buffer = Buffer.alloc(imageInfo.length);
            fs.readSync(fileHandle, buffer, 0, imageInfo.length, Number(imageInfo.offset));
            return buffer;
        }
        finally {
            fs.closeSync(fileHandle);
        }
    }
    /**
     * 读取瓦片数据
     */
    async readTileData(tile) {
        const filePath = this.tmapReader.filePath;
        const fileHandle = fs.openSync(filePath, 'r');
        try {
            const buffer = Buffer.alloc(tile.length);
            fs.readSync(fileHandle, buffer, 0, tile.length, Number(tile.offset));
            return buffer;
        }
        finally {
            fs.closeSync(fileHandle);
        }
    }
    /**
     * 计算瓦片索引
     */
    calculateTileIndex(layerId, row, col, focusId = 0) {
        const tmapInfo = this.tmapReader.getTmapInfo();
        const layer = tmapInfo.layers[layerId];
        // 计算焦点偏移
        const maxFocus = tmapInfo.header.maxFocusNumber;
        const actualFocusId = focusId >= 0 ? focusId : maxFocus - focusId;
        const focusOffset = actualFocusId * layer.tileCol * layer.tileRow;
        return layer.tileStart + focusOffset + row * layer.tileCol + col;
    }
    /**
     * 创建空白瓦片
     */
    async createEmptyTile(width, height, format) {
        const tmapInfo = this.tmapReader.getTmapInfo();
        const backgroundColor = tmapInfo.header.bkgColor;
        const channels = 3;
        const rawData = await compression_1.CompressionUtils.createFilledImage(width, height, backgroundColor, channels);
        if (format === 'raw') {
            return rawData;
        }
        else if (format === 'jpeg') {
            return await compression_1.CompressionUtils.convertToJpeg(rawData, width, height, channels, tmapInfo.header.quality);
        }
        else if (format === 'png') {
            return await compression_1.CompressionUtils.convertToPng(rawData, width, height, channels);
        }
        return rawData;
    }
    /**
     * 获取层信息
     */
    getLayerInfo(layerId) {
        const tmapInfo = this.tmapReader.getTmapInfo();
        if (layerId < 0 || layerId >= tmapInfo.layers.length) {
            return null;
        }
        return tmapInfo.layers[layerId];
    }
    /**
     * 获取所有层信息
     */
    getAllLayers() {
        const tmapInfo = this.tmapReader.getTmapInfo();
        return tmapInfo.layers;
    }
}
exports.ImageExtractor = ImageExtractor;
//# sourceMappingURL=image-extractor.js.map