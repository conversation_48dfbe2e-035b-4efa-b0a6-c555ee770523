"use strict";
/**
 * 图像提取器
 * 负责从TMAP文件中提取各种类型的图像数据
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageExtractor = void 0;
const fs = __importStar(require("fs"));
const compression_1 = require("../utils/compression");
const types_1 = require("../types");
class ImageExtractor {
    constructor(tmapReader) {
        this.tmapReader = tmapReader;
    }
    /**
     * 获取指定类型的图像信息（仅支持TMAP 7.x）
     */
    getImageInfo(imageType) {
        const tmapInfo = this.tmapReader.getTmapInfo();
        // 暂时只支持TMAP 7.x版本
        if (tmapInfo.version !== types_1.TmapVersion.VERSION_7) {
            console.warn('Image extraction currently only supports TMAP 7.x format');
            return null;
        }
        const tmapInfo7 = tmapInfo; // 临时类型断言
        for (const image of tmapInfo7.images) {
            if (image.type === imageType) {
                return image;
            }
        }
        return null;
    }
    /**
     * 提取指定类型的图像数据
     */
    async extractImage(imageType, format = 'jpeg') {
        const tmapInfo = this.tmapReader.getTmapInfo();
        if (tmapInfo.version === types_1.TmapVersion.VERSION_7) {
            return this.extractImageV7(imageType, format);
        }
        else if (tmapInfo.version === types_1.TmapVersion.VERSION_6 || tmapInfo.version === types_1.TmapVersion.VERSION_5) {
            return this.extractImageV5(imageType, format);
        }
        console.warn('Image extraction not supported for this TMAP version');
        return null;
    }
    /**
     * 提取TMAP 7.x图像数据
     */
    async extractImageV7(imageType, format) {
        const imageInfo = this.getImageInfo(imageType);
        if (!imageInfo) {
            return null;
        }
        if (!this.tmapReader.isFileOpen()) {
            throw new Error('TMAP file is not open');
        }
        // 读取压缩的图像数据
        const compressedData = await this.readImageData(imageInfo);
        if (format === 'raw') {
            return compressedData;
        }
        const tmapInfo = this.tmapReader.getTmapInfo();
        const tmapInfo7 = tmapInfo;
        const imageInfo7 = imageInfo;
        // 解压缩图像数据
        const rawData = await compression_1.CompressionUtils.decompressTileData(compressedData, tmapInfo7.header.compressAlgo, imageInfo7.width, imageInfo7.height);
        // 转换为指定格式
        const channels = imageInfo7.depth / 8;
        if (format === 'jpeg') {
            return await compression_1.CompressionUtils.convertToJpeg(rawData, imageInfo7.width, imageInfo7.height, channels, tmapInfo7.header.quality);
        }
        else if (format === 'png') {
            return await compression_1.CompressionUtils.convertToPng(rawData, imageInfo7.width, imageInfo7.height, channels);
        }
        return rawData;
    }
    /**
     * 提取TMAP 5.x/6.x图像数据
     */
    async extractImageV5(imageType, format) {
        const tmapInfo = this.tmapReader.getTmapInfo();
        const tmapInfo5 = tmapInfo;
        // 对于TMAP 5.x/6.x，尝试从缩略瓦片中找到对应的图像
        // 通常缩略图、导航图等存储在特定的缩略瓦片中
        const shrinkTiles = tmapInfo5.shrinkTiles;
        if (!shrinkTiles || shrinkTiles.length === 0) {
            console.warn(`No shrink tiles available for image type ${imageType}`);
            return null;
        }
        // 根据图像类型选择合适的缩略瓦片
        let targetTile = null;
        if (imageType === types_1.ImageType.THUMBNAIL) {
            // 缩略图通常是第一个或最小的瓦片
            targetTile = shrinkTiles[0];
        }
        else if (imageType === types_1.ImageType.NAVIGATE) {
            // 导航图通常是较大的瓦片
            targetTile = shrinkTiles.find((tile) => tile.length > 10000) || shrinkTiles[0];
        }
        else {
            // 其他类型，尝试第一个可用的瓦片
            targetTile = shrinkTiles[0];
        }
        if (!targetTile || targetTile.length === 0) {
            console.warn(`No suitable tile found for image type ${imageType}`);
            return null;
        }
        // 读取瓦片数据
        const tileData = await this.readShrinkTileData(targetTile);
        if (format === 'raw') {
            return tileData;
        }
        // 对于TMAP 5.x/6.x，数据通常已经是压缩格式（JPEG）
        // 如果请求的格式与存储格式匹配，直接返回
        if (format === 'jpeg') {
            return tileData;
        }
        // 如果需要转换格式，这里需要更复杂的处理
        // 暂时返回原始数据
        console.warn(`Format conversion from raw to ${format} not fully implemented for TMAP 5.x/6.x`);
        return tileData;
    }
    /**
     * 获取缩略图
     */
    async getThumbnail(format = 'jpeg') {
        return this.extractImage(types_1.ImageType.THUMBNAIL, format);
    }
    /**
     * 获取导航图
     */
    async getNavigateImage(format = 'jpeg') {
        return this.extractImage(types_1.ImageType.NAVIGATE, format);
    }
    /**
     * 获取宏观图
     */
    async getMacroImage(format = 'jpeg') {
        return this.extractImage(types_1.ImageType.MACRO, format);
    }
    /**
     * 获取标签图
     */
    async getLabelImage(format = 'jpeg') {
        return this.extractImage(types_1.ImageType.LABEL, format);
    }
    /**
     * 获取宏观标签图
     */
    async getMacroLabelImage(format = 'jpeg') {
        return this.extractImage(types_1.ImageType.MACRO_LABEL, format);
    }
    /**
     * 获取瓦片数据（仅支持TMAP 7.x）
     */
    async getTileData(layerId, row, col, format = 'jpeg') {
        const tmapInfo = this.tmapReader.getTmapInfo();
        // 暂时只支持TMAP 7.x版本
        if (tmapInfo.version !== types_1.TmapVersion.VERSION_7) {
            console.warn('Tile extraction currently only supports TMAP 7.x format');
            return null;
        }
        const tmapInfo7 = tmapInfo;
        // 验证层ID
        if (layerId < 0 || layerId >= tmapInfo7.layers.length) {
            throw new Error(`Invalid layer ID: ${layerId}`);
        }
        const layer = tmapInfo7.layers[layerId];
        // 验证瓦片坐标
        if (row < 0 || row >= layer.tileRow || col < 0 || col >= layer.tileCol) {
            throw new Error(`Invalid tile coordinates: (${row}, ${col})`);
        }
        // 计算瓦片索引
        const tileIndex = this.calculateTileIndex(layerId, row, col);
        if (tileIndex >= tmapInfo7.tiles.length) {
            throw new Error(`Tile index out of range: ${tileIndex}`);
        }
        const tile = tmapInfo7.tiles[tileIndex];
        if (tile.length === 0) {
            // 返回空白瓦片
            return this.createEmptyTile(tile.width, tile.height, format);
        }
        // 读取瓦片数据
        const compressedData = await this.readTileData(tile);
        if (format === 'raw') {
            return compressedData;
        }
        // 解压缩瓦片数据
        const rawData = await compression_1.CompressionUtils.decompressTileData(compressedData, tmapInfo7.header.compressAlgo, tile.width, tile.height);
        // 转换为指定格式
        const channels = 3; // 假设RGB格式
        if (format === 'jpeg') {
            return await compression_1.CompressionUtils.convertToJpeg(rawData, tile.width, tile.height, channels, tmapInfo7.header.quality);
        }
        else if (format === 'png') {
            return await compression_1.CompressionUtils.convertToPng(rawData, tile.width, tile.height, channels);
        }
        return rawData;
    }
    /**
     * 读取图像数据（仅支持TMAP 7.x）
     */
    async readImageData(imageInfo) {
        const filePath = this.tmapReader.filePath;
        const fileHandle = fs.openSync(filePath, 'r');
        try {
            const imageInfo7 = imageInfo;
            const buffer = Buffer.alloc(imageInfo7.length);
            fs.readSync(fileHandle, buffer, 0, imageInfo7.length, Number(imageInfo7.offset));
            return buffer;
        }
        finally {
            fs.closeSync(fileHandle);
        }
    }
    /**
     * 读取瓦片数据（仅支持TMAP 7.x）
     */
    async readTileData(tile) {
        const filePath = this.tmapReader.filePath;
        const fileHandle = fs.openSync(filePath, 'r');
        try {
            const tile7 = tile;
            const buffer = Buffer.alloc(tile7.length);
            fs.readSync(fileHandle, buffer, 0, tile7.length, Number(tile7.offset));
            return buffer;
        }
        finally {
            fs.closeSync(fileHandle);
        }
    }
    /**
     * 读取缩略瓦片数据（支持TMAP 5.x/6.x）
     */
    async readShrinkTileData(tile) {
        const filePath = this.tmapReader.filePath;
        const fileHandle = fs.openSync(filePath, 'r');
        try {
            const buffer = Buffer.alloc(tile.length);
            fs.readSync(fileHandle, buffer, 0, tile.length, tile.fileOffset);
            return buffer;
        }
        finally {
            fs.closeSync(fileHandle);
        }
    }
    /**
     * 计算瓦片索引（仅支持TMAP 7.x）
     */
    calculateTileIndex(layerId, row, col, focusId = 0) {
        const tmapInfo = this.tmapReader.getTmapInfo();
        const tmapInfo7 = tmapInfo;
        const layer = tmapInfo7.layers[layerId];
        // 计算焦点偏移
        const maxFocus = tmapInfo7.header.maxFocusNumber;
        const actualFocusId = focusId >= 0 ? focusId : maxFocus - focusId;
        const focusOffset = actualFocusId * layer.tileCol * layer.tileRow;
        return layer.tileStart + focusOffset + row * layer.tileCol + col;
    }
    /**
     * 创建空白瓦片（仅支持TMAP 7.x）
     */
    async createEmptyTile(width, height, format) {
        const tmapInfo = this.tmapReader.getTmapInfo();
        const tmapInfo7 = tmapInfo;
        const backgroundColor = tmapInfo7.header.bkgColor || 255;
        const channels = 3;
        const rawData = await compression_1.CompressionUtils.createFilledImage(width, height, backgroundColor, channels);
        if (format === 'raw') {
            return rawData;
        }
        else if (format === 'jpeg') {
            return await compression_1.CompressionUtils.convertToJpeg(rawData, width, height, channels, tmapInfo7.header.quality || 90);
        }
        else if (format === 'png') {
            return await compression_1.CompressionUtils.convertToPng(rawData, width, height, channels);
        }
        return rawData;
    }
    /**
     * 获取层信息（仅支持TMAP 7.x）
     */
    getLayerInfo(layerId) {
        const tmapInfo = this.tmapReader.getTmapInfo();
        if (tmapInfo.version !== types_1.TmapVersion.VERSION_7) {
            console.warn('Layer info currently only supports TMAP 7.x format');
            return null;
        }
        const tmapInfo7 = tmapInfo;
        if (layerId < 0 || layerId >= tmapInfo7.layers.length) {
            return null;
        }
        return tmapInfo7.layers[layerId];
    }
    /**
     * 获取所有层信息（仅支持TMAP 7.x）
     */
    getAllLayers() {
        const tmapInfo = this.tmapReader.getTmapInfo();
        if (tmapInfo.version !== types_1.TmapVersion.VERSION_7) {
            console.warn('Layer info currently only supports TMAP 7.x format');
            return [];
        }
        const tmapInfo7 = tmapInfo;
        return tmapInfo7.layers || [];
    }
}
exports.ImageExtractor = ImageExtractor;
//# sourceMappingURL=image-extractor.js.map