/**
 * 瓦片服务器
 * 为OpenSeadragon提供瓦片服务
 */
export declare class TileServer {
    private app;
    private tmapReader;
    private imageExtractor;
    private port;
    constructor(tmapFilePath: string, port?: number);
    /**
     * 设置中间件
     */
    private setupMiddleware;
    /**
     * 设置路由
     */
    private setupRoutes;
    /**
     * 获取DZI信息
     */
    private getDziInfo;
    /**
     * 获取DZI XML格式
     */
    private getDziXml;
    /**
     * 获取瓦片数据
     */
    private getTile;
    /**
     * 启动服务器
     */
    start(): Promise<void>;
    /**
     * 停止服务器
     */
    stop(): void;
}
//# sourceMappingURL=tile-server.d.ts.map