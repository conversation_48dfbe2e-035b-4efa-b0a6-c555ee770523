"use strict";
/**
 * 瓦片服务器
 * 为OpenSeadragon提供瓦片服务
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TileServer = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const tmap_reader_1 = require("./tmap-reader");
const image_extractor_1 = require("./image-extractor");
class TileServer {
    constructor(tmapFilePath, port = 3000) {
        this.app = (0, express_1.default)();
        this.tmapReader = new tmap_reader_1.TmapReader(tmapFilePath);
        this.imageExtractor = new image_extractor_1.ImageExtractor(this.tmapReader);
        this.port = port;
        this.setupMiddleware();
        this.setupRoutes();
    }
    /**
     * 设置中间件
     */
    setupMiddleware() {
        this.app.use((0, cors_1.default)());
        this.app.use(express_1.default.json());
        // 错误处理中间件
        this.app.use((err, req, res, next) => {
            console.error('Server error:', err);
            res.status(500).json({ error: err.message });
        });
    }
    /**
     * 设置路由
     */
    setupRoutes() {
        // 获取DZI信息
        this.app.get('/dzi', async (req, res) => {
            try {
                const dziInfo = await this.getDziInfo();
                res.json(dziInfo);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
        // 获取DZI XML格式
        this.app.get('/image.dzi', async (req, res) => {
            try {
                const dziXml = await this.getDziXml();
                res.set('Content-Type', 'application/xml');
                res.send(dziXml);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
        // 获取瓦片
        this.app.get('/image_files/:level/:col_:row.:format', async (req, res) => {
            try {
                const { level, format } = req.params;
                const colRowPart = req.params['col_:row'];
                const [col, row] = colRowPart.split('_');
                const tileRequest = {
                    layer: parseInt(level),
                    row: parseInt(row),
                    col: parseInt(col),
                    format: format
                };
                const tileData = await this.getTile(tileRequest);
                if (!tileData) {
                    res.status(404).json({ error: 'Tile not found' });
                    return;
                }
                // 设置正确的Content-Type
                const contentType = format === 'png' ? 'image/png' : 'image/jpeg';
                res.set('Content-Type', contentType);
                res.send(tileData);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
        // 获取基本信息
        this.app.get('/info', async (req, res) => {
            try {
                const basicInfo = this.tmapReader.getBasicInfo();
                res.json(basicInfo);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
        // 获取缩略图
        this.app.get('/thumbnail', async (req, res) => {
            try {
                const format = req.query.format || 'jpeg';
                const thumbnail = await this.imageExtractor.getThumbnail(format);
                if (!thumbnail) {
                    res.status(404).json({ error: 'Thumbnail not found' });
                    return;
                }
                const contentType = format === 'png' ? 'image/png' : 'image/jpeg';
                res.set('Content-Type', contentType);
                res.send(thumbnail);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
        // 获取导航图
        this.app.get('/navigate', async (req, res) => {
            try {
                const format = req.query.format || 'jpeg';
                const navigate = await this.imageExtractor.getNavigateImage(format);
                if (!navigate) {
                    res.status(404).json({ error: 'Navigate image not found' });
                    return;
                }
                const contentType = format === 'png' ? 'image/png' : 'image/jpeg';
                res.set('Content-Type', contentType);
                res.send(navigate);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
        // 获取宏观图
        this.app.get('/macro', async (req, res) => {
            try {
                const format = req.query.format || 'jpeg';
                const macro = await this.imageExtractor.getMacroImage(format);
                if (!macro) {
                    res.status(404).json({ error: 'Macro image not found' });
                    return;
                }
                const contentType = format === 'png' ? 'image/png' : 'image/jpeg';
                res.set('Content-Type', contentType);
                res.send(macro);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
        // 获取标签图
        this.app.get('/label', async (req, res) => {
            try {
                const format = req.query.format || 'jpeg';
                const label = await this.imageExtractor.getLabelImage(format);
                if (!label) {
                    res.status(404).json({ error: 'Label image not found' });
                    return;
                }
                const contentType = format === 'png' ? 'image/png' : 'image/jpeg';
                res.set('Content-Type', contentType);
                res.send(label);
            }
            catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
    }
    /**
     * 获取DZI信息
     */
    async getDziInfo() {
        const basicInfo = this.tmapReader.getBasicInfo();
        return {
            format: 'jpeg',
            overlap: 0,
            tileSize: 256,
            size: {
                width: basicInfo.imageSize.width,
                height: basicInfo.imageSize.height
            }
        };
    }
    /**
     * 获取DZI XML格式
     */
    async getDziXml() {
        const dziInfo = await this.getDziInfo();
        return `<?xml version="1.0" encoding="UTF-8"?>
<Image xmlns="http://schemas.microsoft.com/deepzoom/2008"
       Format="${dziInfo.format}"
       Overlap="${dziInfo.overlap}"
       TileSize="${dziInfo.tileSize}">
  <Size Width="${dziInfo.size.width}" Height="${dziInfo.size.height}"/>
</Image>`;
    }
    /**
     * 获取瓦片数据
     */
    async getTile(request) {
        const format = request.format === 'png' ? 'png' : 'jpeg';
        return await this.imageExtractor.getTileData(request.layer, request.row, request.col, format);
    }
    /**
     * 启动服务器
     */
    async start() {
        try {
            // 打开TMAP文件
            await this.tmapReader.open();
            // 启动HTTP服务器
            this.app.listen(this.port, () => {
                console.log(`TMAP Tile Server started on port ${this.port}`);
                console.log(`DZI URL: http://localhost:${this.port}/image.dzi`);
                console.log(`Info URL: http://localhost:${this.port}/info`);
            });
        }
        catch (error) {
            throw new Error(`Failed to start tile server: ${error}`);
        }
    }
    /**
     * 停止服务器
     */
    stop() {
        this.tmapReader.close();
    }
}
exports.TileServer = TileServer;
//# sourceMappingURL=tile-server.js.map