/**
 * TMAP 5.x/6.x 版本文件读取器
 * 负责解析TMAP 5.x和6.x版本的文件格式
 */
import { TmapInfo5, TmapVersion } from '../types';
export declare class TmapReaderV5 {
    private filePath;
    private fileHandle;
    private version;
    constructor(filePath: string, version: TmapVersion);
    /**
     * 解析TMAP 5.x/6.x文件
     */
    parse(): Promise<TmapInfo5>;
    /**
     * 解析文件头
     */
    private parseHeader;
    /**
     * 验证文件头
     */
    private validateHeader;
    /**
     * 解析扩展信息
     */
    private parseExtInfo;
    /**
     * 解析图像信息
     */
    private parseImages;
    /**
     * 解析缩略瓦片信息
     */
    private parseShrinkTiles;
    /**
     * 验证校验和（仅适用于版本3及以上）
     */
    private validateChecksum;
}
//# sourceMappingURL=tmap-reader-v5.d.ts.map