"use strict";
/**
 * TMAP 5.x/6.x 版本文件读取器
 * 负责解析TMAP 5.x和6.x版本的文件格式
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TmapReaderV5 = void 0;
const fs = __importStar(require("fs"));
const buffer_reader_1 = require("../utils/buffer-reader");
const types_1 = require("../types");
class TmapReaderV5 {
    constructor(filePath, version) {
        this.fileHandle = null;
        this.filePath = filePath;
        this.version = version;
    }
    /**
     * 解析TMAP 5.x/6.x文件
     */
    async parse() {
        this.fileHandle = fs.openSync(this.filePath, 'r');
        try {
            // 解析文件头
            const header = await this.parseHeader();
            // 解析扩展信息（5.x及以上版本）
            let extInfo;
            if (this.version >= types_1.TmapVersion.VERSION_5) {
                extInfo = await this.parseExtInfo();
            }
            // 解析图像信息
            const images = await this.parseImages(header);
            // 解析缩略瓦片信息
            const shrinkTiles = await this.parseShrinkTiles(header);
            return {
                version: this.version,
                header,
                extInfo,
                images,
                shrinkTiles
            };
        }
        finally {
            if (this.fileHandle !== null) {
                fs.closeSync(this.fileHandle);
                this.fileHandle = null;
            }
        }
    }
    /**
     * 解析文件头
     */
    async parseHeader() {
        if (!this.fileHandle) {
            throw new Error('File not opened');
        }
        const headerBuffer = Buffer.alloc(types_1.CONSTANTS.HEADER_SIZE_V5);
        fs.readSync(this.fileHandle, headerBuffer, 0, types_1.CONSTANTS.HEADER_SIZE_V5, 0);
        const reader = new buffer_reader_1.BufferReader(headerBuffer);
        const header = {
            header: reader.readString(4),
            mainVersion: reader.readString(2),
            maxFocusNumber: reader.readUInt8(),
            imageFormat: reader.readUInt8(),
            fileNumber: reader.readUInt8(),
            layerSize: reader.readUInt8(),
            imageColor: reader.readUInt8(),
            checksum: reader.readUInt8(),
            ratioStep: reader.readUInt8(),
            maxLayerSize: reader.readUInt8(),
            slideType: reader.readUInt8(),
            bkgColor: reader.readUInt8(),
            pixelSize: reader.readFloatLE(),
            imageWidth: reader.readInt32LE(),
            imageHeight: reader.readInt32LE(),
            tileWidth: reader.readInt32LE(),
            tileHeight: reader.readInt32LE(),
            imageRow: reader.readInt32LE(),
            imageCol: reader.readInt32LE(),
            totalImageNumber: reader.readInt32LE(),
            shrinkTileNumber: reader.readInt32LE(),
            airImageOffset: reader.readInt32LE(),
            extInfoOffset: reader.readInt32LE()
        };
        // 验证头部信息
        this.validateHeader(header);
        return header;
    }
    /**
     * 验证文件头
     */
    validateHeader(header) {
        if (header.header !== 'TMAP') {
            throw new Error(`Invalid TMAP header: ${header.header}`);
        }
        const version = parseInt(header.mainVersion[1]);
        if (version !== this.version) {
            throw new Error(`Version mismatch: expected ${this.version}, got ${version}`);
        }
        if (!types_1.CONSTANTS.SUPPORTED_COLOR_DEPTHS.includes(header.imageColor)) {
            throw new Error(`Unsupported color depth: ${header.imageColor}`);
        }
        if (header.imageWidth <= 0 || header.imageHeight <= 0) {
            throw new Error('Invalid image dimensions');
        }
        if (header.tileWidth <= 0 || header.tileHeight <= 0) {
            throw new Error('Invalid tile dimensions');
        }
    }
    /**
     * 解析扩展信息
     */
    async parseExtInfo() {
        if (!this.fileHandle) {
            throw new Error('File not opened');
        }
        const extInfoBuffer = Buffer.alloc(types_1.CONSTANTS.EXT_INFO_SIZE);
        const offset = types_1.CONSTANTS.HEADER_SIZE_V5;
        fs.readSync(this.fileHandle, extInfoBuffer, 0, types_1.CONSTANTS.EXT_INFO_SIZE, offset);
        const reader = new buffer_reader_1.BufferReader(extInfoBuffer);
        return {
            ocr: reader.readString(64),
            barcode: reader.readString(64),
            client: reader.readString(types_1.CONSTANTS.CLIENT_LEN + 1),
            reserved: reader.readBuffer(types_1.CONSTANTS.EXT_INFO_SIZE - 64 - 64 - types_1.CONSTANTS.CLIENT_LEN - 1)
        };
    }
    /**
     * 解析图像信息
     */
    async parseImages(header) {
        if (!this.fileHandle) {
            throw new Error('File not opened');
        }
        const images = [];
        const imageCount = this.version > types_1.TmapVersion.VERSION_3 ?
            header.totalImageNumber :
            header.imageRow * header.imageCol;
        const imageDataSize = imageCount * types_1.CONSTANTS.IMAGE_INFO_5_SIZE;
        const imageBuffer = Buffer.alloc(imageDataSize);
        let offset = types_1.CONSTANTS.HEADER_SIZE_V5;
        if (this.version >= types_1.TmapVersion.VERSION_5) {
            offset += types_1.CONSTANTS.EXT_INFO_SIZE;
        }
        fs.readSync(this.fileHandle, imageBuffer, 0, imageDataSize, offset);
        const reader = new buffer_reader_1.BufferReader(imageBuffer);
        for (let i = 0; i < imageCount; i++) {
            const image = {
                fileId: reader.readUInt8(),
                layer: reader.readUInt8(), // 注意：这里是有符号字节
                reserved: reader.readBuffer(2),
                topDx: reader.readUInt8(), // 注意：这里是有符号字节
                topDy: reader.readUInt8(), // 注意：这里是有符号字节
                leftDx: reader.readUInt8(), // 注意：这里是有符号字节
                leftDy: reader.readUInt8(), // 注意：这里是有符号字节
                imageCol: reader.readUInt8(), // 注意：这里是16位
                imageRow: reader.readUInt8(), // 注意：这里是16位
                x: reader.readInt32LE(),
                y: reader.readInt32LE(),
                tiles: []
            };
            // 读取瓦片信息（每个图像最多有MAX_TILE_NUM个瓦片）
            for (let j = 0; j < types_1.CONSTANTS.MAX_TILE_NUM; j++) {
                const tileFileId = reader.readUInt8();
                const tileLayer = reader.readUInt8();
                const tileX = reader.readInt32LE();
                const tileY = reader.readInt32LE();
                const tileOffset = reader.readInt32LE();
                const tileLength = reader.readInt32LE();
                // 只添加有效的瓦片（长度大于0）
                if (tileLength > 0) {
                    image.tiles.push({
                        fileId: tileFileId,
                        layer: tileLayer,
                        x: tileX,
                        y: tileY,
                        fileOffset: tileOffset,
                        length: tileLength
                    });
                }
            }
            images.push(image);
        }
        return images;
    }
    /**
     * 解析缩略瓦片信息
     */
    async parseShrinkTiles(header) {
        if (!this.fileHandle) {
            throw new Error('File not opened');
        }
        const shrinkTiles = [];
        const shrinkTileCount = header.shrinkTileNumber;
        if (shrinkTileCount <= 0) {
            return shrinkTiles;
        }
        const shrinkTileDataSize = shrinkTileCount * types_1.CONSTANTS.SHRINK_TILE_INFO_SIZE;
        const shrinkTileBuffer = Buffer.alloc(shrinkTileDataSize);
        // 计算偏移量
        const imageCount = this.version > types_1.TmapVersion.VERSION_3 ?
            header.totalImageNumber :
            header.imageRow * header.imageCol;
        let offset = types_1.CONSTANTS.HEADER_SIZE_V5;
        if (this.version >= types_1.TmapVersion.VERSION_5) {
            offset += types_1.CONSTANTS.EXT_INFO_SIZE;
        }
        offset += imageCount * types_1.CONSTANTS.IMAGE_INFO_5_SIZE;
        fs.readSync(this.fileHandle, shrinkTileBuffer, 0, shrinkTileDataSize, offset);
        const reader = new buffer_reader_1.BufferReader(shrinkTileBuffer);
        for (let i = 0; i < shrinkTileCount; i++) {
            const shrinkTile = {
                fileId: reader.readUInt8(),
                layerNo: reader.readUInt8(),
                x: reader.readInt32LE(),
                y: reader.readInt32LE(),
                fileOffset: reader.readInt32LE(),
                length: reader.readUInt32LE()
            };
            shrinkTiles.push(shrinkTile);
        }
        return shrinkTiles;
    }
    /**
     * 验证校验和（仅适用于版本3及以上）
     */
    validateChecksum(header, images, shrinkTiles) {
        if (this.version <= types_1.TmapVersion.VERSION_2) {
            return true; // 版本2没有校验和
        }
        // 这里应该实现校验和验证逻辑
        // 由于校验和计算比较复杂，暂时返回true
        console.warn('Checksum validation not implemented for version', this.version);
        return true;
    }
}
exports.TmapReaderV5 = TmapReaderV5;
//# sourceMappingURL=tmap-reader-v5.js.map