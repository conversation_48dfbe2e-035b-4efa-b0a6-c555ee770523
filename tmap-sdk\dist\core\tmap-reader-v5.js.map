{"version": 3, "file": "tmap-reader-v5.js", "sourceRoot": "", "sources": ["../../src/core/tmap-reader-v5.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,uCAAyB;AACzB,0DAAsD;AACtD,oCASkB;AAElB,MAAa,YAAY;IAKvB,YAAY,QAAgB,EAAE,OAAoB;QAH1C,eAAU,GAAkB,IAAI,CAAC;QAIvC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAElD,IAAI,CAAC;YACH,QAAQ;YACR,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAExC,mBAAmB;YACnB,IAAI,OAAgC,CAAC;YACrC,IAAI,IAAI,CAAC,OAAO,IAAI,mBAAW,CAAC,SAAS,EAAE,CAAC;gBAC1C,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YACtC,CAAC;YAED,SAAS;YACT,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAE9C,WAAW;YACX,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAExD,OAAO;gBACL,OAAO,EAAE,IAAI,CAAC,OAAwD;gBACtE,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,WAAW;aACZ,CAAC;QACJ,CAAC;gBAAS,CAAC;YACT,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;gBAC7B,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC9B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW;QACvB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,iBAAS,CAAC,cAAc,CAAC,CAAC;QAC5D,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC,EAAE,iBAAS,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAE3E,MAAM,MAAM,GAAG,IAAI,4BAAY,CAAC,YAAY,CAAC,CAAC;QAE9C,MAAM,MAAM,GAAgB;YAC1B,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;YAC5B,WAAW,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;YACjC,cAAc,EAAE,MAAM,CAAC,SAAS,EAAE;YAClC,WAAW,EAAE,MAAM,CAAC,SAAS,EAAE;YAC/B,UAAU,EAAE,MAAM,CAAC,SAAS,EAAE;YAC9B,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE;YAC7B,UAAU,EAAE,MAAM,CAAC,SAAS,EAAE;YAC9B,QAAQ,EAAE,MAAM,CAAC,SAAS,EAAE;YAC5B,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE;YAC7B,YAAY,EAAE,MAAM,CAAC,SAAS,EAAE;YAChC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE;YAC7B,QAAQ,EAAE,MAAM,CAAC,SAAS,EAAE;YAC5B,SAAS,EAAE,MAAM,CAAC,WAAW,EAAE;YAC/B,UAAU,EAAE,MAAM,CAAC,WAAW,EAAE;YAChC,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE;YACjC,SAAS,EAAE,MAAM,CAAC,WAAW,EAAE;YAC/B,UAAU,EAAE,MAAM,CAAC,WAAW,EAAE;YAChC,QAAQ,EAAE,MAAM,CAAC,WAAW,EAAE;YAC9B,QAAQ,EAAE,MAAM,CAAC,WAAW,EAAE;YAC9B,gBAAgB,EAAE,MAAM,CAAC,WAAW,EAAE;YACtC,gBAAgB,EAAE,MAAM,CAAC,WAAW,EAAE;YACtC,cAAc,EAAE,MAAM,CAAC,WAAW,EAAE;YACpC,aAAa,EAAE,MAAM,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,SAAS;QACT,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE5B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,MAAmB;QACxC,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,wBAAwB,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,8BAA8B,IAAI,CAAC,OAAO,SAAS,OAAO,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,IAAI,CAAC,iBAAS,CAAC,sBAAsB,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAiB,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,KAAK,CAAC,4BAA4B,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,IAAI,CAAC,IAAI,MAAM,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY;QACxB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,iBAAS,CAAC,aAAa,CAAC,CAAC;QAC5D,MAAM,MAAM,GAAG,iBAAS,CAAC,cAAc,CAAC;QAExC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,EAAE,CAAC,EAAE,iBAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QAEhF,MAAM,MAAM,GAAG,IAAI,4BAAY,CAAC,aAAa,CAAC,CAAC;QAE/C,OAAO;YACL,GAAG,EAAE,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1B,OAAO,EAAE,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,iBAAS,CAAC,UAAU,GAAG,CAAC,CAAC;YACnD,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,iBAAS,CAAC,aAAa,GAAG,EAAE,GAAG,EAAE,GAAG,iBAAS,CAAC,UAAU,GAAG,CAAC,CAAC;SAC1F,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,MAAmB;QAC3C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,MAAM,GAAiB,EAAE,CAAC;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,GAAG,mBAAW,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACzB,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAEpD,MAAM,aAAa,GAAG,UAAU,GAAG,iBAAS,CAAC,iBAAiB,CAAC;QAC/D,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAEhD,IAAI,MAAM,GAAG,iBAAS,CAAC,cAAc,CAAC;QACtC,IAAI,IAAI,CAAC,OAAO,IAAI,mBAAW,CAAC,SAAS,EAAE,CAAC;YAC1C,MAAM,IAAI,iBAAS,CAAC,aAAa,CAAC;QACpC,CAAC;QAED,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;QAEpE,MAAM,MAAM,GAAG,IAAI,4BAAY,CAAC,WAAW,CAAC,CAAC;QAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,KAAK,GAAe;gBACxB,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE;gBAC1B,KAAK,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,cAAc;gBACzC,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC9B,KAAK,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,cAAc;gBACzC,KAAK,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,cAAc;gBACzC,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,cAAc;gBAC1C,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,cAAc;gBAC1C,QAAQ,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,YAAY;gBAC1C,QAAQ,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,YAAY;gBAC1C,CAAC,EAAE,MAAM,CAAC,WAAW,EAAE;gBACvB,CAAC,EAAE,MAAM,CAAC,WAAW,EAAE;gBACvB,KAAK,EAAE,EAAE;aACV,CAAC;YAEF,iCAAiC;YACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAS,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;gBAChD,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;gBACtC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;gBACrC,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;gBACnC,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;gBACnC,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;gBACxC,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;gBAExC,kBAAkB;gBAClB,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;oBACnB,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;wBACf,MAAM,EAAE,UAAU;wBAClB,KAAK,EAAE,SAAS;wBAChB,CAAC,EAAE,KAAK;wBACR,CAAC,EAAE,KAAK;wBACR,UAAU,EAAE,UAAU;wBACtB,MAAM,EAAE,UAAU;qBACnB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,MAAmB;QAChD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,WAAW,GAAqB,EAAE,CAAC;QACzC,MAAM,eAAe,GAAG,MAAM,CAAC,gBAAgB,CAAC;QAEhD,IAAI,eAAe,IAAI,CAAC,EAAE,CAAC;YACzB,OAAO,WAAW,CAAC;QACrB,CAAC;QAED,MAAM,kBAAkB,GAAG,eAAe,GAAG,iBAAS,CAAC,qBAAqB,CAAC;QAC7E,MAAM,gBAAgB,GAAG,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAE1D,QAAQ;QACR,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,GAAG,mBAAW,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACzB,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAEpD,IAAI,MAAM,GAAG,iBAAS,CAAC,cAAc,CAAC;QACtC,IAAI,IAAI,CAAC,OAAO,IAAI,mBAAW,CAAC,SAAS,EAAE,CAAC;YAC1C,MAAM,IAAI,iBAAS,CAAC,aAAa,CAAC;QACpC,CAAC;QACD,MAAM,IAAI,UAAU,GAAG,iBAAS,CAAC,iBAAiB,CAAC;QAEnD,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE,kBAAkB,EAAE,MAAM,CAAC,CAAC;QAE9E,MAAM,MAAM,GAAG,IAAI,4BAAY,CAAC,gBAAgB,CAAC,CAAC;QAElD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,MAAM,UAAU,GAAmB;gBACjC,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE;gBAC1B,OAAO,EAAE,MAAM,CAAC,SAAS,EAAE;gBAC3B,CAAC,EAAE,MAAM,CAAC,WAAW,EAAE;gBACvB,CAAC,EAAE,MAAM,CAAC,WAAW,EAAE;gBACvB,UAAU,EAAE,MAAM,CAAC,WAAW,EAAE;gBAChC,MAAM,EAAE,MAAM,CAAC,YAAY,EAAE;aAC9B,CAAC;YAEF,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,MAAmB,EAAE,MAAoB,EAAE,WAA6B;QAC/F,IAAI,IAAI,CAAC,OAAO,IAAI,mBAAW,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC,CAAC,WAAW;QAC1B,CAAC;QAED,gBAAgB;QAChB,uBAAuB;QACvB,OAAO,CAAC,IAAI,CAAC,iDAAiD,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAhRD,oCAgRC"}