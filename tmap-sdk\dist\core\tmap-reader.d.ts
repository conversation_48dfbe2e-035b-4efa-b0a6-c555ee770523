/**
 * TMAP文件读取器
 * 负责解析TMAP文件格式并提供数据访问接口
 * 支持TMAP 2.x - 7.x版本
 */
import { TmapInfo, TmapBasicInfo } from '../types';
export declare class TmapReader {
    private filePath;
    private tmapInfo;
    private versionInfo;
    private isOpen;
    constructor(filePath: string);
    /**
     * 打开TMAP文件
     */
    open(): Promise<void>;
    /**
     * 关闭文件
     */
    close(): void;
    /**
     * 根据版本选择解析器
     */
    private parseByVersion;
    /**
     * 解析TMAP 7.x版本
     */
    private parseV7;
    /**
     * 解析TMAP 5.x/6.x版本
     */
    private parseV5;
    /**
     * 解析TMAP 7.x版本的图像信息
     */
    private parseImagesV7;
    /**
     * 解析TMAP 7.x版本的层信息
     */
    private parseLayersV7;
    /**
     * 解析TMAP 7.x版本的瓦片信息
     */
    private parseTilesV7;
    /**
     * 获取TMAP基本信息
     */
    getBasicInfo(): TmapBasicInfo;
    /**
     * 获取TMAP 7.x版本的基本信息
     */
    private getBasicInfoV7;
    /**
     * 获取TMAP 5.x/6.x版本的基本信息
     */
    private getBasicInfoV5;
    /**
     * 计算TMAP 5.x/6.x的真实图像尺寸
     */
    private calculateRealImageSize;
    /**
     * 获取完整的TMAP信息
     */
    getTmapInfo(): TmapInfo;
    /**
     * 检查文件是否已打开
     */
    isFileOpen(): boolean;
}
//# sourceMappingURL=tmap-reader.d.ts.map