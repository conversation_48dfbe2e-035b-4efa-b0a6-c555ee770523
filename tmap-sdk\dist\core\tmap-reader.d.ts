/**
 * TMAP文件读取器
 * 负责解析TMAP文件格式并提供数据访问接口
 */
import { TmapInfo, TmapBasicInfo } from '../types';
export declare class TmapReader {
    private filePath;
    private tmapInfo;
    private fileHandle;
    private isOpen;
    constructor(filePath: string);
    /**
     * 打开TMAP文件
     */
    open(): Promise<void>;
    /**
     * 关闭文件
     */
    close(): void;
    /**
     * 解析文件头
     */
    private parseHeader;
    /**
     * 验证文件头
     */
    private validateHeader;
    /**
     * 解析图像信息
     */
    private parseImages;
    /**
     * 解析层信息
     */
    private parseLayers;
    /**
     * 解析瓦片信息
     */
    private parseTiles;
    /**
     * 获取TMAP基本信息
     */
    getBasicInfo(): TmapBasicInfo;
    /**
     * 获取完整的TMAP信息
     */
    getTmapInfo(): TmapInfo;
    /**
     * 检查文件是否已打开
     */
    isFileOpen(): boolean;
}
//# sourceMappingURL=tmap-reader.d.ts.map