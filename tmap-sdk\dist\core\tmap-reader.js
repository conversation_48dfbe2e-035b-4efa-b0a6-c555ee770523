"use strict";
/**
 * TMAP文件读取器
 * 负责解析TMAP文件格式并提供数据访问接口
 * 支持TMAP 2.x - 7.x版本
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TmapReader = void 0;
const fs = __importStar(require("fs"));
const buffer_reader_1 = require("../utils/buffer-reader");
const version_detector_1 = require("./version-detector");
const tmap_reader_v5_1 = require("./tmap-reader-v5");
const types_1 = require("../types");
class TmapReader {
    constructor(filePath) {
        this.tmapInfo = null;
        this.versionInfo = null;
        this.isOpen = false;
        this.filePath = filePath;
    }
    /**
     * 打开TMAP文件
     */
    async open() {
        try {
            // 检查文件是否存在
            if (!fs.existsSync(this.filePath)) {
                throw new Error(`TMAP file not found: ${this.filePath}`);
            }
            // 检测版本
            this.versionInfo = version_detector_1.VersionDetector.detectVersion(this.filePath);
            if (!this.versionInfo.isSupported) {
                throw new Error(`Unsupported TMAP version: ${this.versionInfo.version}`);
            }
            // 验证文件
            if (!version_detector_1.VersionDetector.validateFile(this.filePath, this.versionInfo)) {
                throw new Error('TMAP file validation failed');
            }
            // 根据版本选择解析器
            await this.parseByVersion();
            this.isOpen = true;
        }
        catch (error) {
            this.close();
            throw new Error(`Failed to open TMAP file: ${error}`);
        }
    }
    /**
     * 关闭文件
     */
    close() {
        this.isOpen = false;
    }
    /**
     * 根据版本选择解析器
     */
    async parseByVersion() {
        if (!this.versionInfo) {
            throw new Error('Version info not available');
        }
        if (this.versionInfo.parserType === 'v7') {
            await this.parseV7();
        }
        else if (this.versionInfo.parserType === 'v5') {
            await this.parseV5();
        }
        else {
            throw new Error(`Unsupported parser type: ${this.versionInfo.parserType}`);
        }
    }
    /**
     * 解析TMAP 7.x版本
     */
    async parseV7() {
        const fileHandle = fs.openSync(this.filePath, 'r');
        try {
            // 读取文件头
            const headerBuffer = Buffer.alloc(types_1.CONSTANTS.HEADER_SIZE_V7);
            fs.readSync(fileHandle, headerBuffer, 0, types_1.CONSTANTS.HEADER_SIZE_V7, 0);
            const reader = new buffer_reader_1.BufferReader(headerBuffer);
            // 解析头部信息
            const header = {
                header: reader.readString(4),
                mainVersion: reader.readString(2),
                maxFocusNumber: reader.readUInt8(),
                imageFormat: reader.readUInt8(),
                fileNumber: reader.readUInt8(),
                layerSize: reader.readUInt8(),
                imageColor: reader.readUInt8(),
                checksum: reader.readUInt8(),
                ratioStep: reader.readUInt8(),
                maxLayerSize: reader.readUInt8(),
                slideType: reader.readUInt8(),
                bkgColor: reader.readUInt8(),
                pixelSize: reader.readFloatLE(),
                reserved: reader.readBuffer(2),
                compressAlgo: reader.readUInt8(),
                quality: reader.readUInt8(),
                maxZoomRate: reader.readUInt8(),
                imageNumber: reader.readInt32LE(),
                layerNumber: reader.readInt32LE(),
                tileNumber: reader.readInt32LE(),
                extOffset: reader.readBigInt64LE(),
                ocr: reader.readString(64),
                barcode: reader.readString(64),
                client: reader.readString(types_1.CONSTANTS.CLIENT_LEN + 1),
                reservedEx: reader.readBuffer(128 - types_1.CONSTANTS.CLIENT_LEN - 1)
            };
            // 读取图像信息
            const images = await this.parseImagesV7(fileHandle, header.imageNumber);
            // 读取层信息
            const layers = await this.parseLayersV7(fileHandle, header.layerNumber, header.imageNumber);
            // 读取瓦片信息
            const tiles = await this.parseTilesV7(fileHandle, header.tileNumber, header.imageNumber, header.layerNumber);
            this.tmapInfo = {
                version: types_1.TmapVersion.VERSION_7,
                header,
                images,
                layers,
                tiles
            };
        }
        finally {
            fs.closeSync(fileHandle);
        }
    }
    /**
     * 解析TMAP 5.x/6.x版本
     */
    async parseV5() {
        if (!this.versionInfo) {
            throw new Error('Version info not available');
        }
        const readerV5 = new tmap_reader_v5_1.TmapReaderV5(this.filePath, this.versionInfo.version);
        this.tmapInfo = await readerV5.parse();
    }
    /**
     * 解析TMAP 7.x版本的图像信息
     */
    async parseImagesV7(fileHandle, imageCount) {
        const images = [];
        const imageStructSize = types_1.CONSTANTS.IMAGE_INFO_7_SIZE;
        const imagesBuffer = Buffer.alloc(imageCount * imageStructSize);
        fs.readSync(fileHandle, imagesBuffer, 0, imagesBuffer.length, types_1.CONSTANTS.HEADER_SIZE_V7);
        const reader = new buffer_reader_1.BufferReader(imagesBuffer);
        for (let i = 0; i < imageCount; i++) {
            const image = {
                width: reader.readInt32LE(),
                height: reader.readInt32LE(),
                depth: reader.readInt32LE(),
                type: reader.readInt32LE(),
                offset: reader.readBigInt64LE(),
                length: reader.readInt32LE()
            };
            // 跳过填充字节
            reader.skip(4);
            images.push(image);
        }
        return images;
    }
    /**
     * 解析TMAP 7.x版本的层信息
     */
    async parseLayersV7(fileHandle, layerCount, imageCount) {
        const layers = [];
        const layerStructSize = types_1.CONSTANTS.LAYER_INFO_SIZE;
        const layersBuffer = Buffer.alloc(layerCount * layerStructSize);
        const imageStructSize = types_1.CONSTANTS.IMAGE_INFO_7_SIZE;
        const offset = types_1.CONSTANTS.HEADER_SIZE_V7 + imageCount * imageStructSize;
        fs.readSync(fileHandle, layersBuffer, 0, layersBuffer.length, offset);
        const reader = new buffer_reader_1.BufferReader(layersBuffer);
        for (let i = 0; i < layerCount; i++) {
            const layer = {
                layerId: reader.readInt32LE(),
                scale: reader.readFloatLE(),
                width: reader.readInt32LE(),
                height: reader.readInt32LE(),
                tileRow: reader.readInt32LE(),
                tileCol: reader.readInt32LE(),
                offset: reader.readInt32LE(),
                tileStart: reader.readInt32LE()
            };
            layers.push(layer);
        }
        return layers;
    }
    /**
     * 解析TMAP 7.x版本的瓦片信息
     */
    async parseTilesV7(fileHandle, tileCount, imageCount, layerCount) {
        const tiles = [];
        const tileStructSize = types_1.CONSTANTS.TILE_INFO_7_SIZE;
        const tilesBuffer = Buffer.alloc(tileCount * tileStructSize);
        const imageStructSize = types_1.CONSTANTS.IMAGE_INFO_7_SIZE;
        const layerStructSize = types_1.CONSTANTS.LAYER_INFO_SIZE;
        const offset = types_1.CONSTANTS.HEADER_SIZE_V7 +
            imageCount * imageStructSize +
            layerCount * layerStructSize;
        fs.readSync(fileHandle, tilesBuffer, 0, tilesBuffer.length, offset);
        const reader = new buffer_reader_1.BufferReader(tilesBuffer);
        for (let i = 0; i < tileCount; i++) {
            const tile = {
                layerId: reader.readInt32LE(),
                focusId: reader.readInt32LE(),
                x: reader.readInt32LE(),
                y: reader.readInt32LE(),
                width: reader.readInt32LE(),
                height: reader.readInt32LE(),
                offset: reader.readBigInt64LE(),
                length: reader.readInt32LE()
            };
            // 跳过填充字节
            reader.skip(4);
            tiles.push(tile);
        }
        return tiles;
    }
    /**
     * 获取TMAP基本信息
     */
    getBasicInfo() {
        if (!this.tmapInfo) {
            throw new Error('TMAP file not loaded');
        }
        if (this.tmapInfo.version === types_1.TmapVersion.VERSION_7) {
            return this.getBasicInfoV7(this.tmapInfo);
        }
        else {
            return this.getBasicInfoV5(this.tmapInfo);
        }
    }
    /**
     * 获取TMAP 7.x版本的基本信息
     */
    getBasicInfoV7(tmapInfo) {
        const header = tmapInfo.header;
        // 找到主图像（通常是第一个或最大的图像）
        let mainImage = tmapInfo.images[0];
        for (const image of tmapInfo.images) {
            if (image.type === types_1.ImageType.WHOLE ||
                (image.width * image.height > mainImage.width * mainImage.height)) {
                mainImage = image;
            }
        }
        return {
            version: header.mainVersion,
            imageSize: {
                width: mainImage.width,
                height: mainImage.height
            },
            pixelSize: header.pixelSize,
            scanScale: header.maxZoomRate,
            layerCount: header.layerNumber,
            focusCount: header.maxFocusNumber * 2 + 1,
            tileCount: header.tileNumber,
            compressAlgo: header.compressAlgo,
            quality: header.quality
        };
    }
    /**
     * 获取TMAP 5.x/6.x版本的基本信息
     */
    getBasicInfoV5(tmapInfo) {
        const header = tmapInfo.header;
        return {
            version: header.mainVersion,
            imageSize: {
                width: header.imageWidth,
                height: header.imageHeight
            },
            pixelSize: header.pixelSize,
            scanScale: header.maxLayerSize, // 5.x版本使用maxLayerSize作为扫描倍率
            layerCount: header.layerSize,
            focusCount: header.maxFocusNumber * 2 + 1,
            tileCount: tmapInfo.shrinkTiles.length,
            compressAlgo: types_1.CompressAlgo.QUICK, // 5.x版本默认使用快速压缩
            quality: 90 // 5.x版本默认质量
        };
    }
    /**
     * 获取完整的TMAP信息
     */
    getTmapInfo() {
        if (!this.tmapInfo) {
            throw new Error('TMAP file not loaded');
        }
        return this.tmapInfo;
    }
    /**
     * 检查文件是否已打开
     */
    isFileOpen() {
        return this.isOpen;
    }
}
exports.TmapReader = TmapReader;
//# sourceMappingURL=tmap-reader.js.map