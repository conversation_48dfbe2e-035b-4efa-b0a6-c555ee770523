"use strict";
/**
 * TMAP文件读取器
 * 负责解析TMAP文件格式并提供数据访问接口
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TmapReader = void 0;
const fs = __importStar(require("fs"));
const buffer_reader_1 = require("../utils/buffer-reader");
const types_1 = require("../types");
class TmapReader {
    constructor(filePath) {
        this.tmapInfo = null;
        this.fileHandle = null;
        this.isOpen = false;
        this.filePath = filePath;
    }
    /**
     * 打开TMAP文件
     */
    async open() {
        try {
            // 检查文件是否存在
            if (!fs.existsSync(this.filePath)) {
                throw new Error(`TMAP file not found: ${this.filePath}`);
            }
            // 打开文件
            this.fileHandle = fs.openSync(this.filePath, 'r');
            // 解析文件头
            await this.parseHeader();
            this.isOpen = true;
        }
        catch (error) {
            this.close();
            throw new Error(`Failed to open TMAP file: ${error}`);
        }
    }
    /**
     * 关闭文件
     */
    close() {
        if (this.fileHandle !== null) {
            fs.closeSync(this.fileHandle);
            this.fileHandle = null;
        }
        this.isOpen = false;
    }
    /**
     * 解析文件头
     */
    async parseHeader() {
        if (!this.fileHandle) {
            throw new Error('File not opened');
        }
        // 读取文件头
        const headerBuffer = Buffer.alloc(types_1.CONSTANTS.HEADER_SIZE);
        fs.readSync(this.fileHandle, headerBuffer, 0, types_1.CONSTANTS.HEADER_SIZE, 0);
        const reader = new buffer_reader_1.BufferReader(headerBuffer);
        // 解析头部信息
        const header = {
            header: reader.readString(4),
            mainVersion: reader.readString(2),
            reserved: reader.readBuffer(2),
            compressAlgo: reader.readUInt8(),
            quality: reader.readUInt8(),
            maxFocusNumber: reader.readUInt8(),
            maxZoomRate: reader.readUInt8(),
            bkgColor: reader.readUInt8(),
            pixelSize: reader.readFloatLE(),
            imageNumber: reader.readInt32LE(),
            layerNumber: reader.readInt32LE(),
            tileNumber: reader.readInt32LE(),
            extOffset: reader.readBigInt64LE(),
            ocr: reader.readString(64),
            barcode: reader.readString(64),
            client: reader.readString(types_1.CONSTANTS.CLIENT_LEN + 1),
            reservedEx: reader.readBuffer(128 - types_1.CONSTANTS.CLIENT_LEN - 1),
            checksum: reader.readInt32LE()
        };
        // 验证文件头
        this.validateHeader(header);
        // 读取图像信息
        const images = await this.parseImages(header.imageNumber);
        // 读取层信息
        const layers = await this.parseLayers(header.layerNumber);
        // 读取瓦片信息
        const tiles = await this.parseTiles(header.tileNumber);
        this.tmapInfo = {
            header,
            images,
            layers,
            tiles
        };
    }
    /**
     * 验证文件头
     */
    validateHeader(header) {
        if (header.header !== 'TMAP') {
            throw new Error(`Invalid TMAP header: ${header.header}`);
        }
        const version = parseInt(header.mainVersion);
        if (version < 7) {
            throw new Error(`Unsupported TMAP version: ${header.mainVersion}`);
        }
        if (header.imageNumber <= 0 || header.layerNumber <= 0 || header.tileNumber <= 0) {
            throw new Error('Invalid TMAP structure: zero images, layers, or tiles');
        }
    }
    /**
     * 解析图像信息
     */
    async parseImages(imageCount) {
        if (!this.fileHandle) {
            throw new Error('File not opened');
        }
        const images = [];
        const imageStructSize = 32; // 根据C++结构体大小
        const imagesBuffer = Buffer.alloc(imageCount * imageStructSize);
        fs.readSync(this.fileHandle, imagesBuffer, 0, imagesBuffer.length, types_1.CONSTANTS.HEADER_SIZE);
        const reader = new buffer_reader_1.BufferReader(imagesBuffer);
        for (let i = 0; i < imageCount; i++) {
            const image = {
                width: reader.readInt32LE(),
                height: reader.readInt32LE(),
                depth: reader.readInt32LE(),
                type: reader.readInt32LE(),
                offset: reader.readBigInt64LE(),
                length: reader.readInt32LE()
            };
            // 跳过填充字节
            reader.skip(4);
            images.push(image);
        }
        return images;
    }
    /**
     * 解析层信息
     */
    async parseLayers(layerCount) {
        if (!this.fileHandle) {
            throw new Error('File not opened');
        }
        const layers = [];
        const layerStructSize = 32; // 根据C++结构体大小
        const layersBuffer = Buffer.alloc(layerCount * layerStructSize);
        const imageStructSize = 32;
        const offset = types_1.CONSTANTS.HEADER_SIZE + this.tmapInfo.header.imageNumber * imageStructSize;
        fs.readSync(this.fileHandle, layersBuffer, 0, layersBuffer.length, offset);
        const reader = new buffer_reader_1.BufferReader(layersBuffer);
        for (let i = 0; i < layerCount; i++) {
            const layer = {
                layerId: reader.readInt32LE(),
                scale: reader.readFloatLE(),
                width: reader.readInt32LE(),
                height: reader.readInt32LE(),
                tileRow: reader.readInt32LE(),
                tileCol: reader.readInt32LE(),
                offset: reader.readInt32LE(),
                tileStart: reader.readInt32LE()
            };
            layers.push(layer);
        }
        return layers;
    }
    /**
     * 解析瓦片信息
     */
    async parseTiles(tileCount) {
        if (!this.fileHandle) {
            throw new Error('File not opened');
        }
        const tiles = [];
        const tileStructSize = 32; // 根据C++结构体大小
        const tilesBuffer = Buffer.alloc(tileCount * tileStructSize);
        const imageStructSize = 32;
        const layerStructSize = 32;
        const offset = types_1.CONSTANTS.HEADER_SIZE +
            this.tmapInfo.header.imageNumber * imageStructSize +
            this.tmapInfo.header.layerNumber * layerStructSize;
        fs.readSync(this.fileHandle, tilesBuffer, 0, tilesBuffer.length, offset);
        const reader = new buffer_reader_1.BufferReader(tilesBuffer);
        for (let i = 0; i < tileCount; i++) {
            const tile = {
                layerId: reader.readInt32LE(),
                focusId: reader.readInt32LE(),
                x: reader.readInt32LE(),
                y: reader.readInt32LE(),
                width: reader.readInt32LE(),
                height: reader.readInt32LE(),
                offset: reader.readBigInt64LE(),
                length: reader.readInt32LE()
            };
            // 跳过填充字节
            reader.skip(4);
            tiles.push(tile);
        }
        return tiles;
    }
    /**
     * 获取TMAP基本信息
     */
    getBasicInfo() {
        if (!this.tmapInfo) {
            throw new Error('TMAP file not loaded');
        }
        const header = this.tmapInfo.header;
        // 找到主图像（通常是第一个或最大的图像）
        let mainImage = this.tmapInfo.images[0];
        for (const image of this.tmapInfo.images) {
            if (image.type === types_1.ImageType.WHOLE ||
                (image.width * image.height > mainImage.width * mainImage.height)) {
                mainImage = image;
            }
        }
        return {
            version: header.mainVersion,
            imageSize: {
                width: mainImage.width,
                height: mainImage.height
            },
            pixelSize: header.pixelSize,
            scanScale: header.maxZoomRate,
            layerCount: header.layerNumber,
            focusCount: header.maxFocusNumber * 2 + 1,
            tileCount: header.tileNumber,
            compressAlgo: header.compressAlgo,
            quality: header.quality
        };
    }
    /**
     * 获取完整的TMAP信息
     */
    getTmapInfo() {
        if (!this.tmapInfo) {
            throw new Error('TMAP file not loaded');
        }
        return this.tmapInfo;
    }
    /**
     * 检查文件是否已打开
     */
    isFileOpen() {
        return this.isOpen;
    }
}
exports.TmapReader = TmapReader;
//# sourceMappingURL=tmap-reader.js.map