{"version": 3, "file": "tmap-reader.js", "sourceRoot": "", "sources": ["../../src/core/tmap-reader.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,uCAAyB;AAEzB,0DAAsD;AAEtD,oCAUkB;AAElB,MAAa,UAAU;IAMrB,YAAY,QAAgB;QAJpB,aAAQ,GAAoB,IAAI,CAAC;QACjC,eAAU,GAAkB,IAAI,CAAC;QACjC,WAAM,GAAY,KAAK,CAAC;QAG9B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC;YACH,WAAW;YACX,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,OAAO;YACP,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YAElD,QAAQ;YACR,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAEzB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAC7B,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC9B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW;QACvB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;QAED,QAAQ;QACR,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,iBAAS,CAAC,WAAW,CAAC,CAAC;QACzD,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC,EAAE,iBAAS,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAExE,MAAM,MAAM,GAAG,IAAI,4BAAY,CAAC,YAAY,CAAC,CAAC;QAE9C,SAAS;QACT,MAAM,MAAM,GAAe;YACzB,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;YAC5B,WAAW,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;YACjC,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;YAC9B,YAAY,EAAE,MAAM,CAAC,SAAS,EAAkB;YAChD,OAAO,EAAE,MAAM,CAAC,SAAS,EAAE;YAC3B,cAAc,EAAE,MAAM,CAAC,SAAS,EAAE;YAClC,WAAW,EAAE,MAAM,CAAC,SAAS,EAAE;YAC/B,QAAQ,EAAE,MAAM,CAAC,SAAS,EAAE;YAC5B,SAAS,EAAE,MAAM,CAAC,WAAW,EAAE;YAC/B,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE;YACjC,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE;YACjC,UAAU,EAAE,MAAM,CAAC,WAAW,EAAE;YAChC,SAAS,EAAE,MAAM,CAAC,cAAc,EAAE;YAClC,GAAG,EAAE,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1B,OAAO,EAAE,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,iBAAS,CAAC,UAAU,GAAG,CAAC,CAAC;YACnD,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,GAAG,GAAG,iBAAS,CAAC,UAAU,GAAG,CAAC,CAAC;YAC7D,QAAQ,EAAE,MAAM,CAAC,WAAW,EAAE;SAC/B,CAAC;QAEF,QAAQ;QACR,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAE5B,SAAS;QACT,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAE1D,QAAQ;QACR,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAE1D,SAAS;QACT,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAEvD,IAAI,CAAC,QAAQ,GAAG;YACd,MAAM;YACN,MAAM;YACN,MAAM;YACN,KAAK;SACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,MAAkB;QACvC,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,wBAAwB,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC7C,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,6BAA6B,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,MAAM,CAAC,WAAW,IAAI,CAAC,IAAI,MAAM,CAAC,WAAW,IAAI,CAAC,IAAI,MAAM,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;YACjF,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,UAAkB;QAC1C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,MAAM,GAAgB,EAAE,CAAC;QAC/B,MAAM,eAAe,GAAG,EAAE,CAAC,CAAC,aAAa;QACzC,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,eAAe,CAAC,CAAC;QAEhE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC,EAAE,YAAY,CAAC,MAAM,EAAE,iBAAS,CAAC,WAAW,CAAC,CAAC;QAE1F,MAAM,MAAM,GAAG,IAAI,4BAAY,CAAC,YAAY,CAAC,CAAC;QAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,KAAK,GAAc;gBACvB,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC3B,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC5B,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC3B,IAAI,EAAE,MAAM,CAAC,WAAW,EAAe;gBACvC,MAAM,EAAE,MAAM,CAAC,cAAc,EAAE;gBAC/B,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;aAC7B,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEf,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,UAAkB;QAC1C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,MAAM,GAAgB,EAAE,CAAC;QAC/B,MAAM,eAAe,GAAG,EAAE,CAAC,CAAC,aAAa;QACzC,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,eAAe,CAAC,CAAC;QAEhE,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,MAAM,MAAM,GAAG,iBAAS,CAAC,WAAW,GAAG,IAAI,CAAC,QAAS,CAAC,MAAM,CAAC,WAAW,GAAG,eAAe,CAAC;QAE3F,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC,EAAE,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAE3E,MAAM,MAAM,GAAG,IAAI,4BAAY,CAAC,YAAY,CAAC,CAAC;QAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,KAAK,GAAc;gBACvB,OAAO,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC7B,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC3B,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC3B,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC5B,OAAO,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC7B,OAAO,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC7B,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC5B,SAAS,EAAE,MAAM,CAAC,WAAW,EAAE;aAChC,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU,CAAC,SAAiB;QACxC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,KAAK,GAAe,EAAE,CAAC;QAC7B,MAAM,cAAc,GAAG,EAAE,CAAC,CAAC,aAAa;QACxC,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,cAAc,CAAC,CAAC;QAE7D,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,MAAM,MAAM,GAAG,iBAAS,CAAC,WAAW;YACrB,IAAI,CAAC,QAAS,CAAC,MAAM,CAAC,WAAW,GAAG,eAAe;YACnD,IAAI,CAAC,QAAS,CAAC,MAAM,CAAC,WAAW,GAAG,eAAe,CAAC;QAEnE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,CAAC,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAEzE,MAAM,MAAM,GAAG,IAAI,4BAAY,CAAC,WAAW,CAAC,CAAC;QAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,MAAM,IAAI,GAAa;gBACrB,OAAO,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC7B,OAAO,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC7B,CAAC,EAAE,MAAM,CAAC,WAAW,EAAE;gBACvB,CAAC,EAAE,MAAM,CAAC,WAAW,EAAE;gBACvB,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC3B,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC5B,MAAM,EAAE,MAAM,CAAC,cAAc,EAAE;gBAC/B,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;aAC7B,CAAC;YAEF,SAAS;YACT,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEf,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,YAAY;QACV,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QAEpC,sBAAsB;QACtB,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACxC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACzC,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAS,CAAC,KAAK;gBAC9B,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;gBACtE,SAAS,GAAG,KAAK,CAAC;YACpB,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,WAAW;YAC3B,SAAS,EAAE;gBACT,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,MAAM,EAAE,SAAS,CAAC,MAAM;aACzB;YACD,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,SAAS,EAAE,MAAM,CAAC,WAAW;YAC7B,UAAU,EAAE,MAAM,CAAC,WAAW;YAC9B,UAAU,EAAE,MAAM,CAAC,cAAc,GAAG,CAAC,GAAG,CAAC;YACzC,SAAS,EAAE,MAAM,CAAC,UAAU;YAC5B,YAAY,EAAE,MAAM,CAAC,YAAY;YACjC,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,WAAW;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF;AA5RD,gCA4RC"}