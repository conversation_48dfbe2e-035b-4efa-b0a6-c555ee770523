/**
 * TMAP版本检测器
 * 负责检测TMAP文件版本并选择合适的解析器
 */
import { TmapVersion } from '../types';
export interface VersionInfo {
    version: TmapVersion;
    isSupported: boolean;
    headerSize: number;
    parserType: 'v7' | 'v5' | 'legacy';
}
export declare class VersionDetector {
    /**
     * 检测TMAP文件版本
     */
    static detectVersion(filePath: string): VersionInfo;
    /**
     * 验证文件完整性
     */
    static validateFile(filePath: string, versionInfo: VersionInfo): boolean;
    /**
     * 验证TMAP 7.x版本头部
     */
    private static validateV7Header;
    /**
     * 验证TMAP 5.x/6.x版本头部
     */
    private static validateV5Header;
    /**
     * 获取版本描述
     */
    static getVersionDescription(version: TmapVersion): string;
    /**
     * 检查版本兼容性
     */
    static isVersionCompatible(version: TmapVersion, requiredFeatures?: string[]): boolean;
}
//# sourceMappingURL=version-detector.d.ts.map