"use strict";
/**
 * TMAP版本检测器
 * 负责检测TMAP文件版本并选择合适的解析器
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionDetector = void 0;
const fs = __importStar(require("fs"));
const buffer_reader_1 = require("../utils/buffer-reader");
const types_1 = require("../types");
class VersionDetector {
    /**
     * 检测TMAP文件版本
     */
    static detectVersion(filePath) {
        if (!fs.existsSync(filePath)) {
            throw new Error(`File not found: ${filePath}`);
        }
        // 读取文件头的前16字节来检测版本
        const fileHandle = fs.openSync(filePath, 'r');
        const headerBuffer = Buffer.alloc(16);
        try {
            fs.readSync(fileHandle, headerBuffer, 0, 16, 0);
        }
        finally {
            fs.closeSync(fileHandle);
        }
        const reader = new buffer_reader_1.BufferReader(headerBuffer);
        // 验证TMAP标识
        const header = reader.readString(4);
        if (header !== 'TMAP') {
            throw new Error(`Invalid TMAP header: ${header}`);
        }
        // 读取版本信息
        const mainVersionBytes = reader.readBuffer(2);
        const mainVersionStr = mainVersionBytes.toString('ascii');
        // 解析版本号
        if (mainVersionStr[0] !== '0') {
            throw new Error(`Invalid version format: ${mainVersionStr}`);
        }
        const versionNumber = parseInt(mainVersionStr[1]);
        // 验证版本范围
        if (versionNumber < types_1.CONSTANTS.MIN_SUPPORTED_VERSION ||
            versionNumber > types_1.CONSTANTS.MAX_SUPPORTED_VERSION) {
            return {
                version: versionNumber,
                isSupported: false,
                headerSize: 0,
                parserType: 'legacy'
            };
        }
        // 确定解析器类型和头部大小
        let parserType;
        let headerSize;
        if (versionNumber >= 7) {
            parserType = 'v7';
            headerSize = types_1.CONSTANTS.HEADER_SIZE_V7;
        }
        else if (versionNumber >= 5) {
            parserType = 'v5';
            headerSize = types_1.CONSTANTS.HEADER_SIZE_V5;
        }
        else {
            parserType = 'legacy';
            headerSize = types_1.CONSTANTS.HEADER_SIZE_V5;
        }
        return {
            version: versionNumber,
            isSupported: true,
            headerSize,
            parserType
        };
    }
    /**
     * 验证文件完整性
     */
    static validateFile(filePath, versionInfo) {
        if (!versionInfo.isSupported) {
            return false;
        }
        const fileHandle = fs.openSync(filePath, 'r');
        try {
            // 读取完整的文件头
            const headerBuffer = Buffer.alloc(versionInfo.headerSize);
            fs.readSync(fileHandle, headerBuffer, 0, versionInfo.headerSize, 0);
            const reader = new buffer_reader_1.BufferReader(headerBuffer);
            // 跳过已验证的部分
            reader.skip(6); // header + version
            if (versionInfo.parserType === 'v7') {
                return this.validateV7Header(reader);
            }
            else if (versionInfo.parserType === 'v5') {
                return this.validateV5Header(reader, versionInfo.version);
            }
            return true;
        }
        catch (error) {
            console.error('File validation error:', error);
            return false;
        }
        finally {
            fs.closeSync(fileHandle);
        }
    }
    /**
     * 验证TMAP 7.x版本头部
     */
    static validateV7Header(reader) {
        try {
            reader.skip(2); // reserved
            const compressAlgo = reader.readUInt8();
            const quality = reader.readUInt8();
            const maxFocusNumber = reader.readUInt8();
            const maxZoomRate = reader.readUInt8();
            const bkgColor = reader.readUInt8();
            reader.skip(4); // pixelSize
            const imageNumber = reader.readInt32LE();
            const layerNumber = reader.readInt32LE();
            const tileNumber = reader.readInt32LE();
            // 基本验证
            return compressAlgo >= 0 && compressAlgo <= 2 &&
                quality > 0 && quality <= 100 &&
                maxFocusNumber >= 0 && maxFocusNumber <= 20 &&
                maxZoomRate > 0 && maxZoomRate <= 200 &&
                imageNumber > 0 && imageNumber <= types_1.CONSTANTS.MAX_IMAGE_NUM &&
                layerNumber > 0 && layerNumber <= types_1.CONSTANTS.MAX_LAYER_NUM &&
                tileNumber >= 0;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * 验证TMAP 5.x/6.x版本头部
     */
    static validateV5Header(reader, version) {
        try {
            const maxFocusNum = reader.readUInt8();
            const imageFormat = reader.readUInt8();
            const fileNum = reader.readUInt8();
            const layerSize = reader.readUInt8();
            const imgColor = reader.readUInt8();
            const checkSum = reader.readUInt8();
            const ratioStep = reader.readUInt8();
            const maxLaySize = reader.readUInt8();
            const slideType = reader.readUInt8();
            const bkColor = reader.readUInt8();
            reader.skip(4); // pixelSize
            const imgWidth = reader.readInt32LE();
            const imgHeight = reader.readInt32LE();
            const tileWidth = reader.readInt32LE();
            const tileHeight = reader.readInt32LE();
            const imgRow = reader.readInt32LE();
            const imgCol = reader.readInt32LE();
            // 基本验证
            return maxFocusNum >= 0 && maxFocusNum <= 20 &&
                fileNum > 0 && fileNum <= types_1.CONSTANTS.MAX_FILE_NUM &&
                layerSize > 0 && layerSize <= types_1.CONSTANTS.MAX_LAYER_SIZE &&
                types_1.CONSTANTS.SUPPORTED_COLOR_DEPTHS.includes(imgColor) &&
                imgWidth > 0 && imgHeight > 0 &&
                tileWidth > 0 && tileHeight > 0 &&
                imgRow > 0 && imgCol > 0;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * 获取版本描述
     */
    static getVersionDescription(version) {
        switch (version) {
            case types_1.TmapVersion.VERSION_2:
                return 'TMAP 2.x - 基础版本';
            case types_1.TmapVersion.VERSION_3:
                return 'TMAP 3.x - 增加融合层支持';
            case types_1.TmapVersion.VERSION_4:
                return 'TMAP 4.x - 改进的融合层';
            case types_1.TmapVersion.VERSION_5:
                return 'TMAP 5.x - 增加扩展信息';
            case types_1.TmapVersion.VERSION_6:
                return 'TMAP 6.x - 改进的扩展信息';
            case types_1.TmapVersion.VERSION_7:
                return 'TMAP 7.x - 最新版本，完整功能';
            default:
                return `TMAP ${version}.x - 未知版本`;
        }
    }
    /**
     * 检查版本兼容性
     */
    static isVersionCompatible(version, requiredFeatures = []) {
        const supportedFeatures = {
            [types_1.TmapVersion.VERSION_2]: ['basic_tiles'],
            [types_1.TmapVersion.VERSION_3]: ['basic_tiles', 'fusion_layers'],
            [types_1.TmapVersion.VERSION_4]: ['basic_tiles', 'fusion_layers', 'improved_fusion'],
            [types_1.TmapVersion.VERSION_5]: ['basic_tiles', 'fusion_layers', 'improved_fusion', 'ext_info'],
            [types_1.TmapVersion.VERSION_6]: ['basic_tiles', 'fusion_layers', 'improved_fusion', 'ext_info', 'improved_ext'],
            [types_1.TmapVersion.VERSION_7]: ['basic_tiles', 'fusion_layers', 'improved_fusion', 'ext_info', 'improved_ext', 'full_features', 'compression', 'ocr', 'barcode']
        };
        const versionFeatures = supportedFeatures[version] || [];
        return requiredFeatures.every(feature => versionFeatures.includes(feature));
    }
}
exports.VersionDetector = VersionDetector;
//# sourceMappingURL=version-detector.js.map