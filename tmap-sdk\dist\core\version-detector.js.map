{"version": 3, "file": "version-detector.js", "sourceRoot": "", "sources": ["../../src/core/version-detector.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,uCAAyB;AACzB,0DAAsD;AACtD,oCAAkD;AASlD,MAAa,eAAe;IAC1B;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,QAAgB;QACnC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;QACjD,CAAC;QAED,mBAAmB;QACnB,MAAM,UAAU,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC9C,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAEtC,IAAI,CAAC;YACH,EAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAClD,CAAC;gBAAS,CAAC;YACT,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,4BAAY,CAAC,YAAY,CAAC,CAAC;QAE9C,WAAW;QACX,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,wBAAwB,MAAM,EAAE,CAAC,CAAC;QACpD,CAAC;QAED,SAAS;QACT,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC9C,MAAM,cAAc,GAAG,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAE1D,QAAQ;QACR,IAAI,cAAc,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,2BAA2B,cAAc,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAElD,SAAS;QACT,IAAI,aAAa,GAAG,iBAAS,CAAC,qBAAqB;YAC/C,aAAa,GAAG,iBAAS,CAAC,qBAAqB,EAAE,CAAC;YACpD,OAAO;gBACL,OAAO,EAAE,aAA4B;gBACrC,WAAW,EAAE,KAAK;gBAClB,UAAU,EAAE,CAAC;gBACb,UAAU,EAAE,QAAQ;aACrB,CAAC;QACJ,CAAC;QAED,eAAe;QACf,IAAI,UAAkC,CAAC;QACvC,IAAI,UAAkB,CAAC;QAEvB,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;YACvB,UAAU,GAAG,IAAI,CAAC;YAClB,UAAU,GAAG,iBAAS,CAAC,cAAc,CAAC;QACxC,CAAC;aAAM,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;YAC9B,UAAU,GAAG,IAAI,CAAC;YAClB,UAAU,GAAG,iBAAS,CAAC,cAAc,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,UAAU,GAAG,QAAQ,CAAC;YACtB,UAAU,GAAG,iBAAS,CAAC,cAAc,CAAC;QACxC,CAAC;QAED,OAAO;YACL,OAAO,EAAE,aAA4B;YACrC,WAAW,EAAE,IAAI;YACjB,UAAU;YACV,UAAU;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,QAAgB,EAAE,WAAwB;QAC5D,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YAC7B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,UAAU,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAE9C,IAAI,CAAC;YACH,WAAW;YACX,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAC1D,EAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC,EAAE,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;YAEpE,MAAM,MAAM,GAAG,IAAI,4BAAY,CAAC,YAAY,CAAC,CAAC;YAE9C,WAAW;YACX,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB;YAEnC,IAAI,WAAW,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;gBACpC,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACvC,CAAC;iBAAM,IAAI,WAAW,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;gBAC3C,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;YAC5D,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,KAAK,CAAC;QACf,CAAC;gBAAS,CAAC;YACT,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,MAAoB;QAClD,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW;YAC3B,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACxC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACnC,MAAM,cAAc,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAC1C,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACvC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACpC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY;YAC5B,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YAExC,OAAO;YACP,OAAO,YAAY,IAAI,CAAC,IAAI,YAAY,IAAI,CAAC;gBACtC,OAAO,GAAG,CAAC,IAAI,OAAO,IAAI,GAAG;gBAC7B,cAAc,IAAI,CAAC,IAAI,cAAc,IAAI,EAAE;gBAC3C,WAAW,GAAG,CAAC,IAAI,WAAW,IAAI,GAAG;gBACrC,WAAW,GAAG,CAAC,IAAI,WAAW,IAAI,iBAAS,CAAC,aAAa;gBACzD,WAAW,GAAG,CAAC,IAAI,WAAW,IAAI,iBAAS,CAAC,aAAa;gBACzD,UAAU,IAAI,CAAC,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,gBAAgB,CAAC,MAAoB,EAAE,OAAoB;QACxE,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACvC,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACvC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACnC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACrC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACpC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACpC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACrC,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACrC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY;YAC5B,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YAEpC,OAAO;YACP,OAAO,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,EAAE;gBACrC,OAAO,GAAG,CAAC,IAAI,OAAO,IAAI,iBAAS,CAAC,YAAY;gBAChD,SAAS,GAAG,CAAC,IAAI,SAAS,IAAI,iBAAS,CAAC,cAAc;gBACtD,iBAAS,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAe,CAAC;gBAC1D,QAAQ,GAAG,CAAC,IAAI,SAAS,GAAG,CAAC;gBAC7B,SAAS,GAAG,CAAC,IAAI,UAAU,GAAG,CAAC;gBAC/B,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,OAAoB;QAC/C,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,mBAAW,CAAC,SAAS;gBACxB,OAAO,iBAAiB,CAAC;YAC3B,KAAK,mBAAW,CAAC,SAAS;gBACxB,OAAO,oBAAoB,CAAC;YAC9B,KAAK,mBAAW,CAAC,SAAS;gBACxB,OAAO,mBAAmB,CAAC;YAC7B,KAAK,mBAAW,CAAC,SAAS;gBACxB,OAAO,mBAAmB,CAAC;YAC7B,KAAK,mBAAW,CAAC,SAAS;gBACxB,OAAO,oBAAoB,CAAC;YAC9B,KAAK,mBAAW,CAAC,SAAS;gBACxB,OAAO,sBAAsB,CAAC;YAChC;gBACE,OAAO,QAAQ,OAAO,WAAW,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,OAAoB,EAAE,mBAA6B,EAAE;QAC9E,MAAM,iBAAiB,GAAkC;YACvD,CAAC,mBAAW,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,CAAC;YACxC,CAAC,mBAAW,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,EAAE,eAAe,CAAC;YACzD,CAAC,mBAAW,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,iBAAiB,CAAC;YAC5E,CAAC,mBAAW,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,iBAAiB,EAAE,UAAU,CAAC;YACxF,CAAC,mBAAW,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,iBAAiB,EAAE,UAAU,EAAE,cAAc,CAAC;YACxG,CAAC,mBAAW,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,iBAAiB,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,CAAC;SAC3J,CAAC;QAEF,MAAM,eAAe,GAAG,iBAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACzD,OAAO,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAC9E,CAAC;CACF;AAlND,0CAkNC"}