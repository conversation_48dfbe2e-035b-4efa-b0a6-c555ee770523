/**
 * TMAP SDK 主入口文件
 * 提供完整的TMAP文件解析和处理功能
 */
export { TmapReader } from './core/tmap-reader';
export { ImageExtractor } from './core/image-extractor';
export { BufferReader } from './utils/buffer-reader';
export { CompressionUtils } from './utils/compression';
export * from './types';
import { TileRequest } from './types';
export declare class TmapSDK {
    private tmapReader;
    private imageExtractor;
    constructor(tmapFilePath: string);
    /**
     * 打开TMAP文件
     */
    open(): Promise<void>;
    /**
     * 关闭TMAP文件
     */
    close(): void;
    /**
     * 获取版本号
     */
    getVersion(): string;
    /**
     * 获取图像尺寸
     */
    getImageSize(): {
        width: number;
        height: number;
    };
    /**
     * 获取像素尺寸
     */
    getPixelSize(): number;
    /**
     * 获取扫描倍率
     */
    getScanScale(): number;
    /**
     * 获取扫描层数
     */
    getLayerCount(): number;
    /**
     * 获取焦点数量
     */
    getFocusCount(): number;
    /**
     * 获取瓦片总数
     */
    getTileCount(): number;
    /**
     * 获取缩略图
     */
    getThumbnail(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取导航图
     */
    getNavigateImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取宏观图
     */
    getMacroImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取标签图
     */
    getLabelImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取宏观标签图
     */
    getMacroLabelImage(format?: 'jpeg' | 'png'): Promise<Buffer | null>;
    /**
     * 获取瓦片数据
     */
    getTileData(layer: number, row: number, col: number, format?: 'raw' | 'jpeg' | 'png', focusId?: number): Promise<Buffer | null>;
    /**
     * 批量获取瓦片数据
     */
    getTileDataBatch(requests: TileRequest[]): Promise<(Buffer | null)[]>;
    /**
     * 获取指定层的所有瓦片信息
     */
    getLayerTileInfo(layerId: number): {
        totalTiles: number;
        tileRows: number;
        tileCols: number;
    } | null;
    /**
     * 检查瓦片是否存在
     */
    tileExists(layer: number, row: number, col: number): Promise<boolean>;
    /**
     * 获取完整的TMAP信息
     */
    getTmapInfo(): import("./types").TmapInfo;
    /**
     * 获取基本信息
     */
    getBasicInfo(): import("./types").TmapBasicInfo;
    /**
     * 获取层信息
     */
    getLayerInfo(layerId: number): import("./types").LayerInfo | null;
    /**
     * 获取所有层信息
     */
    getAllLayers(): import("./types").LayerInfo[];
}
export default TmapSDK;
//# sourceMappingURL=index.d.ts.map