{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAGH,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AAGxD,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AACrD,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAGvD,cAAc,SAAS,CAAC;AAGxB,OAAO,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAGtC,qBAAa,OAAO;IAClB,OAAO,CAAC,UAAU,CAA0C;IAC5D,OAAO,CAAC,cAAc,CAAkD;gBAE5D,YAAY,EAAE,MAAM;IAKhC;;OAEG;IACG,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAI3B;;OAEG;IACH,KAAK,IAAI,IAAI;IAIb;;OAEG;IACH,UAAU,IAAI,MAAM;IAIpB;;OAEG;IACH,YAAY,IAAI;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE;IAIjD;;OAEG;IACH,YAAY,IAAI,MAAM;IAItB;;OAEG;IACH,YAAY,IAAI,MAAM;IAItB;;OAEG;IACH,aAAa,IAAI,MAAM;IAIvB;;OAEG;IACH,aAAa,IAAI,MAAM;IAIvB;;OAEG;IACH,YAAY,IAAI,MAAM;IAItB;;OAEG;IACG,YAAY,CAAC,MAAM,GAAE,MAAM,GAAG,KAAc,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAI3E;;OAEG;IACG,gBAAgB,CAAC,MAAM,GAAE,MAAM,GAAG,KAAc,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAI/E;;OAEG;IACG,aAAa,CAAC,MAAM,GAAE,MAAM,GAAG,KAAc,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAI5E;;OAEG;IACG,aAAa,CAAC,MAAM,GAAE,MAAM,GAAG,KAAc,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAI5E;;OAEG;IACG,kBAAkB,CAAC,MAAM,GAAE,MAAM,GAAG,KAAc,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAIjF;;OAEG;IACG,WAAW,CACf,KAAK,EAAE,MAAM,EACb,GAAG,EAAE,MAAM,EACX,GAAG,EAAE,MAAM,EACX,MAAM,GAAE,KAAK,GAAG,MAAM,GAAG,KAAc,EACvC,OAAO,GAAE,MAAU,GAClB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAIzB;;OAEG;IACG,gBAAgB,CAAC,QAAQ,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;IAsB3E;;OAEG;IACH,gBAAgB,CAAC,OAAO,EAAE,MAAM,GAAG;QAAE,UAAU,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAA;KAAE,GAAG,IAAI;IAapG;;OAEG;IACG,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAS3E;;OAEG;IACH,WAAW;IAIX;;OAEG;IACH,YAAY;IAIZ;;OAEG;IACH,YAAY,CAAC,OAAO,EAAE,MAAM;IAI5B;;OAEG;IACH,YAAY;CAGb;AAGD,eAAe,OAAO,CAAC"}