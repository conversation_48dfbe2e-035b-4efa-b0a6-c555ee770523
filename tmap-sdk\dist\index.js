"use strict";
/**
 * TMAP SDK 主入口文件
 * 提供完整的TMAP文件解析和处理功能
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TmapSDK = exports.CompressionUtils = exports.BufferReader = exports.ImageExtractor = exports.TmapReader = void 0;
// 导出核心类
var tmap_reader_1 = require("./core/tmap-reader");
Object.defineProperty(exports, "TmapReader", { enumerable: true, get: function () { return tmap_reader_1.TmapReader; } });
var image_extractor_1 = require("./core/image-extractor");
Object.defineProperty(exports, "ImageExtractor", { enumerable: true, get: function () { return image_extractor_1.ImageExtractor; } });
// 导出工具类
var buffer_reader_1 = require("./utils/buffer-reader");
Object.defineProperty(exports, "BufferReader", { enumerable: true, get: function () { return buffer_reader_1.BufferReader; } });
var compression_1 = require("./utils/compression");
Object.defineProperty(exports, "CompressionUtils", { enumerable: true, get: function () { return compression_1.CompressionUtils; } });
// 导出类型定义
__exportStar(require("./types"), exports);
// 导出便捷的SDK类
class TmapSDK {
    constructor(tmapFilePath) {
        this.tmapReader = new (require('./core/tmap-reader').TmapReader)(tmapFilePath);
        this.imageExtractor = new (require('./core/image-extractor').ImageExtractor)(this.tmapReader);
    }
    /**
     * 打开TMAP文件
     */
    async open() {
        await this.tmapReader.open();
    }
    /**
     * 关闭TMAP文件
     */
    close() {
        this.tmapReader.close();
    }
    /**
     * 获取版本号
     */
    getVersion() {
        return this.tmapReader.getBasicInfo().version;
    }
    /**
     * 获取图像尺寸
     */
    getImageSize() {
        return this.tmapReader.getBasicInfo().imageSize;
    }
    /**
     * 获取像素尺寸
     */
    getPixelSize() {
        return this.tmapReader.getBasicInfo().pixelSize;
    }
    /**
     * 获取扫描倍率
     */
    getScanScale() {
        return this.tmapReader.getBasicInfo().scanScale;
    }
    /**
     * 获取扫描层数
     */
    getLayerCount() {
        return this.tmapReader.getBasicInfo().layerCount;
    }
    /**
     * 获取焦点数量
     */
    getFocusCount() {
        return this.tmapReader.getBasicInfo().focusCount;
    }
    /**
     * 获取瓦片总数
     */
    getTileCount() {
        return this.tmapReader.getBasicInfo().tileCount;
    }
    /**
     * 获取缩略图
     */
    async getThumbnail(format = 'jpeg') {
        return this.imageExtractor.getThumbnail(format);
    }
    /**
     * 获取导航图
     */
    async getNavigateImage(format = 'jpeg') {
        return this.imageExtractor.getNavigateImage(format);
    }
    /**
     * 获取宏观图
     */
    async getMacroImage(format = 'jpeg') {
        return this.imageExtractor.getMacroImage(format);
    }
    /**
     * 获取标签图
     */
    async getLabelImage(format = 'jpeg') {
        return this.imageExtractor.getLabelImage(format);
    }
    /**
     * 获取宏观标签图
     */
    async getMacroLabelImage(format = 'jpeg') {
        return this.imageExtractor.getMacroLabelImage(format);
    }
    /**
     * 获取瓦片数据
     */
    async getTileData(layer, row, col, format = 'jpeg', focusId = 0) {
        return this.imageExtractor.getTileData(layer, row, col, format);
    }
    /**
     * 批量获取瓦片数据
     */
    async getTileDataBatch(requests) {
        const results = [];
        for (const request of requests) {
            try {
                const tileData = await this.getTileData(request.layer, request.row, request.col, request.format || 'jpeg', request.focusId || 0);
                results.push(tileData);
            }
            catch (error) {
                console.warn(`Failed to get tile data for layer ${request.layer}, row ${request.row}, col ${request.col}:`, error);
                results.push(null);
            }
        }
        return results;
    }
    /**
     * 获取指定层的所有瓦片信息
     */
    getLayerTileInfo(layerId) {
        const layerInfo = this.imageExtractor.getLayerInfo(layerId);
        if (!layerInfo) {
            return null;
        }
        return {
            totalTiles: layerInfo.tileRow * layerInfo.tileCol,
            tileRows: layerInfo.tileRow,
            tileCols: layerInfo.tileCol
        };
    }
    /**
     * 检查瓦片是否存在
     */
    async tileExists(layer, row, col) {
        try {
            const tileData = await this.getTileData(layer, row, col, 'raw');
            return tileData !== null && tileData.length > 0;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * 获取完整的TMAP信息
     */
    getTmapInfo() {
        return this.tmapReader.getTmapInfo();
    }
    /**
     * 获取基本信息
     */
    getBasicInfo() {
        return this.tmapReader.getBasicInfo();
    }
    /**
     * 获取层信息
     */
    getLayerInfo(layerId) {
        return this.imageExtractor.getLayerInfo(layerId);
    }
    /**
     * 获取所有层信息
     */
    getAllLayers() {
        return this.imageExtractor.getAllLayers();
    }
}
exports.TmapSDK = TmapSDK;
// 默认导出
exports.default = TmapSDK;
//# sourceMappingURL=index.js.map