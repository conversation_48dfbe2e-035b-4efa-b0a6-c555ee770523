/**
 * TMAP文件格式数据结构定义
 * 基于C++版本的结构体定义转换为TypeScript接口
 */
export declare enum ImageType {
    THUMBNAIL = 0,// 缩略图 <= 256
    NAVIGATE = 1,// 导航图 <= 640
    MACRO = 2,// 宏观图
    LABEL = 3,// 标签图 <= 256
    MACRO_LABEL = 4,// 宏观标签图 <= 256
    TILE = 5,// 瓦片图 = 256
    WHOLE = 6,// 整个图像
    ALL = 7
}
export declare enum CompressAlgo {
    QUICK = 0,
    JPEG = 1,
    J2K = 2
}
export declare enum TmapVersion {
    VERSION_2 = 2,
    VERSION_3 = 3,
    VERSION_4 = 4,
    VERSION_5 = 5,
    VERSION_6 = 6,
    VERSION_7 = 7
}
export interface TmapHeaderBase {
    header: string;
    mainVersion: string;
    maxFocusNumber: number;
    imageFormat: number;
    fileNumber: number;
    layerSize: number;
    imageColor: number;
    checksum: number;
    ratioStep: number;
    maxLayerSize: number;
    slideType: number;
    bkgColor: number;
    pixelSize: number;
}
export interface TmapHeader7 extends TmapHeaderBase {
    reserved: Buffer;
    compressAlgo: CompressAlgo;
    quality: number;
    maxZoomRate: number;
    imageNumber: number;
    layerNumber: number;
    tileNumber: number;
    extOffset: bigint;
    ocr: string;
    barcode: string;
    client: string;
    reservedEx: Buffer;
    checksum: number;
}
export interface TmapHeader5 extends TmapHeaderBase {
    imageWidth: number;
    imageHeight: number;
    tileWidth: number;
    tileHeight: number;
    imageRow: number;
    imageCol: number;
    totalImageNumber: number;
    shrinkTileNumber: number;
    airImageOffset: number;
    extInfoOffset: number;
}
export type TmapHeader = TmapHeader7 | TmapHeader5;
export interface ImageInfo7 {
    width: number;
    height: number;
    depth: number;
    type: ImageType;
    offset: bigint;
    length: number;
}
export interface ImageInfo5 {
    fileId: number;
    layer: number;
    reserved: Buffer;
    topDx: number;
    topDy: number;
    leftDx: number;
    leftDy: number;
    imageCol: number;
    imageRow: number;
    x: number;
    y: number;
    tiles: TileInfo5[];
}
export type ImageInfo = ImageInfo7 | ImageInfo5;
export interface LayerInfo {
    layerId: number;
    scale: number;
    width: number;
    height: number;
    tileRow: number;
    tileCol: number;
    offset: number;
    tileStart: number;
}
export interface TileInfo7 {
    layerId: number;
    focusId: number;
    x: number;
    y: number;
    width: number;
    height: number;
    offset: bigint;
    length: number;
}
export interface TileInfo5 {
    fileId: number;
    layer: number;
    x: number;
    y: number;
    fileOffset: number;
    length: number;
}
export interface ShrinkTileInfo {
    fileId: number;
    layerNo: number;
    x: number;
    y: number;
    fileOffset: number;
    length: number;
}
export type TileInfo = TileInfo7 | TileInfo5;
export interface TmapExtInfo {
    ocr: string;
    barcode: string;
    client: string;
    reserved: Buffer;
}
export interface TmapInfo7 {
    version: TmapVersion.VERSION_7;
    header: TmapHeader7;
    images: ImageInfo7[];
    layers: LayerInfo[];
    tiles: TileInfo7[];
}
export interface TmapInfo5 {
    version: TmapVersion.VERSION_5 | TmapVersion.VERSION_6;
    header: TmapHeader5;
    extInfo?: TmapExtInfo;
    images: ImageInfo5[];
    shrinkTiles: ShrinkTileInfo[];
    extData?: Buffer;
}
export type TmapInfo = TmapInfo7 | TmapInfo5;
export interface TileRequest {
    layer: number;
    row: number;
    col: number;
    focusId?: number;
    format?: 'raw' | 'jpeg' | 'png';
}
export interface TmapBasicInfo {
    version: string;
    imageSize: {
        width: number;
        height: number;
    };
    pixelSize: number;
    scanScale: number;
    layerCount: number;
    focusCount: number;
    tileCount: number;
    compressAlgo: CompressAlgo;
    quality: number;
}
export declare const CONSTANTS: {
    readonly MAX_IMAGE_NUM: 8;
    readonly MAX_LAYER_NUM: 16;
    readonly MAX_TILE_NUM: 256;
    readonly MAX_FILE_NUM: 16;
    readonly MAX_LAYER_SIZE: 16;
    readonly CLIENT_LEN: 8;
    readonly TILE_SIZE: 256;
    readonly PIXELSIZE_100X: 0.1;
    readonly HEADER_SIZE_V7: 256;
    readonly HEADER_SIZE_V5: 128;
    readonly IMAGE_INFO_7_SIZE: 32;
    readonly IMAGE_INFO_5_SIZE: 1024;
    readonly LAYER_INFO_SIZE: 32;
    readonly TILE_INFO_7_SIZE: 32;
    readonly SHRINK_TILE_INFO_SIZE: 16;
    readonly EXT_INFO_SIZE: 256;
    readonly MIN_SUPPORTED_VERSION: 2;
    readonly MAX_SUPPORTED_VERSION: 7;
    readonly SUPPORTED_COLOR_DEPTHS: readonly [8, 24, 32];
};
//# sourceMappingURL=tmap-structures.d.ts.map