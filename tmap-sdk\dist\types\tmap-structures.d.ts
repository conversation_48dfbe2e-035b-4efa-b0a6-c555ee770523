/**
 * TMAP文件格式数据结构定义
 * 基于C++版本的结构体定义转换为TypeScript接口
 */
export declare enum ImageType {
    THUMBNAIL = 0,// 缩略图 <= 256
    NAVIGATE = 1,// 导航图 <= 640
    MACRO = 2,// 宏观图
    LABEL = 3,// 标签图 <= 256
    MACRO_LABEL = 4,// 宏观标签图 <= 256
    TILE = 5,// 瓦片图 = 256
    WHOLE = 6,// 整个图像
    ALL = 7
}
export declare enum CompressAlgo {
    QUICK = 0,
    JPEG = 1,
    J2K = 2
}
export interface TmapHeader {
    header: string;
    mainVersion: string;
    reserved: Buffer;
    compressAlgo: CompressAlgo;
    quality: number;
    maxFocusNumber: number;
    maxZoomRate: number;
    bkgColor: number;
    pixelSize: number;
    imageNumber: number;
    layerNumber: number;
    tileNumber: number;
    extOffset: bigint;
    ocr: string;
    barcode: string;
    client: string;
    reservedEx: Buffer;
    checksum: number;
}
export interface ImageInfo {
    width: number;
    height: number;
    depth: number;
    type: ImageType;
    offset: bigint;
    length: number;
}
export interface LayerInfo {
    layerId: number;
    scale: number;
    width: number;
    height: number;
    tileRow: number;
    tileCol: number;
    offset: number;
    tileStart: number;
}
export interface TileInfo {
    layerId: number;
    focusId: number;
    x: number;
    y: number;
    width: number;
    height: number;
    offset: bigint;
    length: number;
}
export interface TmapInfo {
    header: TmapHeader;
    images: ImageInfo[];
    layers: LayerInfo[];
    tiles: TileInfo[];
}
export interface TileRequest {
    layer: number;
    row: number;
    col: number;
    format?: string;
}
export interface DziInfo {
    format: string;
    overlap: number;
    tileSize: number;
    size: {
        width: number;
        height: number;
    };
}
export interface TmapBasicInfo {
    version: string;
    imageSize: {
        width: number;
        height: number;
    };
    pixelSize: number;
    scanScale: number;
    layerCount: number;
    focusCount: number;
    tileCount: number;
    compressAlgo: CompressAlgo;
    quality: number;
}
export declare const CONSTANTS: {
    readonly MAX_IMAGE_NUM: 8;
    readonly MAX_LAYER_NUM: 16;
    readonly CLIENT_LEN: 8;
    readonly TILE_SIZE: 256;
    readonly HEADER_SIZE: 256;
    readonly PIXELSIZE_100X: 0.1;
};
//# sourceMappingURL=tmap-structures.d.ts.map