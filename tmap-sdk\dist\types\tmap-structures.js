"use strict";
/**
 * TMAP文件格式数据结构定义
 * 基于C++版本的结构体定义转换为TypeScript接口
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CONSTANTS = exports.TmapVersion = exports.CompressAlgo = exports.ImageType = void 0;
// 图像类型枚举
var ImageType;
(function (ImageType) {
    ImageType[ImageType["THUMBNAIL"] = 0] = "THUMBNAIL";
    ImageType[ImageType["NAVIGATE"] = 1] = "NAVIGATE";
    ImageType[ImageType["MACRO"] = 2] = "MACRO";
    ImageType[ImageType["LABEL"] = 3] = "LABEL";
    ImageType[ImageType["MACRO_LABEL"] = 4] = "MACRO_LABEL";
    ImageType[ImageType["TILE"] = 5] = "TILE";
    ImageType[ImageType["WHOLE"] = 6] = "WHOLE";
    ImageType[ImageType["ALL"] = 7] = "ALL";
})(ImageType || (exports.ImageType = ImageType = {}));
// 压缩算法枚举
var CompressAlgo;
(function (CompressAlgo) {
    CompressAlgo[CompressAlgo["QUICK"] = 0] = "QUICK";
    CompressAlgo[CompressAlgo["JPEG"] = 1] = "JPEG";
    CompressAlgo[CompressAlgo["J2K"] = 2] = "J2K";
})(CompressAlgo || (exports.CompressAlgo = CompressAlgo = {}));
// TMAP版本枚举
var TmapVersion;
(function (TmapVersion) {
    TmapVersion[TmapVersion["VERSION_2"] = 2] = "VERSION_2";
    TmapVersion[TmapVersion["VERSION_3"] = 3] = "VERSION_3";
    TmapVersion[TmapVersion["VERSION_4"] = 4] = "VERSION_4";
    TmapVersion[TmapVersion["VERSION_5"] = 5] = "VERSION_5";
    TmapVersion[TmapVersion["VERSION_6"] = 6] = "VERSION_6";
    TmapVersion[TmapVersion["VERSION_7"] = 7] = "VERSION_7";
})(TmapVersion || (exports.TmapVersion = TmapVersion = {}));
// 常量定义
exports.CONSTANTS = {
    // 通用常量
    MAX_IMAGE_NUM: 8,
    MAX_LAYER_NUM: 16,
    MAX_TILE_NUM: 256,
    MAX_FILE_NUM: 16,
    MAX_LAYER_SIZE: 16,
    CLIENT_LEN: 8,
    TILE_SIZE: 256,
    PIXELSIZE_100X: 0.1, // 100X下的标准像素大小
    // 版本相关的头部大小
    HEADER_SIZE_V7: 256, // TMAP 7.x 头部大小
    HEADER_SIZE_V5: 128, // TMAP 5.x/6.x 头部大小
    // 结构体大小
    IMAGE_INFO_7_SIZE: 32, // TMAP 7.x 图像信息结构大小
    IMAGE_INFO_5_SIZE: 1024, // TMAP 5.x 图像信息结构大小（包含瓦片数组）
    LAYER_INFO_SIZE: 32, // 层信息结构大小
    TILE_INFO_7_SIZE: 32, // TMAP 7.x 瓦片信息结构大小
    SHRINK_TILE_INFO_SIZE: 16, // 缩略瓦片信息结构大小
    EXT_INFO_SIZE: 256, // 扩展信息结构大小
    // 支持的版本范围
    MIN_SUPPORTED_VERSION: 2,
    MAX_SUPPORTED_VERSION: 7,
    // 支持的颜色深度
    SUPPORTED_COLOR_DEPTHS: [8, 24, 32],
};
//# sourceMappingURL=tmap-structures.js.map