"use strict";
/**
 * TMAP文件格式数据结构定义
 * 基于C++版本的结构体定义转换为TypeScript接口
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CONSTANTS = exports.CompressAlgo = exports.ImageType = void 0;
// 图像类型枚举
var ImageType;
(function (ImageType) {
    ImageType[ImageType["THUMBNAIL"] = 0] = "THUMBNAIL";
    ImageType[ImageType["NAVIGATE"] = 1] = "NAVIGATE";
    ImageType[ImageType["MACRO"] = 2] = "MACRO";
    ImageType[ImageType["LABEL"] = 3] = "LABEL";
    ImageType[ImageType["MACRO_LABEL"] = 4] = "MACRO_LABEL";
    ImageType[ImageType["TILE"] = 5] = "TILE";
    ImageType[ImageType["WHOLE"] = 6] = "WHOLE";
    ImageType[ImageType["ALL"] = 7] = "ALL";
})(ImageType || (exports.ImageType = ImageType = {}));
// 压缩算法枚举
var CompressAlgo;
(function (CompressAlgo) {
    CompressAlgo[CompressAlgo["QUICK"] = 0] = "QUICK";
    CompressAlgo[CompressAlgo["JPEG"] = 1] = "JPEG";
    CompressAlgo[CompressAlgo["J2K"] = 2] = "J2K";
})(CompressAlgo || (exports.CompressAlgo = CompressAlgo = {}));
// 常量定义
exports.CONSTANTS = {
    MAX_IMAGE_NUM: 8,
    MAX_LAYER_NUM: 16,
    CLIENT_LEN: 8,
    TILE_SIZE: 256,
    HEADER_SIZE: 256,
    PIXELSIZE_100X: 0.1, // 100X下的标准像素大小
};
//# sourceMappingURL=tmap-structures.js.map