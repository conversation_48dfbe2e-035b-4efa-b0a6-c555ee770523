/**
 * 二进制数据读取工具类
 * 用于从Buffer中按照指定格式读取数据
 */
export declare class BufferReader {
    private buffer;
    private position;
    constructor(buffer: Buffer);
    /**
     * 获取当前位置
     */
    getPosition(): number;
    /**
     * 设置当前位置
     */
    setPosition(position: number): void;
    /**
     * 跳过指定字节数
     */
    skip(bytes: number): void;
    /**
     * 检查是否还有足够的字节可读
     */
    hasBytes(count: number): boolean;
    /**
     * 读取8位无符号整数
     */
    readUInt8(): number;
    /**
     * 读取8位有符号整数
     */
    readInt8(): number;
    /**
     * 读取16位小端序有符号整数
     */
    readInt16LE(): number;
    /**
     * 读取16位小端序无符号整数
     */
    readUInt16LE(): number;
    /**
     * 读取32位小端序有符号整数
     */
    readInt32LE(): number;
    /**
     * 读取32位小端序无符号整数
     */
    readUInt32LE(): number;
    /**
     * 读取64位小端序有符号整数
     */
    readBigInt64LE(): bigint;
    /**
     * 读取32位小端序浮点数
     */
    readFloatLE(): number;
    /**
     * 读取指定长度的字符串（以null结尾）
     */
    readString(length: number, encoding?: BufferEncoding): string;
    /**
     * 读取指定长度的Buffer
     */
    readBuffer(length: number): Buffer;
    /**
     * 读取剩余的所有字节
     */
    readRemaining(): Buffer;
    /**
     * 获取剩余字节数
     */
    getRemainingBytes(): number;
    /**
     * 重置到开始位置
     */
    reset(): void;
}
//# sourceMappingURL=buffer-reader.d.ts.map