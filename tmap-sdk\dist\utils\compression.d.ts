/**
 * 压缩/解压缩工具
 * 支持JPEG和其他压缩格式的处理
 */
import { CompressAlgo } from '../types';
export declare class CompressionUtils {
    /**
     * 解压缩瓦片数据
     * @param compressedData 压缩的数据
     * @param algorithm 压缩算法
     * @param width 图像宽度
     * @param height 图像高度
     * @returns 解压缩后的图像数据
     */
    static decompressTileData(compressedData: Buffer, algorithm: CompressAlgo, width: number, height: number): Promise<Buffer>;
    /**
     * 解压缩JPEG数据
     * 暂时返回原始JPEG数据，实际应用中需要使用图像处理库
     */
    private static decompressJpeg;
    /**
     * 解压缩快速压缩数据
     * 这里需要根据实际的快速压缩算法实现
     */
    private static decompressQuick;
    /**
     * 将原始图像数据转换为JPEG
     * 暂时返回原始数据，实际应用中需要使用图像处理库
     */
    static convertToJpeg(rawData: Buffer, width: number, height: number, channels?: number, quality?: number): Promise<Buffer>;
    /**
     * 将原始图像数据转换为PNG
     * 暂时返回原始数据，实际应用中需要使用图像处理库
     */
    static convertToPng(rawData: Buffer, width: number, height: number, channels?: number): Promise<Buffer>;
    /**
     * 调整图像大小
     * 暂时返回原始数据，实际应用中需要使用图像处理库
     */
    static resizeImage(imageData: Buffer, originalWidth: number, originalHeight: number, newWidth: number, newHeight: number, channels?: number): Promise<Buffer>;
    /**
     * 创建填充背景的图像
     */
    static createFilledImage(width: number, height: number, backgroundColor?: number, channels?: number): Promise<Buffer>;
}
//# sourceMappingURL=compression.d.ts.map