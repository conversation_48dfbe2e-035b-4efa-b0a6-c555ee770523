<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TMAP SDK + OpenSeadragon 示例</title>
    <script src="https://cdn.jsdelivr.net/npm/openseadragon@4.1.0/build/openseadragon/openseadragon.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            display: flex;
            height: 600px;
        }
        .sidebar {
            width: 300px;
            padding: 20px;
            background: #ecf0f1;
            border-right: 1px solid #bdc3c7;
            overflow-y: auto;
        }
        .viewer-container {
            flex: 1;
            position: relative;
        }
        #viewer {
            width: 100%;
            height: 100%;
        }
        .info-section {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .info-section h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .info-item {
            margin: 5px 0;
            font-size: 14px;
        }
        .info-label {
            font-weight: bold;
            color: #34495e;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            text-align: center;
        }
        .status.error {
            background: #e74c3c;
            color: white;
        }
        .status.warning {
            background: #f39c12;
            color: white;
        }
        .status.success {
            background: #27ae60;
            color: white;
        }
        .button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #2980b9;
        }
        .button:disabled {
            background: #95a5a6;
            cursor: not-allowed;
        }
        .image-preview {
            max-width: 100%;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 TMAP SDK + OpenSeadragon 示例</h1>
            <p>数字病理图像查看器</p>
        </div>
        
        <div class="content">
            <div class="sidebar">
                <div class="info-section">
                    <h3>📡 服务器状态</h3>
                    <div id="server-status" class="status warning">
                        等待连接...
                    </div>
                    <button class="button" onclick="checkServer()">检查连接</button>
                </div>

                <div class="info-section">
                    <h3>📊 TMAP 信息</h3>
                    <div id="tmap-info">
                        <div class="info-item">
                            <span class="info-label">版本:</span>
                            <span id="version">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">图像尺寸:</span>
                            <span id="image-size">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">像素大小:</span>
                            <span id="pixel-size">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">扫描倍率:</span>
                            <span id="scan-scale">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">层数:</span>
                            <span id="layer-count">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">瓦片数:</span>
                            <span id="tile-count">-</span>
                        </div>
                    </div>
                    <button class="button" onclick="loadTmapInfo()">加载信息</button>
                </div>

                <div class="info-section">
                    <h3>🖼️ 图像预览</h3>
                    <button class="button" onclick="loadThumbnail()">缩略图</button>
                    <button class="button" onclick="loadNavigate()">导航图</button>
                    <button class="button" onclick="loadMacro()">宏观图</button>
                    <button class="button" onclick="loadLabel()">标签图</button>
                    <div id="image-preview"></div>
                </div>

                <div class="info-section">
                    <h3>🔧 查看器控制</h3>
                    <button class="button" onclick="loadViewer()">加载查看器</button>
                    <button class="button" onclick="resetViewer()">重置视图</button>
                    <button class="button" onclick="toggleFullscreen()">全屏</button>
                </div>
            </div>

            <div class="viewer-container">
                <div id="viewer"></div>
            </div>
        </div>
    </div>

    <script>
        const SERVER_URL = 'http://localhost:3000';
        let viewer = null;

        // 检查服务器状态
        async function checkServer() {
            const statusEl = document.getElementById('server-status');
            try {
                const response = await fetch(`${SERVER_URL}/info`);
                if (response.ok) {
                    statusEl.className = 'status success';
                    statusEl.textContent = '✅ 服务器连接正常';
                    loadTmapInfo();
                } else {
                    throw new Error('服务器响应错误');
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 无法连接到服务器';
                console.error('Server check failed:', error);
            }
        }

        // 加载TMAP信息
        async function loadTmapInfo() {
            try {
                const response = await fetch(`${SERVER_URL}/info`);
                const info = await response.json();
                
                document.getElementById('version').textContent = info.version || '-';
                document.getElementById('image-size').textContent = 
                    info.imageSize ? `${info.imageSize.width} × ${info.imageSize.height}` : '-';
                document.getElementById('pixel-size').textContent = 
                    info.pixelSize ? `${info.pixelSize} μm/pixel` : '-';
                document.getElementById('scan-scale').textContent = 
                    info.scanScale ? `${info.scanScale}X` : '-';
                document.getElementById('layer-count').textContent = info.layerCount || '-';
                document.getElementById('tile-count').textContent = info.tileCount || '-';
            } catch (error) {
                console.error('Failed to load TMAP info:', error);
            }
        }

        // 加载图像预览
        async function loadImage(type, name) {
            const previewEl = document.getElementById('image-preview');
            try {
                const response = await fetch(`${SERVER_URL}/${type}`);
                if (response.ok) {
                    const blob = await response.blob();
                    const url = URL.createObjectURL(blob);
                    previewEl.innerHTML = `
                        <h4>${name}</h4>
                        <img src="${url}" class="image-preview" alt="${name}">
                    `;
                } else {
                    previewEl.innerHTML = `<p>❌ 无法加载${name}</p>`;
                }
            } catch (error) {
                previewEl.innerHTML = `<p>❌ 加载${name}失败</p>`;
                console.error(`Failed to load ${type}:`, error);
            }
        }

        function loadThumbnail() { loadImage('thumbnail', '缩略图'); }
        function loadNavigate() { loadImage('navigate', '导航图'); }
        function loadMacro() { loadImage('macro', '宏观图'); }
        function loadLabel() { loadImage('label', '标签图'); }

        // 加载OpenSeadragon查看器
        function loadViewer() {
            try {
                if (viewer) {
                    viewer.destroy();
                }
                
                viewer = OpenSeadragon({
                    id: "viewer",
                    tileSources: `${SERVER_URL}/image.dzi`,
                    prefixUrl: "https://cdn.jsdelivr.net/npm/openseadragon@4.1.0/build/openseadragon/images/",
                    showNavigationControl: true,
                    showZoomControl: true,
                    showHomeControl: true,
                    showFullPageControl: true,
                    showSequenceControl: false,
                    mouseNavEnabled: true,
                    showNavigator: true,
                    navigatorPosition: "BOTTOM_RIGHT",
                    navigatorSizeRatio: 0.15,
                    animationTime: 0.5,
                    blendTime: 0.1,
                    constrainDuringPan: true,
                    maxZoomPixelRatio: 2,
                    minZoomLevel: 0.1,
                    visibilityRatio: 1,
                    zoomPerScroll: 2.0
                });

                viewer.addHandler('open', function() {
                    console.log('Viewer opened successfully');
                });

                viewer.addHandler('open-failed', function(event) {
                    console.error('Failed to open viewer:', event);
                    document.getElementById('viewer').innerHTML = 
                        '<div style="padding: 20px; text-align: center; color: #e74c3c;">❌ 无法加载图像<br>请确保TMAP服务器正在运行</div>';
                });

            } catch (error) {
                console.error('Failed to initialize viewer:', error);
            }
        }

        // 重置查看器视图
        function resetViewer() {
            if (viewer) {
                viewer.viewport.goHome();
            }
        }

        // 切换全屏
        function toggleFullscreen() {
            if (viewer) {
                viewer.setFullScreen(!viewer.isFullScreen());
            }
        }

        // 页面加载时自动检查服务器
        window.addEventListener('load', function() {
            checkServer();
        });
    </script>
</body>
</html>
