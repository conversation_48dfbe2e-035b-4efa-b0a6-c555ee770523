/**
 * TMAP SDK 使用示例
 */

import { TmapSDK } from './src';
import * as fs from 'fs';
import * as path from 'path';

async function main() {
  const tmapFilePath = 'E:\\TMAP\\Test_1.TMAP';

  // 检查文件是否存在
  if (!fs.existsSync(tmapFilePath)) {
    console.error(`TMAP file not found: ${tmapFilePath}`);
    console.log('Please update the file path in example.ts to point to your TMAP file');
    console.log('Example: const tmapFilePath = "C:\\\\path\\\\to\\\\your\\\\file.tmap";');
    console.log('\nFor testing purposes, you can create a simple demo without a real TMAP file:');
    console.log('npm run demo');
    return;
  }

  console.log('=== TMAP SDK 示例 ===\n');

  try {
    // 创建SDK实例
    const sdk = new TmapSDK(tmapFilePath);
    
    // 打开TMAP文件
    console.log('正在打开TMAP文件...');
    await sdk.open();
    console.log('✓ TMAP文件打开成功\n');

    // 获取基本信息
    console.log('=== 基本信息 ===');
    const basicInfo = sdk.getBasicInfo();
    console.log(`版本号: ${basicInfo.version}`);
    console.log(`图像尺寸: ${basicInfo.imageSize.width} x ${basicInfo.imageSize.height}`);
    console.log(`像素大小: ${basicInfo.pixelSize} μm/pixel`);
    console.log(`扫描倍率: ${basicInfo.scanScale}X`);
    console.log(`扫描层数: ${basicInfo.layerCount}`);
    console.log(`焦点数量: ${basicInfo.focusCount}`);
    console.log(`瓦片总数: ${basicInfo.tileCount}`);
    console.log(`压缩算法: ${basicInfo.compressAlgo}`);
    console.log(`压缩质量: ${basicInfo.quality}\n`);

    // 获取层信息
    console.log('=== 层信息 ===');
    const layers = sdk.getAllLayers();
    layers.forEach((layer, index) => {
      console.log(`层 ${index}:`);
      console.log(`  - ID: ${layer.layerId}`);
      console.log(`  - 缩放比例: ${layer.scale}`);
      console.log(`  - 尺寸: ${layer.width} x ${layer.height}`);
      console.log(`  - 瓦片数: ${layer.tileCol} x ${layer.tileRow}`);
    });
    console.log();

    // 提取各种图像
    console.log('=== 图像提取 ===');
    
    // 提取缩略图
    try {
      console.log('正在提取缩略图...');
      const thumbnail = await sdk.getThumbnail('jpeg');
      if (thumbnail) {
        const outputPath = 'thumbnail.jpg';
        fs.writeFileSync(outputPath, thumbnail);
        console.log(`✓ 缩略图已保存到: ${outputPath} (${thumbnail.length} bytes)`);
      } else {
        console.log('⚠ 未找到缩略图');
      }
    } catch (error) {
      console.log(`✗ 缩略图提取失败: ${error}`);
    }

    // 提取导航图
    try {
      console.log('正在提取导航图...');
      const navigate = await sdk.getNavigateImage('jpeg');
      if (navigate) {
        const outputPath = 'navigate.jpg';
        fs.writeFileSync(outputPath, navigate);
        console.log(`✓ 导航图已保存到: ${outputPath} (${navigate.length} bytes)`);
      } else {
        console.log('⚠ 未找到导航图');
      }
    } catch (error) {
      console.log(`✗ 导航图提取失败: ${error}`);
    }

    // 提取宏观图
    try {
      console.log('正在提取宏观图...');
      const macro = await sdk.getMacroImage('jpeg');
      if (macro) {
        const outputPath = 'macro.jpg';
        fs.writeFileSync(outputPath, macro);
        console.log(`✓ 宏观图已保存到: ${outputPath} (${macro.length} bytes)`);
      } else {
        console.log('⚠ 未找到宏观图');
      }
    } catch (error) {
      console.log(`✗ 宏观图提取失败: ${error}`);
    }

    // 提取标签图
    try {
      console.log('正在提取标签图...');
      const label = await sdk.getLabelImage('jpeg');
      if (label) {
        const outputPath = 'label.jpg';
        fs.writeFileSync(outputPath, label);
        console.log(`✓ 标签图已保存到: ${outputPath} (${label.length} bytes)`);
      } else {
        console.log('⚠ 未找到标签图');
      }
    } catch (error) {
      console.log(`✗ 标签图提取失败: ${error}`);
    }

    // 提取宏观标签图
    try {
      console.log('正在提取宏观标签图...');
      const macroLabel = await sdk.getMacroLabelImage('jpeg');
      if (macroLabel) {
        const outputPath = 'macro-label.jpg';
        fs.writeFileSync(outputPath, macroLabel);
        console.log(`✓ 宏观标签图已保存到: ${outputPath} (${macroLabel.length} bytes)`);
      } else {
        console.log('⚠ 未找到宏观标签图');
      }
    } catch (error) {
      console.log(`✗ 宏观标签图提取失败: ${error}`);
    }

    console.log();

    // 提取瓦片示例
    console.log('=== 瓦片提取示例 ===');
    try {
      console.log('正在提取瓦片 (层0, 行0, 列0)...');
      const tile = await sdk.getTileData(0, 0, 0, 'jpeg');
      if (tile) {
        const outputPath = 'tile_0_0_0.jpg';
        fs.writeFileSync(outputPath, tile);
        console.log(`✓ 瓦片已保存到: ${outputPath} (${tile.length} bytes)`);
      } else {
        console.log('⚠ 未找到指定瓦片');
      }
    } catch (error) {
      console.log(`✗ 瓦片提取失败: ${error}`);
    }

    // 瓦片操作示例
    console.log('\n=== 瓦片操作 ===');

    // 获取层级信息
    try {
      const layerInfo = sdk.getLayerTileInfo(0);
      if (layerInfo) {
        console.log(`✓ 层级0信息: ${layerInfo.tileCols}x${layerInfo.tileRows} = ${layerInfo.totalTiles}个瓦片`);
      }
    } catch (error) {
      console.log(`✗ 获取层级信息失败: ${error}`);
    }

    // 检查瓦片存在性
    try {
      const exists = await sdk.tileExists(0, 0, 0);
      console.log(`✓ 瓦片(0,0,0)存在: ${exists}`);
    } catch (error) {
      console.log(`✗ 检查瓦片存在性失败: ${error}`);
    }

    // 批量获取瓦片示例
    try {
      console.log('正在批量获取瓦片...');
      const requests = [
        { layer: 0, row: 0, col: 0, format: 'jpeg' as const },
        { layer: 0, row: 0, col: 1, format: 'jpeg' as const },
        { layer: 0, row: 1, col: 0, format: 'jpeg' as const }
      ];
      const tiles = await sdk.getTileDataBatch(requests);
      const successCount = tiles.filter(tile => tile !== null).length;
      console.log(`✓ 批量获取完成: ${successCount}/${requests.length} 个瓦片成功`);
    } catch (error) {
      console.log(`✗ 批量获取瓦片失败: ${error}`);
    }

    console.log('\n=== 操作完成 ===');
    sdk.close();

  } catch (error) {
    console.error('错误:', error);
  }
}

// 运行示例
if (require.main === module) {
  main().catch(console.error);
}
