/**
 * TMAP SDK 使用示例
 */

import { TmapSDK } from './src';
import * as fs from 'fs';
import * as path from 'path';

async function main() {
  const tmapFilePath = 'E:\\TMAP\\Test_1.TMAP';

  // 检查文件是否存在
  if (!fs.existsSync(tmapFilePath)) {
    console.error(`TMAP file not found: ${tmapFilePath}`);
    console.log('Please update the file path in example.ts to point to your TMAP file');
    console.log('Example: const tmapFilePath = "C:\\\\path\\\\to\\\\your\\\\file.tmap";');
    console.log('\nFor testing purposes, you can create a simple demo without a real TMAP file:');
    console.log('npm run demo');
    return;
  }

  console.log('=== TMAP SDK 示例 ===\n');

  try {
    // 创建SDK实例
    const sdk = new TmapSDK(tmapFilePath);
    
    // 打开TMAP文件
    console.log('正在打开TMAP文件...');
    await sdk.open();
    console.log('✓ TMAP文件打开成功\n');

    // 获取基本信息
    console.log('=== 基本信息 ===');
    const basicInfo = sdk.getBasicInfo();
    console.log(`版本号: ${basicInfo.version}`);
    console.log(`图像尺寸: ${basicInfo.imageSize.width} x ${basicInfo.imageSize.height}`);
    console.log(`像素大小: ${basicInfo.pixelSize} μm/pixel`);
    console.log(`扫描倍率: ${basicInfo.scanScale}X`);
    console.log(`扫描层数: ${basicInfo.layerCount}`);
    console.log(`焦点数量: ${basicInfo.focusCount}`);
    console.log(`瓦片总数: ${basicInfo.tileCount}`);
    console.log(`压缩算法: ${basicInfo.compressAlgo}`);
    console.log(`压缩质量: ${basicInfo.quality}\n`);

    // 获取层信息
    console.log('=== 层信息 ===');
    const layers = sdk.getAllLayers();
    layers.forEach((layer, index) => {
      console.log(`层 ${index}:`);
      console.log(`  - ID: ${layer.layerId}`);
      console.log(`  - 缩放比例: ${layer.scale}`);
      console.log(`  - 尺寸: ${layer.width} x ${layer.height}`);
      console.log(`  - 瓦片数: ${layer.tileCol} x ${layer.tileRow}`);
    });
    console.log();

    // 提取各种图像
    console.log('=== 图像提取 ===');
    
    // 提取缩略图
    try {
      console.log('正在提取缩略图...');
      const thumbnail = await sdk.getThumbnail('jpeg');
      if (thumbnail) {
        const outputPath = 'thumbnail.jpg';
        fs.writeFileSync(outputPath, thumbnail);
        console.log(`✓ 缩略图已保存到: ${outputPath} (${thumbnail.length} bytes)`);
      } else {
        console.log('⚠ 未找到缩略图');
      }
    } catch (error) {
      console.log(`✗ 缩略图提取失败: ${error}`);
    }

    // 提取导航图
    try {
      console.log('正在提取导航图...');
      const navigate = await sdk.getNavigateImage('jpeg');
      if (navigate) {
        const outputPath = 'navigate.jpg';
        fs.writeFileSync(outputPath, navigate);
        console.log(`✓ 导航图已保存到: ${outputPath} (${navigate.length} bytes)`);
      } else {
        console.log('⚠ 未找到导航图');
      }
    } catch (error) {
      console.log(`✗ 导航图提取失败: ${error}`);
    }

    // 提取宏观图
    try {
      console.log('正在提取宏观图...');
      const macro = await sdk.getMacroImage('jpeg');
      if (macro) {
        const outputPath = 'macro.jpg';
        fs.writeFileSync(outputPath, macro);
        console.log(`✓ 宏观图已保存到: ${outputPath} (${macro.length} bytes)`);
      } else {
        console.log('⚠ 未找到宏观图');
      }
    } catch (error) {
      console.log(`✗ 宏观图提取失败: ${error}`);
    }

    // 提取标签图
    try {
      console.log('正在提取标签图...');
      const label = await sdk.getLabelImage('jpeg');
      if (label) {
        const outputPath = 'label.jpg';
        fs.writeFileSync(outputPath, label);
        console.log(`✓ 标签图已保存到: ${outputPath} (${label.length} bytes)`);
      } else {
        console.log('⚠ 未找到标签图');
      }
    } catch (error) {
      console.log(`✗ 标签图提取失败: ${error}`);
    }

    // 提取宏观标签图
    try {
      console.log('正在提取宏观标签图...');
      const macroLabel = await sdk.getMacroLabelImage('jpeg');
      if (macroLabel) {
        const outputPath = 'macro-label.jpg';
        fs.writeFileSync(outputPath, macroLabel);
        console.log(`✓ 宏观标签图已保存到: ${outputPath} (${macroLabel.length} bytes)`);
      } else {
        console.log('⚠ 未找到宏观标签图');
      }
    } catch (error) {
      console.log(`✗ 宏观标签图提取失败: ${error}`);
    }

    console.log();

    // 提取瓦片示例
    console.log('=== 瓦片提取示例 ===');
    try {
      console.log('正在提取瓦片 (层0, 行0, 列0)...');
      const tile = await sdk.getTileData(0, 0, 0, 'jpeg');
      if (tile) {
        const outputPath = 'tile_0_0_0.jpg';
        fs.writeFileSync(outputPath, tile);
        console.log(`✓ 瓦片已保存到: ${outputPath} (${tile.length} bytes)`);
      } else {
        console.log('⚠ 未找到指定瓦片');
      }
    } catch (error) {
      console.log(`✗ 瓦片提取失败: ${error}`);
    }

    // 启动瓦片服务器示例
    console.log('\n=== 瓦片服务器 ===');
    console.log('正在启动瓦片服务器...');
    try {
      await sdk.startTileServer(3000);
      console.log('✓ 瓦片服务器已启动');
      console.log('OpenSeadragon DZI URL: http://localhost:3000/image.dzi');
      console.log('基本信息 URL: http://localhost:3000/info');
      console.log('缩略图 URL: http://localhost:3000/thumbnail');
      console.log('导航图 URL: http://localhost:3000/navigate');
      console.log('宏观图 URL: http://localhost:3000/macro');
      console.log('标签图 URL: http://localhost:3000/label');
      console.log('\n按 Ctrl+C 停止服务器');
      
      // 保持服务器运行
      process.on('SIGINT', () => {
        console.log('\n正在停止服务器...');
        sdk.stopTileServer();
        sdk.close();
        console.log('✓ 服务器已停止');
        process.exit(0);
      });
      
    } catch (error) {
      console.log(`✗ 瓦片服务器启动失败: ${error}`);
      sdk.close();
    }

  } catch (error) {
    console.error('错误:', error);
  }
}

// 运行示例
if (require.main === module) {
  main().catch(console.error);
}
