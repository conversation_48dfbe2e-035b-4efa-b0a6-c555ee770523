"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext = void 0;
const es2023_1 = require("./es2023");
const esnext_decorators_1 = require("./esnext.decorators");
const esnext_disposable_1 = require("./esnext.disposable");
const esnext_intl_1 = require("./esnext.intl");
exports.esnext = {
    ...es2023_1.es2023,
    ...esnext_intl_1.esnext_intl,
    ...esnext_decorators_1.esnext_decorators,
    ...esnext_disposable_1.esnext_disposable,
};
//# sourceMappingURL=esnext.js.map