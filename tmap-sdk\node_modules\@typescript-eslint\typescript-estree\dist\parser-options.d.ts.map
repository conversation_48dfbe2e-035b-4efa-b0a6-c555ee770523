{"version": 3, "file": "parser-options.d.ts", "sourceRoot": "", "sources": ["../src/parser-options.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,oBAAoB,EACpB,UAAU,EACV,gBAAgB,EACjB,MAAM,0BAA0B,CAAC;AAClC,OAAO,KAAK,KAAK,EAAE,MAAM,YAAY,CAAC;AAEtC,OAAO,KAAK,EAAE,QAAQ,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,aAAa,CAAC;AAM/E,UAAU,YAAY;IACpB;;;OAGG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAE1B;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAElB;;;;;;;;;OASG;IACH,UAAU,CAAC,EAAE,UAAU,CAAC;IAExB;;;OAGG;IACH,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAEhC;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;;;;;;;;OASG;IACH,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IAEpC;;;;;;;;OAQG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;IAEd;;;;OAIG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;IAOd,QAAQ,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC,CAAC;IAE/C;;;;OAIG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC;IAEhB;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB;;OAEG;IACH,kCAAkC,CAAC,EAAE,OAAO,CAAC;CAC9C;AAED;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACpC;;OAEG;IACH,2BAA2B,CAAC,EAAE,MAAM,EAAE,CAAC;CACxC;AAED,UAAU,+BAAgC,SAAQ,YAAY;IAC5D;;OAEG;IACH,2CAA2C,CAAC,EAAE,OAAO,CAAC;IAEtD;;;;;;OAMG;IACH,8BAA8B,CAAC,EAAE,OAAO,GAAG,qBAAqB,CAAC;IAEjE;;;;;;;;;OASG;IACH,gDAAgD,CAAC,EAAE,OAAO,CAAC;IAE3D;;;OAGG;IACH,mBAAmB,CAAC,EAAE,MAAM,EAAE,CAAC;IAE/B;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;;;;;;;;OASG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAE3B;;;;;;OAMG;IACH,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC;IAE7C;;;;;;OAMG;IACH,uBAAuB,CAAC,EAAE,MAAM,EAAE,CAAC;IAEnC;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IAEzB;;;;OAIG;IACH,QAAQ,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;IAE/B;;;OAGG;IACH,gCAAgC,CAAC,EAAE,OAAO,CAAC;IAE3C;;;;;;;;;;;OAWG;IACH,gCAAgC,CAAC,EAAE,OAAO,CAAC;IAE3C;;;;;;;;OAQG;IACH,aAAa,CAAC,EAAE;QACd;;WAEG;QACH,IAAI,CAAC,EAAE,oBAAoB,CAAC;KAC7B,CAAC;CACH;AAED,MAAM,MAAM,eAAe,GAAG,+BAA+B,CAAC;AAI9D,MAAM,WAAW,aAAa,CAAC,IAAI,EAAE,UAAU;IAC7C,GAAG,CAAC,MAAM,SAAS,UAAU,EAAE,GAAG,EAAE,IAAI,GAAG,MAAM,CAAC;IAClD,GAAG,CAAC,GAAG,EAAE,OAAO,GAAG,OAAO,CAAC;CAC5B;AAED,MAAM,WAAW,2BAA2B,CAC1C,IAAI,SAAS,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI;IAE1C,GAAG,CAAC,QAAQ,SAAS,IAAI,EAAE,GAAG,EAAE,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IACtE,GAAG,CAAC,GAAG,EAAE,OAAO,GAAG,OAAO,CAAC;CAC5B;AAED,MAAM,WAAW,sBAAsB;IACrC,qBAAqB,EAAE,2BAA2B,CAAC;IACnD,qBAAqB,EAAE,aAAa,CAAC,MAAM,GAAG,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;CACvE;AACD,MAAM,WAAW,iCACf,SAAQ,sBAAsB;IAC9B,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC;IACpB,mBAAmB,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,KAAK,EAAE,CAAC,MAAM,GAAG,SAAS,CAAC;IACpE,iBAAiB,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,CAAC;CACrD;AACD,MAAM,WAAW,oCACf,SAAQ,sBAAsB;IAC9B,OAAO,EAAE,IAAI,CAAC;CACf;AACD,MAAM,MAAM,cAAc,GACtB,oCAAoC,GACpC,iCAAiC,CAAC"}