import type { Scope, SourceCode } from '../ts-eslint';
import type { RuleContext } from '../ts-eslint/Rule';
import type { TSESTree } from '../ts-estree';
export declare function getAncestors(context: Readonly<RuleContext<string, unknown[]>>): TSESTree.Node[];
export declare function getCwd(context: Readonly<RuleContext<string, unknown[]>>): string;
export declare function getDeclaredVariables(context: Readonly<RuleContext<string, unknown[]>>, node: TSESTree.Node): readonly Scope.Variable[];
export declare function getFilename(context: Readonly<RuleContext<string, unknown[]>>): string;
export declare function getScope(context: Readonly<RuleContext<string, readonly unknown[]>>): Scope.Scope;
export declare function getSourceCode(context: Readonly<RuleContext<string, readonly unknown[]>>): Readonly<SourceCode>;
//# sourceMappingURL=context.d.ts.map