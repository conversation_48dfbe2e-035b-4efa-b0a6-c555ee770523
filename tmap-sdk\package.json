{"name": "tmap-sdk", "version": "1.0.0", "description": "Node.js/TypeScript SDK for parsing TMAP medical imaging files", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"tmap-cli": "./cli.js"}, "scripts": {"build": "tsc", "dev": "ts-node example.ts", "demo": "ts-node demo.ts", "cli": "node cli.js", "debug": "node debug-cli.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "start": "node dist/index.js"}, "keywords": ["tmap", "medical-imaging", "pathology", "openseadragon", "tiles", "microscopy"], "author": "TMAP SDK Team", "license": "MIT", "dependencies": {"sharp": "^0.33.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/jest": "^29.5.8", "typescript": "^5.3.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0"}, "engines": {"node": ">=16.0.0"}}