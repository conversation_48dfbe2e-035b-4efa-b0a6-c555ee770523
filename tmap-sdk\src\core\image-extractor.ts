/**
 * 图像提取器
 * 负责从TMAP文件中提取各种类型的图像数据
 */

import * as fs from 'fs';
import { TmapReader } from './tmap-reader';
import { CompressionUtils } from '../utils/compression';
import { ImageType, ImageInfo, ImageInfo7, ImageInfo5, TileInfo, TileInfo7, LayerInfo, TmapVersion } from '../types';

export class ImageExtractor {
  private tmapReader: TmapReader;

  constructor(tmapReader: TmapReader) {
    this.tmapReader = tmapReader;
  }

  /**
   * 获取指定类型的图像信息（仅支持TMAP 7.x）
   */
  getImageInfo(imageType: ImageType): ImageInfo | null {
    const tmapInfo = this.tmapReader.getTmapInfo();

    // 暂时只支持TMAP 7.x版本
    if (tmapInfo.version !== TmapVersion.VERSION_7) {
      console.warn('Image extraction currently only supports TMAP 7.x format');
      return null;
    }

    const tmapInfo7 = tmapInfo as any; // 临时类型断言
    for (const image of tmapInfo7.images) {
      if (image.type === imageType) {
        return image;
      }
    }

    return null;
  }

  /**
   * 提取指定类型的图像数据（仅支持TMAP 7.x）
   */
  async extractImage(imageType: ImageType, format: 'raw' | 'jpeg' | 'png' = 'jpeg'): Promise<Buffer | null> {
    const tmapInfo = this.tmapReader.getTmapInfo();

    // 暂时只支持TMAP 7.x版本
    if (tmapInfo.version !== TmapVersion.VERSION_7) {
      console.warn('Image extraction currently only supports TMAP 7.x format');
      return null;
    }

    const imageInfo = this.getImageInfo(imageType);
    if (!imageInfo) {
      return null;
    }

    if (!this.tmapReader.isFileOpen()) {
      throw new Error('TMAP file is not open');
    }

    // 读取压缩的图像数据
    const compressedData = await this.readImageData(imageInfo);

    if (format === 'raw') {
      return compressedData;
    }

    // 对于TMAP 7.x版本，使用类型断言
    const tmapInfo7 = tmapInfo as any;
    const imageInfo7 = imageInfo as any;

    // 解压缩图像数据
    const rawData = await CompressionUtils.decompressTileData(
      compressedData,
      tmapInfo7.header.compressAlgo,
      imageInfo7.width,
      imageInfo7.height
    );

    // 转换为指定格式
    const channels = imageInfo7.depth / 8;

    if (format === 'jpeg') {
      return await CompressionUtils.convertToJpeg(
        rawData,
        imageInfo7.width,
        imageInfo7.height,
        channels,
        tmapInfo7.header.quality
      );
    } else if (format === 'png') {
      return await CompressionUtils.convertToPng(
        rawData,
        imageInfo7.width,
        imageInfo7.height,
        channels
      );
    }

    return rawData;
  }

  /**
   * 获取缩略图
   */
  async getThumbnail(format: 'jpeg' | 'png' = 'jpeg'): Promise<Buffer | null> {
    return this.extractImage(ImageType.THUMBNAIL, format);
  }

  /**
   * 获取导航图
   */
  async getNavigateImage(format: 'jpeg' | 'png' = 'jpeg'): Promise<Buffer | null> {
    return this.extractImage(ImageType.NAVIGATE, format);
  }

  /**
   * 获取宏观图
   */
  async getMacroImage(format: 'jpeg' | 'png' = 'jpeg'): Promise<Buffer | null> {
    return this.extractImage(ImageType.MACRO, format);
  }

  /**
   * 获取标签图
   */
  async getLabelImage(format: 'jpeg' | 'png' = 'jpeg'): Promise<Buffer | null> {
    return this.extractImage(ImageType.LABEL, format);
  }

  /**
   * 获取宏观标签图
   */
  async getMacroLabelImage(format: 'jpeg' | 'png' = 'jpeg'): Promise<Buffer | null> {
    return this.extractImage(ImageType.MACRO_LABEL, format);
  }

  /**
   * 获取瓦片数据（仅支持TMAP 7.x）
   */
  async getTileData(layerId: number, row: number, col: number, format: 'raw' | 'jpeg' | 'png' = 'jpeg'): Promise<Buffer | null> {
    const tmapInfo = this.tmapReader.getTmapInfo();

    // 暂时只支持TMAP 7.x版本
    if (tmapInfo.version !== TmapVersion.VERSION_7) {
      console.warn('Tile extraction currently only supports TMAP 7.x format');
      return null;
    }

    const tmapInfo7 = tmapInfo as any;

    // 验证层ID
    if (layerId < 0 || layerId >= tmapInfo7.layers.length) {
      throw new Error(`Invalid layer ID: ${layerId}`);
    }

    const layer = tmapInfo7.layers[layerId];

    // 验证瓦片坐标
    if (row < 0 || row >= layer.tileRow || col < 0 || col >= layer.tileCol) {
      throw new Error(`Invalid tile coordinates: (${row}, ${col})`);
    }

    // 计算瓦片索引
    const tileIndex = this.calculateTileIndex(layerId, row, col);
    if (tileIndex >= tmapInfo7.tiles.length) {
      throw new Error(`Tile index out of range: ${tileIndex}`);
    }

    const tile = tmapInfo7.tiles[tileIndex];

    if (tile.length === 0) {
      // 返回空白瓦片
      return this.createEmptyTile(tile.width, tile.height, format);
    }

    // 读取瓦片数据
    const compressedData = await this.readTileData(tile);

    if (format === 'raw') {
      return compressedData;
    }

    // 解压缩瓦片数据
    const rawData = await CompressionUtils.decompressTileData(
      compressedData,
      tmapInfo7.header.compressAlgo,
      tile.width,
      tile.height
    );

    // 转换为指定格式
    const channels = 3; // 假设RGB格式

    if (format === 'jpeg') {
      return await CompressionUtils.convertToJpeg(
        rawData,
        tile.width,
        tile.height,
        channels,
        tmapInfo7.header.quality
      );
    } else if (format === 'png') {
      return await CompressionUtils.convertToPng(
        rawData,
        tile.width,
        tile.height,
        channels
      );
    }

    return rawData;
  }

  /**
   * 读取图像数据（仅支持TMAP 7.x）
   */
  private async readImageData(imageInfo: ImageInfo): Promise<Buffer> {
    const filePath = (this.tmapReader as any).filePath;
    const fileHandle = fs.openSync(filePath, 'r');

    try {
      const imageInfo7 = imageInfo as any;
      const buffer = Buffer.alloc(imageInfo7.length);
      fs.readSync(fileHandle, buffer, 0, imageInfo7.length, Number(imageInfo7.offset));
      return buffer;
    } finally {
      fs.closeSync(fileHandle);
    }
  }

  /**
   * 读取瓦片数据（仅支持TMAP 7.x）
   */
  private async readTileData(tile: TileInfo): Promise<Buffer> {
    const filePath = (this.tmapReader as any).filePath;
    const fileHandle = fs.openSync(filePath, 'r');

    try {
      const tile7 = tile as any;
      const buffer = Buffer.alloc(tile7.length);
      fs.readSync(fileHandle, buffer, 0, tile7.length, Number(tile7.offset));
      return buffer;
    } finally {
      fs.closeSync(fileHandle);
    }
  }

  /**
   * 计算瓦片索引（仅支持TMAP 7.x）
   */
  private calculateTileIndex(layerId: number, row: number, col: number, focusId: number = 0): number {
    const tmapInfo = this.tmapReader.getTmapInfo();
    const tmapInfo7 = tmapInfo as any;
    const layer = tmapInfo7.layers[layerId];

    // 计算焦点偏移
    const maxFocus = tmapInfo7.header.maxFocusNumber;
    const actualFocusId = focusId >= 0 ? focusId : maxFocus - focusId;
    const focusOffset = actualFocusId * layer.tileCol * layer.tileRow;

    return layer.tileStart + focusOffset + row * layer.tileCol + col;
  }

  /**
   * 创建空白瓦片（仅支持TMAP 7.x）
   */
  private async createEmptyTile(width: number, height: number, format: 'raw' | 'jpeg' | 'png'): Promise<Buffer> {
    const tmapInfo = this.tmapReader.getTmapInfo();
    const tmapInfo7 = tmapInfo as any;
    const backgroundColor = tmapInfo7.header.bkgColor || 255;
    const channels = 3;

    const rawData = await CompressionUtils.createFilledImage(
      width,
      height,
      backgroundColor,
      channels
    );

    if (format === 'raw') {
      return rawData;
    } else if (format === 'jpeg') {
      return await CompressionUtils.convertToJpeg(
        rawData,
        width,
        height,
        channels,
        tmapInfo7.header.quality || 90
      );
    } else if (format === 'png') {
      return await CompressionUtils.convertToPng(
        rawData,
        width,
        height,
        channels
      );
    }

    return rawData;
  }

  /**
   * 获取层信息（仅支持TMAP 7.x）
   */
  getLayerInfo(layerId: number): LayerInfo | null {
    const tmapInfo = this.tmapReader.getTmapInfo();

    if (tmapInfo.version !== TmapVersion.VERSION_7) {
      console.warn('Layer info currently only supports TMAP 7.x format');
      return null;
    }

    const tmapInfo7 = tmapInfo as any;

    if (layerId < 0 || layerId >= tmapInfo7.layers.length) {
      return null;
    }

    return tmapInfo7.layers[layerId];
  }

  /**
   * 获取所有层信息（仅支持TMAP 7.x）
   */
  getAllLayers(): LayerInfo[] {
    const tmapInfo = this.tmapReader.getTmapInfo();

    if (tmapInfo.version !== TmapVersion.VERSION_7) {
      console.warn('Layer info currently only supports TMAP 7.x format');
      return [];
    }

    const tmapInfo7 = tmapInfo as any;
    return tmapInfo7.layers || [];
  }
}
