/**
 * TMAP 5.x/6.x 版本文件读取器
 * 负责解析TMAP 5.x和6.x版本的文件格式
 */

import * as fs from 'fs';
import { BufferReader } from '../utils/buffer-reader';
import {
  TmapInfo5,
  TmapHeader5,
  TmapExtInfo,
  ImageInfo5,
  TileInfo5,
  ShrinkTileInfo,
  TmapVersion,
  CONSTANTS
} from '../types';

export class TmapReaderV5 {
  private filePath: string;
  private fileHandle: number | null = null;
  private version: TmapVersion;

  constructor(filePath: string, version: TmapVersion) {
    this.filePath = filePath;
    this.version = version;
  }

  /**
   * 解析TMAP 5.x/6.x文件
   */
  async parse(): Promise<TmapInfo5> {
    this.fileHandle = fs.openSync(this.filePath, 'r');
    
    try {
      // 解析文件头
      const header = await this.parseHeader();
      
      // 解析扩展信息（5.x及以上版本）
      let extInfo: TmapExtInfo | undefined;
      if (this.version >= TmapVersion.VERSION_5) {
        extInfo = await this.parseExtInfo();
      }
      
      // 解析图像信息
      const images = await this.parseImages(header);
      
      // 解析缩略瓦片信息
      const shrinkTiles = await this.parseShrinkTiles(header);
      
      return {
        version: this.version as TmapVersion.VERSION_5 | TmapVersion.VERSION_6,
        header,
        extInfo,
        images,
        shrinkTiles
      };
    } finally {
      if (this.fileHandle !== null) {
        fs.closeSync(this.fileHandle);
        this.fileHandle = null;
      }
    }
  }

  /**
   * 解析文件头
   */
  private async parseHeader(): Promise<TmapHeader5> {
    if (!this.fileHandle) {
      throw new Error('File not opened');
    }

    const headerBuffer = Buffer.alloc(CONSTANTS.HEADER_SIZE_V5);
    fs.readSync(this.fileHandle, headerBuffer, 0, CONSTANTS.HEADER_SIZE_V5, 0);
    
    const reader = new BufferReader(headerBuffer);
    
    const header: TmapHeader5 = {
      header: reader.readString(4),
      mainVersion: reader.readString(2),
      maxFocusNumber: reader.readUInt8(),
      imageFormat: reader.readUInt8(),
      fileNumber: reader.readUInt8(),
      layerSize: reader.readUInt8(),
      imageColor: reader.readUInt8(),
      checksum: reader.readUInt8(),
      ratioStep: reader.readUInt8(),
      maxLayerSize: reader.readUInt8(),
      slideType: reader.readUInt8(),
      bkgColor: reader.readUInt8(),
      pixelSize: reader.readFloatLE(),
      imageWidth: reader.readInt32LE(),
      imageHeight: reader.readInt32LE(),
      tileWidth: reader.readInt32LE(),
      tileHeight: reader.readInt32LE(),
      imageRow: reader.readInt32LE(),
      imageCol: reader.readInt32LE(),
      totalImageNumber: reader.readInt32LE(),
      shrinkTileNumber: reader.readInt32LE(),
      airImageOffset: reader.readInt32LE(),
      extInfoOffset: reader.readInt32LE()
    };

    // 验证头部信息
    this.validateHeader(header);
    
    return header;
  }

  /**
   * 验证文件头
   */
  private validateHeader(header: TmapHeader5): void {
    if (header.header !== 'TMAP') {
      throw new Error(`Invalid TMAP header: ${header.header}`);
    }

    const version = parseInt(header.mainVersion[1]);
    if (version !== this.version) {
      throw new Error(`Version mismatch: expected ${this.version}, got ${version}`);
    }

    if (!CONSTANTS.SUPPORTED_COLOR_DEPTHS.includes(header.imageColor as any)) {
      throw new Error(`Unsupported color depth: ${header.imageColor}`);
    }

    if (header.imageWidth <= 0 || header.imageHeight <= 0) {
      throw new Error('Invalid image dimensions');
    }

    if (header.tileWidth <= 0 || header.tileHeight <= 0) {
      throw new Error('Invalid tile dimensions');
    }
  }

  /**
   * 解析扩展信息
   */
  private async parseExtInfo(): Promise<TmapExtInfo> {
    if (!this.fileHandle) {
      throw new Error('File not opened');
    }

    const extInfoBuffer = Buffer.alloc(CONSTANTS.EXT_INFO_SIZE);
    const offset = CONSTANTS.HEADER_SIZE_V5;
    
    fs.readSync(this.fileHandle, extInfoBuffer, 0, CONSTANTS.EXT_INFO_SIZE, offset);
    
    const reader = new BufferReader(extInfoBuffer);
    
    return {
      ocr: reader.readString(64),
      barcode: reader.readString(64),
      client: reader.readString(CONSTANTS.CLIENT_LEN + 1),
      reserved: reader.readBuffer(CONSTANTS.EXT_INFO_SIZE - 64 - 64 - CONSTANTS.CLIENT_LEN - 1)
    };
  }

  /**
   * 解析图像信息
   */
  private async parseImages(header: TmapHeader5): Promise<ImageInfo5[]> {
    if (!this.fileHandle) {
      throw new Error('File not opened');
    }

    const images: ImageInfo5[] = [];
    const imageCount = this.version > TmapVersion.VERSION_3 ?
                      header.totalImageNumber :
                      header.imageRow * header.imageCol;

    // 检查文件大小
    const fileStats = fs.fstatSync(this.fileHandle);
    let offset = CONSTANTS.HEADER_SIZE_V5;
    if (this.version >= TmapVersion.VERSION_5) {
      offset += CONSTANTS.EXT_INFO_SIZE;
    }

    const imageDataSize = imageCount * CONSTANTS.IMAGE_INFO_5_SIZE;
    const requiredSize = offset + imageDataSize;

    if (fileStats.size < requiredSize) {
      console.warn(`File too small for expected image data. File size: ${fileStats.size}, required: ${requiredSize}`);
      console.warn(`Attempting to read available data only...`);

      // 计算实际可读取的图像数量
      const availableSize = fileStats.size - offset;
      const actualImageCount = Math.floor(availableSize / CONSTANTS.IMAGE_INFO_5_SIZE);

      if (actualImageCount <= 0) {
        console.warn('No image data available');
        return images;
      }

      console.warn(`Reading ${actualImageCount} images instead of ${imageCount}`);
      return this.parseImagesWithCount(actualImageCount, offset);
    }

    const imageBuffer = Buffer.alloc(imageDataSize);
    const bytesRead = fs.readSync(this.fileHandle, imageBuffer, 0, imageDataSize, offset);

    if (bytesRead !== imageDataSize) {
      console.warn(`Expected to read ${imageDataSize} bytes, but read ${bytesRead} bytes`);
      // 尝试解析实际读取的数据
      const actualImageCount = Math.floor(bytesRead / CONSTANTS.IMAGE_INFO_5_SIZE);
      return this.parseImagesFromBuffer(imageBuffer.subarray(0, bytesRead), actualImageCount);
    }

    return this.parseImagesFromBuffer(imageBuffer, imageCount);
  }

  /**
   * 从缓冲区解析图像信息
   */
  private parseImagesFromBuffer(buffer: Buffer, imageCount: number): ImageInfo5[] {
    const images: ImageInfo5[] = [];
    const reader = new BufferReader(buffer);

    for (let i = 0; i < imageCount; i++) {
      try {
        const image: ImageInfo5 = {
          fileId: reader.readUInt8(),
          layer: reader.readInt8(), // 修正：这里应该是有符号字节
          reserved: reader.readBuffer(2),
          topDx: reader.readInt8(), // 修正：这里应该是有符号字节
          topDy: reader.readInt8(), // 修正：这里应该是有符号字节
          leftDx: reader.readInt8(), // 修正：这里应该是有符号字节
          leftDy: reader.readInt8(), // 修正：这里应该是有符号字节
          imageCol: reader.readInt16LE(), // 修正：这里应该是16位
          imageRow: reader.readInt16LE(), // 修正：这里应该是16位
          x: reader.readInt32LE(),
          y: reader.readInt32LE(),
          tiles: []
        };

        // 读取瓦片信息（每个图像最多有MAX_TILE_NUM个瓦片）
        for (let j = 0; j < CONSTANTS.MAX_TILE_NUM; j++) {
          if (!reader.hasBytes(18)) { // 每个瓦片信息18字节
            console.warn(`Not enough data for tile ${j} in image ${i}`);
            break;
          }

          const tileFileId = reader.readUInt8();
          const tileLayer = reader.readInt8(); // 修正：有符号字节
          const tileX = reader.readInt32LE();
          const tileY = reader.readInt32LE();
          const tileOffset = reader.readInt32LE();
          const tileLength = reader.readInt32LE();

          // 只添加有效的瓦片（长度大于0）
          if (tileLength > 0) {
            image.tiles.push({
              fileId: tileFileId,
              layer: tileLayer,
              x: tileX,
              y: tileY,
              fileOffset: tileOffset,
              length: tileLength
            });
          }
        }

        images.push(image);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`Failed to parse image ${i}: ${errorMessage}`);
        break;
      }
    }

    return images;
  }

  /**
   * 解析指定数量的图像信息
   */
  private async parseImagesWithCount(imageCount: number, offset: number): Promise<ImageInfo5[]> {
    if (!this.fileHandle) {
      throw new Error('File not opened');
    }

    const imageDataSize = imageCount * CONSTANTS.IMAGE_INFO_5_SIZE;
    const imageBuffer = Buffer.alloc(imageDataSize);

    const bytesRead = fs.readSync(this.fileHandle, imageBuffer, 0, imageDataSize, offset);
    const actualImageCount = Math.floor(bytesRead / CONSTANTS.IMAGE_INFO_5_SIZE);

    return this.parseImagesFromBuffer(imageBuffer.subarray(0, bytesRead), actualImageCount);
  }

  /**
   * 解析缩略瓦片信息
   */
  private async parseShrinkTiles(header: TmapHeader5): Promise<ShrinkTileInfo[]> {
    if (!this.fileHandle) {
      throw new Error('File not opened');
    }

    const shrinkTiles: ShrinkTileInfo[] = [];
    const shrinkTileCount = header.shrinkTileNumber;

    if (shrinkTileCount <= 0) {
      return shrinkTiles;
    }

    // 计算偏移量
    const imageCount = this.version > TmapVersion.VERSION_3 ?
                      header.totalImageNumber :
                      header.imageRow * header.imageCol;

    let offset = CONSTANTS.HEADER_SIZE_V5;
    if (this.version >= TmapVersion.VERSION_5) {
      offset += CONSTANTS.EXT_INFO_SIZE;
    }
    offset += imageCount * CONSTANTS.IMAGE_INFO_5_SIZE;

    const shrinkTileDataSize = shrinkTileCount * CONSTANTS.SHRINK_TILE_INFO_SIZE;

    // 检查文件大小
    const fileStats = fs.fstatSync(this.fileHandle);
    const requiredSize = offset + shrinkTileDataSize;

    if (fileStats.size < requiredSize) {
      console.warn(`File too small for shrink tile data. File size: ${fileStats.size}, required: ${requiredSize}`);
      const availableSize = fileStats.size - offset;
      if (availableSize <= 0) {
        return shrinkTiles;
      }

      const actualShrinkTileCount = Math.floor(availableSize / CONSTANTS.SHRINK_TILE_INFO_SIZE);
      console.warn(`Reading ${actualShrinkTileCount} shrink tiles instead of ${shrinkTileCount}`);

      if (actualShrinkTileCount <= 0) {
        return shrinkTiles;
      }

      return this.parseShrinkTilesWithCount(actualShrinkTileCount, offset);
    }

    const shrinkTileBuffer = Buffer.alloc(shrinkTileDataSize);
    const bytesRead = fs.readSync(this.fileHandle, shrinkTileBuffer, 0, shrinkTileDataSize, offset);

    if (bytesRead !== shrinkTileDataSize) {
      console.warn(`Expected to read ${shrinkTileDataSize} bytes for shrink tiles, but read ${bytesRead} bytes`);
      const actualShrinkTileCount = Math.floor(bytesRead / CONSTANTS.SHRINK_TILE_INFO_SIZE);
      return this.parseShrinkTilesFromBuffer(shrinkTileBuffer.subarray(0, bytesRead), actualShrinkTileCount);
    }

    return this.parseShrinkTilesFromBuffer(shrinkTileBuffer, shrinkTileCount);
  }

  /**
   * 从缓冲区解析缩略瓦片信息
   */
  private parseShrinkTilesFromBuffer(buffer: Buffer, shrinkTileCount: number): ShrinkTileInfo[] {
    const shrinkTiles: ShrinkTileInfo[] = [];
    const reader = new BufferReader(buffer);

    for (let i = 0; i < shrinkTileCount; i++) {
      try {
        if (!reader.hasBytes(CONSTANTS.SHRINK_TILE_INFO_SIZE)) {
          console.warn(`Not enough data for shrink tile ${i}`);
          break;
        }

        const shrinkTile: ShrinkTileInfo = {
          fileId: reader.readUInt8(),
          layerNo: reader.readUInt8(),
          x: reader.readInt32LE(),
          y: reader.readInt32LE(),
          fileOffset: reader.readInt32LE(),
          length: reader.readUInt32LE()
        };

        shrinkTiles.push(shrinkTile);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.warn(`Failed to parse shrink tile ${i}: ${errorMessage}`);
        break;
      }
    }

    return shrinkTiles;
  }

  /**
   * 解析指定数量的缩略瓦片信息
   */
  private async parseShrinkTilesWithCount(shrinkTileCount: number, offset: number): Promise<ShrinkTileInfo[]> {
    if (!this.fileHandle) {
      throw new Error('File not opened');
    }

    const shrinkTileDataSize = shrinkTileCount * CONSTANTS.SHRINK_TILE_INFO_SIZE;
    const shrinkTileBuffer = Buffer.alloc(shrinkTileDataSize);

    const bytesRead = fs.readSync(this.fileHandle, shrinkTileBuffer, 0, shrinkTileDataSize, offset);
    const actualShrinkTileCount = Math.floor(bytesRead / CONSTANTS.SHRINK_TILE_INFO_SIZE);

    return this.parseShrinkTilesFromBuffer(shrinkTileBuffer.subarray(0, bytesRead), actualShrinkTileCount);
  }


}
