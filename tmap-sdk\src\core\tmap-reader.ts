/**
 * TMAP文件读取器
 * 负责解析TMAP文件格式并提供数据访问接口
 * 支持TMAP 2.x - 7.x版本
 */

import * as fs from 'fs';
import { BufferReader } from '../utils/buffer-reader';
import { VersionDetector, VersionInfo } from './version-detector';
import { TmapReaderV5 } from './tmap-reader-v5';
import {
  TmapInfo,
  TmapInfo7,
  TmapInfo5,
  TmapHeader7,
  ImageInfo7,
  LayerInfo,
  TileInfo7,
  ImageType,
  CompressAlgo,
  TmapBasicInfo,
  TmapVersion,
  CONSTANTS
} from '../types';

export class TmapReader {
  private filePath: string;
  private tmapInfo: TmapInfo | null = null;
  private versionInfo: VersionInfo | null = null;
  private isOpen: boolean = false;

  constructor(filePath: string) {
    this.filePath = filePath;
  }

  /**
   * 打开TMAP文件
   */
  async open(): Promise<void> {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(this.filePath)) {
        throw new Error(`TMAP file not found: ${this.filePath}`);
      }

      // 检测版本
      this.versionInfo = VersionDetector.detectVersion(this.filePath);
      console.log(`Detected TMAP version: ${this.versionInfo.version}, parser: ${this.versionInfo.parserType}`);

      if (!this.versionInfo.isSupported) {
        throw new Error(`Unsupported TMAP version: ${this.versionInfo.version}`);
      }

      // 验证文件
      if (!VersionDetector.validateFile(this.filePath, this.versionInfo)) {
        throw new Error('TMAP file validation failed');
      }

      // 根据版本选择解析器
      await this.parseByVersion();

      this.isOpen = true;
    } catch (error) {
      this.close();
      throw new Error(`Failed to open TMAP file: ${error}`);
    }
  }

  /**
   * 关闭文件
   */
  close(): void {
    this.isOpen = false;
  }

  /**
   * 根据版本选择解析器
   */
  private async parseByVersion(): Promise<void> {
    if (!this.versionInfo) {
      throw new Error('Version info not available');
    }

    if (this.versionInfo.parserType === 'v7') {
      await this.parseV7();
    } else if (this.versionInfo.parserType === 'v5') {
      await this.parseV5();
    } else {
      throw new Error(`Unsupported parser type: ${this.versionInfo.parserType}`);
    }
  }

  /**
   * 解析TMAP 7.x版本
   */
  private async parseV7(): Promise<void> {
    const fileHandle = fs.openSync(this.filePath, 'r');

    try {
      // 检查文件大小
      const fileStats = fs.fstatSync(fileHandle);
      if (fileStats.size < CONSTANTS.HEADER_SIZE_V7) {
        throw new Error(`File too small for TMAP 7.x format. Expected at least ${CONSTANTS.HEADER_SIZE_V7} bytes, got ${fileStats.size} bytes`);
      }

      // 读取文件头
      const headerBuffer = Buffer.alloc(CONSTANTS.HEADER_SIZE_V7);
      const bytesRead = fs.readSync(fileHandle, headerBuffer, 0, CONSTANTS.HEADER_SIZE_V7, 0);

      if (bytesRead !== CONSTANTS.HEADER_SIZE_V7) {
        throw new Error(`Failed to read complete header. Expected ${CONSTANTS.HEADER_SIZE_V7} bytes, read ${bytesRead} bytes`);
      }

      const reader = new BufferReader(headerBuffer);

      // 解析头部信息（按照C++结构体的确切顺序）
      const header: TmapHeader7 = {
        header: reader.readString(4),                    // char acHeader[4]
        mainVersion: reader.readString(2),               // char acMainVersion[2]
        reserved: reader.readBuffer(2),                  // char acReserved[2]
        compressAlgo: reader.readUInt8() as CompressAlgo, // uchar ucCompressAlgo
        quality: reader.readUInt8(),                     // uchar ucQuality
        maxFocusNumber: reader.readUInt8(),              // uchar ucMaxFocusNumber
        maxZoomRate: reader.readUInt8(),                 // uchar ucMaxZoomRate
        bkgColor: reader.readUInt8(),                    // uchar ucBkgColor
        pixelSize: reader.readFloatLE(),                 // float fPixelSize
        imageNumber: reader.readInt32LE(),               // int nImageNumber
        layerNumber: reader.readInt32LE(),               // int nLayerNumber
        tileNumber: reader.readInt32LE(),                // int nTileNumber
        extOffset: reader.readBigInt64LE(),              // int64 lExtOffset
        ocr: reader.readString(64),                      // char acOCR[64]
        barcode: reader.readString(64),                  // char acBarcode[64]
        client: reader.readString(CONSTANTS.CLIENT_LEN + 1), // char acClient[CLIENT_LEN + 1]
        reservedEx: reader.readBuffer(128 - CONSTANTS.CLIENT_LEN - 1), // char acReservedEx[...]
        checksum: reader.readInt32LE(),                  // int nChecksum
        // 为了兼容性，添加缺失的字段（设置默认值）
        imageFormat: 0,
        fileNumber: 1,
        layerSize: 0,
        imageColor: 24,
        ratioStep: 1,
        maxLayerSize: 0,
        slideType: 0
      };

      // 读取图像信息
      const images = await this.parseImagesV7(fileHandle, header.imageNumber);

      // 读取层信息
      const layers = await this.parseLayersV7(fileHandle, header.layerNumber, header.imageNumber);

      // 读取瓦片信息
      const tiles = await this.parseTilesV7(fileHandle, header.tileNumber, header.imageNumber, header.layerNumber);

      this.tmapInfo = {
        version: TmapVersion.VERSION_7,
        header,
        images,
        layers,
        tiles
      } as TmapInfo7;
    } finally {
      fs.closeSync(fileHandle);
    }
  }

  /**
   * 解析TMAP 5.x/6.x版本
   */
  private async parseV5(): Promise<void> {
    if (!this.versionInfo) {
      throw new Error('Version info not available');
    }

    const readerV5 = new TmapReaderV5(this.filePath, this.versionInfo.version);
    this.tmapInfo = await readerV5.parse();
  }

  /**
   * 解析TMAP 7.x版本的图像信息
   */
  private async parseImagesV7(fileHandle: number, imageCount: number): Promise<ImageInfo7[]> {
    const images: ImageInfo7[] = [];
    const imageStructSize = CONSTANTS.IMAGE_INFO_7_SIZE;
    const imagesBuffer = Buffer.alloc(imageCount * imageStructSize);

    fs.readSync(fileHandle, imagesBuffer, 0, imagesBuffer.length, CONSTANTS.HEADER_SIZE_V7);

    const reader = new BufferReader(imagesBuffer);

    for (let i = 0; i < imageCount; i++) {
      const image: ImageInfo7 = {
        width: reader.readInt32LE(),
        height: reader.readInt32LE(),
        depth: reader.readInt32LE(),
        type: reader.readInt32LE() as ImageType,
        offset: reader.readBigInt64LE(),
        length: reader.readInt32LE()
      };

      // 跳过填充字节
      reader.skip(4);

      images.push(image);
    }

    return images;
  }

  /**
   * 解析TMAP 7.x版本的层信息
   */
  private async parseLayersV7(fileHandle: number, layerCount: number, imageCount: number): Promise<LayerInfo[]> {
    const layers: LayerInfo[] = [];
    const layerStructSize = CONSTANTS.LAYER_INFO_SIZE;
    const layersBuffer = Buffer.alloc(layerCount * layerStructSize);

    const imageStructSize = CONSTANTS.IMAGE_INFO_7_SIZE;
    const offset = CONSTANTS.HEADER_SIZE_V7 + imageCount * imageStructSize;

    fs.readSync(fileHandle, layersBuffer, 0, layersBuffer.length, offset);

    const reader = new BufferReader(layersBuffer);

    for (let i = 0; i < layerCount; i++) {
      const layer: LayerInfo = {
        layerId: reader.readInt32LE(),
        scale: reader.readFloatLE(),
        width: reader.readInt32LE(),
        height: reader.readInt32LE(),
        tileRow: reader.readInt32LE(),
        tileCol: reader.readInt32LE(),
        offset: reader.readInt32LE(),
        tileStart: reader.readInt32LE()
      };

      layers.push(layer);
    }

    return layers;
  }

  /**
   * 解析TMAP 7.x版本的瓦片信息
   */
  private async parseTilesV7(fileHandle: number, tileCount: number, imageCount: number, layerCount: number): Promise<TileInfo7[]> {
    const tiles: TileInfo7[] = [];
    const tileStructSize = CONSTANTS.TILE_INFO_7_SIZE;
    const tilesBuffer = Buffer.alloc(tileCount * tileStructSize);

    const imageStructSize = CONSTANTS.IMAGE_INFO_7_SIZE;
    const layerStructSize = CONSTANTS.LAYER_INFO_SIZE;
    const offset = CONSTANTS.HEADER_SIZE_V7 +
                   imageCount * imageStructSize +
                   layerCount * layerStructSize;

    fs.readSync(fileHandle, tilesBuffer, 0, tilesBuffer.length, offset);

    const reader = new BufferReader(tilesBuffer);

    for (let i = 0; i < tileCount; i++) {
      const tile: TileInfo7 = {
        layerId: reader.readInt32LE(),
        focusId: reader.readInt32LE(),
        x: reader.readInt32LE(),
        y: reader.readInt32LE(),
        width: reader.readInt32LE(),
        height: reader.readInt32LE(),
        offset: reader.readBigInt64LE(),
        length: reader.readInt32LE()
      };

      // 跳过填充字节
      reader.skip(4);

      tiles.push(tile);
    }

    return tiles;
  }

  /**
   * 获取TMAP基本信息
   */
  getBasicInfo(): TmapBasicInfo {
    if (!this.tmapInfo) {
      throw new Error('TMAP file not loaded');
    }

    if (this.tmapInfo.version === TmapVersion.VERSION_7) {
      return this.getBasicInfoV7(this.tmapInfo as TmapInfo7);
    } else {
      return this.getBasicInfoV5(this.tmapInfo as TmapInfo5);
    }
  }

  /**
   * 获取TMAP 7.x版本的基本信息
   */
  private getBasicInfoV7(tmapInfo: TmapInfo7): TmapBasicInfo {
    const header = tmapInfo.header;

    // 找到主图像（通常是第一个或最大的图像）
    let mainImage = tmapInfo.images[0];
    for (const image of tmapInfo.images) {
      if (image.type === ImageType.WHOLE ||
          (image.width * image.height > mainImage.width * mainImage.height)) {
        mainImage = image;
      }
    }

    return {
      version: header.mainVersion,
      imageSize: {
        width: mainImage.width,
        height: mainImage.height
      },
      pixelSize: header.pixelSize,
      scanScale: header.maxZoomRate,
      layerCount: header.layerNumber,
      focusCount: header.maxFocusNumber * 2 + 1,
      tileCount: header.tileNumber,
      compressAlgo: header.compressAlgo,
      quality: header.quality
    };
  }

  /**
   * 获取TMAP 5.x/6.x版本的基本信息
   */
  private getBasicInfoV5(tmapInfo: TmapInfo5): TmapBasicInfo {
    const header = tmapInfo.header;

    return {
      version: header.mainVersion,
      imageSize: {
        width: header.imageWidth,
        height: header.imageHeight
      },
      pixelSize: header.pixelSize,
      scanScale: header.maxLayerSize, // 5.x版本使用maxLayerSize作为扫描倍率
      layerCount: header.layerSize,
      focusCount: header.maxFocusNumber * 2 + 1,
      tileCount: tmapInfo.shrinkTiles.length,
      compressAlgo: CompressAlgo.QUICK, // 5.x版本默认使用快速压缩
      quality: 90 // 5.x版本默认质量
    };
  }

  /**
   * 获取完整的TMAP信息
   */
  getTmapInfo(): TmapInfo {
    if (!this.tmapInfo) {
      throw new Error('TMAP file not loaded');
    }
    return this.tmapInfo;
  }

  /**
   * 检查文件是否已打开
   */
  isFileOpen(): boolean {
    return this.isOpen;
  }
}
