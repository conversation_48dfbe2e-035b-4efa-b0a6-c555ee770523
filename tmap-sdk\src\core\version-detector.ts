/**
 * TMAP版本检测器
 * 负责检测TMAP文件版本并选择合适的解析器
 */

import * as fs from 'fs';
import { BufferReader } from '../utils/buffer-reader';
import { TmapVersion, CONSTANTS } from '../types';

export interface VersionInfo {
  version: TmapVersion;
  isSupported: boolean;
  headerSize: number;
  parserType: 'v7' | 'v5' | 'legacy';
}

export class VersionDetector {
  /**
   * 检测TMAP文件版本
   */
  static detectVersion(filePath: string): VersionInfo {
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    // 读取文件头的前16字节来检测版本
    const fileHandle = fs.openSync(filePath, 'r');
    const headerBuffer = Buffer.alloc(16);
    
    try {
      fs.readSync(fileHandle, headerBuffer, 0, 16, 0);
    } finally {
      fs.closeSync(fileHandle);
    }

    const reader = new BufferReader(headerBuffer);
    
    // 验证TMAP标识
    const header = reader.readString(4);
    if (header !== 'TMAP') {
      throw new Error(`Invalid TMAP header: ${header}`);
    }

    // 读取版本信息
    const mainVersionBytes = reader.readBuffer(2);
    const mainVersionStr = mainVersionBytes.toString('ascii');
    
    // 解析版本号
    if (mainVersionStr[0] !== '0') {
      throw new Error(`Invalid version format: ${mainVersionStr}`);
    }

    const versionNumber = parseInt(mainVersionStr[1]);
    
    // 验证版本范围
    if (versionNumber < CONSTANTS.MIN_SUPPORTED_VERSION || 
        versionNumber > CONSTANTS.MAX_SUPPORTED_VERSION) {
      return {
        version: versionNumber as TmapVersion,
        isSupported: false,
        headerSize: 0,
        parserType: 'legacy'
      };
    }

    // 确定解析器类型和头部大小
    let parserType: 'v7' | 'v5' | 'legacy';
    let headerSize: number;

    if (versionNumber >= 7) {
      parserType = 'v7';
      headerSize = CONSTANTS.HEADER_SIZE_V7;
    } else if (versionNumber >= 5) {
      parserType = 'v5';
      headerSize = CONSTANTS.HEADER_SIZE_V5;
    } else {
      parserType = 'legacy';
      headerSize = CONSTANTS.HEADER_SIZE_V5;
    }

    return {
      version: versionNumber as TmapVersion,
      isSupported: true,
      headerSize,
      parserType
    };
  }

  /**
   * 验证文件完整性
   */
  static validateFile(filePath: string, versionInfo: VersionInfo): boolean {
    if (!versionInfo.isSupported) {
      return false;
    }

    const fileHandle = fs.openSync(filePath, 'r');
    
    try {
      // 读取完整的文件头
      const headerBuffer = Buffer.alloc(versionInfo.headerSize);
      fs.readSync(fileHandle, headerBuffer, 0, versionInfo.headerSize, 0);
      
      const reader = new BufferReader(headerBuffer);
      
      // 跳过已验证的部分
      reader.skip(6); // header + version
      
      if (versionInfo.parserType === 'v7') {
        return this.validateV7Header(reader);
      } else if (versionInfo.parserType === 'v5') {
        return this.validateV5Header(reader, versionInfo.version);
      }
      
      return true;
    } catch (error) {
      console.error('File validation error:', error);
      return false;
    } finally {
      fs.closeSync(fileHandle);
    }
  }

  /**
   * 验证TMAP 7.x版本头部
   */
  private static validateV7Header(reader: BufferReader): boolean {
    try {
      reader.skip(2); // reserved
      const compressAlgo = reader.readUInt8();
      const quality = reader.readUInt8();
      const maxFocusNumber = reader.readUInt8();
      const maxZoomRate = reader.readUInt8();
      const bkgColor = reader.readUInt8();
      reader.skip(4); // pixelSize
      const imageNumber = reader.readInt32LE();
      const layerNumber = reader.readInt32LE();
      const tileNumber = reader.readInt32LE();

      // 基本验证
      return compressAlgo >= 0 && compressAlgo <= 2 &&
             quality > 0 && quality <= 100 &&
             maxFocusNumber >= 0 && maxFocusNumber <= 20 &&
             maxZoomRate > 0 && maxZoomRate <= 200 &&
             imageNumber > 0 && imageNumber <= CONSTANTS.MAX_IMAGE_NUM &&
             layerNumber > 0 && layerNumber <= CONSTANTS.MAX_LAYER_NUM &&
             tileNumber >= 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 验证TMAP 5.x/6.x版本头部
   */
  private static validateV5Header(reader: BufferReader, version: TmapVersion): boolean {
    try {
      const maxFocusNum = reader.readUInt8();
      const imageFormat = reader.readUInt8();
      const fileNum = reader.readUInt8();
      const layerSize = reader.readUInt8();
      const imgColor = reader.readUInt8();
      const checkSum = reader.readUInt8();
      const ratioStep = reader.readUInt8();
      const maxLaySize = reader.readUInt8();
      const slideType = reader.readUInt8();
      const bkColor = reader.readUInt8();
      reader.skip(4); // pixelSize
      const imgWidth = reader.readInt32LE();
      const imgHeight = reader.readInt32LE();
      const tileWidth = reader.readInt32LE();
      const tileHeight = reader.readInt32LE();
      const imgRow = reader.readInt32LE();
      const imgCol = reader.readInt32LE();

      // 基本验证
      return maxFocusNum >= 0 && maxFocusNum <= 20 &&
             fileNum > 0 && fileNum <= CONSTANTS.MAX_FILE_NUM &&
             layerSize > 0 && layerSize <= CONSTANTS.MAX_LAYER_SIZE &&
             CONSTANTS.SUPPORTED_COLOR_DEPTHS.includes(imgColor as any) &&
             imgWidth > 0 && imgHeight > 0 &&
             tileWidth > 0 && tileHeight > 0 &&
             imgRow > 0 && imgCol > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取版本描述
   */
  static getVersionDescription(version: TmapVersion): string {
    switch (version) {
      case TmapVersion.VERSION_2:
        return 'TMAP 2.x - 基础版本';
      case TmapVersion.VERSION_3:
        return 'TMAP 3.x - 增加融合层支持';
      case TmapVersion.VERSION_4:
        return 'TMAP 4.x - 改进的融合层';
      case TmapVersion.VERSION_5:
        return 'TMAP 5.x - 增加扩展信息';
      case TmapVersion.VERSION_6:
        return 'TMAP 6.x - 改进的扩展信息';
      case TmapVersion.VERSION_7:
        return 'TMAP 7.x - 最新版本，完整功能';
      default:
        return `TMAP ${version}.x - 未知版本`;
    }
  }

  /**
   * 检查版本兼容性
   */
  static isVersionCompatible(version: TmapVersion, requiredFeatures: string[] = []): boolean {
    const supportedFeatures: Record<TmapVersion, string[]> = {
      [TmapVersion.VERSION_2]: ['basic_tiles'],
      [TmapVersion.VERSION_3]: ['basic_tiles', 'fusion_layers'],
      [TmapVersion.VERSION_4]: ['basic_tiles', 'fusion_layers', 'improved_fusion'],
      [TmapVersion.VERSION_5]: ['basic_tiles', 'fusion_layers', 'improved_fusion', 'ext_info'],
      [TmapVersion.VERSION_6]: ['basic_tiles', 'fusion_layers', 'improved_fusion', 'ext_info', 'improved_ext'],
      [TmapVersion.VERSION_7]: ['basic_tiles', 'fusion_layers', 'improved_fusion', 'ext_info', 'improved_ext', 'full_features', 'compression', 'ocr', 'barcode']
    };

    const versionFeatures = supportedFeatures[version] || [];
    return requiredFeatures.every(feature => versionFeatures.includes(feature));
  }
}
