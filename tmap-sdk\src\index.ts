/**
 * TMAP SDK 主入口文件
 * 提供完整的TMAP文件解析和处理功能
 */

// 导出核心类
export { TmapReader } from './core/tmap-reader';
export { ImageExtractor } from './core/image-extractor';

// 导出工具类
export { BufferReader } from './utils/buffer-reader';
export { CompressionUtils } from './utils/compression';

// 导出类型定义
export * from './types';

// 导入需要的类型
import { TileRequest } from './types';

// 导出便捷的SDK类
export class TmapSDK {
  private tmapReader: import('./core/tmap-reader').TmapReader;
  private imageExtractor: import('./core/image-extractor').ImageExtractor;

  constructor(tmapFilePath: string) {
    this.tmapReader = new (require('./core/tmap-reader').TmapReader)(tmapFilePath);
    this.imageExtractor = new (require('./core/image-extractor').ImageExtractor)(this.tmapReader);
  }

  /**
   * 打开TMAP文件
   */
  async open(): Promise<void> {
    await this.tmapReader.open();
  }

  /**
   * 关闭TMAP文件
   */
  close(): void {
    this.tmapReader.close();
  }

  /**
   * 获取版本号
   */
  getVersion(): string {
    return this.tmapReader.getBasicInfo().version;
  }

  /**
   * 获取图像尺寸
   */
  getImageSize(): { width: number; height: number } {
    return this.tmapReader.getBasicInfo().imageSize;
  }

  /**
   * 获取像素尺寸
   */
  getPixelSize(): number {
    return this.tmapReader.getBasicInfo().pixelSize;
  }

  /**
   * 获取扫描倍率
   */
  getScanScale(): number {
    return this.tmapReader.getBasicInfo().scanScale;
  }

  /**
   * 获取扫描层数
   */
  getLayerCount(): number {
    return this.tmapReader.getBasicInfo().layerCount;
  }

  /**
   * 获取焦点数量
   */
  getFocusCount(): number {
    return this.tmapReader.getBasicInfo().focusCount;
  }

  /**
   * 获取瓦片总数
   */
  getTileCount(): number {
    return this.tmapReader.getBasicInfo().tileCount;
  }

  /**
   * 获取缩略图
   */
  async getThumbnail(format: 'jpeg' | 'png' = 'jpeg'): Promise<Buffer | null> {
    return this.imageExtractor.getThumbnail(format);
  }

  /**
   * 获取导航图
   */
  async getNavigateImage(format: 'jpeg' | 'png' = 'jpeg'): Promise<Buffer | null> {
    return this.imageExtractor.getNavigateImage(format);
  }

  /**
   * 获取宏观图
   */
  async getMacroImage(format: 'jpeg' | 'png' = 'jpeg'): Promise<Buffer | null> {
    return this.imageExtractor.getMacroImage(format);
  }

  /**
   * 获取标签图
   */
  async getLabelImage(format: 'jpeg' | 'png' = 'jpeg'): Promise<Buffer | null> {
    return this.imageExtractor.getLabelImage(format);
  }

  /**
   * 获取宏观标签图
   */
  async getMacroLabelImage(format: 'jpeg' | 'png' = 'jpeg'): Promise<Buffer | null> {
    return this.imageExtractor.getMacroLabelImage(format);
  }

  /**
   * 获取瓦片数据
   */
  async getTileData(
    layer: number,
    row: number,
    col: number,
    format: 'raw' | 'jpeg' | 'png' = 'jpeg',
    focusId: number = 0
  ): Promise<Buffer | null> {
    return this.imageExtractor.getTileData(layer, row, col, format);
  }

  /**
   * 批量获取瓦片数据
   */
  async getTileDataBatch(requests: TileRequest[]): Promise<(Buffer | null)[]> {
    const results: (Buffer | null)[] = [];

    for (const request of requests) {
      try {
        const tileData = await this.getTileData(
          request.layer,
          request.row,
          request.col,
          request.format || 'jpeg',
          request.focusId || 0
        );
        results.push(tileData);
      } catch (error) {
        console.warn(`Failed to get tile data for layer ${request.layer}, row ${request.row}, col ${request.col}:`, error);
        results.push(null);
      }
    }

    return results;
  }

  /**
   * 获取指定层的所有瓦片信息
   */
  getLayerTileInfo(layerId: number): { totalTiles: number; tileRows: number; tileCols: number } | null {
    const layerInfo = this.imageExtractor.getLayerInfo(layerId);
    if (!layerInfo) {
      return null;
    }

    return {
      totalTiles: layerInfo.tileRow * layerInfo.tileCol,
      tileRows: layerInfo.tileRow,
      tileCols: layerInfo.tileCol
    };
  }

  /**
   * 检查瓦片是否存在
   */
  async tileExists(layer: number, row: number, col: number): Promise<boolean> {
    try {
      const tileData = await this.getTileData(layer, row, col, 'raw');
      return tileData !== null && tileData.length > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取完整的TMAP信息
   */
  getTmapInfo() {
    return this.tmapReader.getTmapInfo();
  }

  /**
   * 获取基本信息
   */
  getBasicInfo() {
    return this.tmapReader.getBasicInfo();
  }

  /**
   * 获取层信息
   */
  getLayerInfo(layerId: number) {
    return this.imageExtractor.getLayerInfo(layerId);
  }

  /**
   * 获取所有层信息
   */
  getAllLayers() {
    return this.imageExtractor.getAllLayers();
  }
}

// 默认导出
export default TmapSDK;
