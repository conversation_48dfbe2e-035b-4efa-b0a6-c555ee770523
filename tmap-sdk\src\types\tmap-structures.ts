/**
 * TMAP文件格式数据结构定义
 * 基于C++版本的结构体定义转换为TypeScript接口
 */

// 图像类型枚举
export enum ImageType {
  THUMBNAIL = 0,    // 缩略图 <= 256
  NAVIGATE = 1,     // 导航图 <= 640
  MACRO = 2,        // 宏观图
  LABEL = 3,        // 标签图 <= 256
  MACRO_LABEL = 4,  // 宏观标签图 <= 256
  TILE = 5,         // 瓦片图 = 256
  WHOLE = 6,        // 整个图像
  ALL = 7
}

// 压缩算法枚举
export enum CompressAlgo {
  QUICK = 0,
  JPEG = 1,
  J2K = 2
}

// TMAP版本枚举
export enum TmapVersion {
  VERSION_2 = 2,
  VERSION_3 = 3,
  VERSION_4 = 4,
  VERSION_5 = 5,
  VERSION_6 = 6,
  VERSION_7 = 7
}

// 基础TMAP文件头结构（适用于所有版本）
export interface TmapHeaderBase {
  header: string;           // 必须是 "TMAP"
  mainVersion: string;      // 主版本号，如 "02", "05", "06", "07"
  maxFocusNumber: number;   // 最大焦点数
  imageFormat: number;      // 图像格式
  fileNumber: number;       // 文件数量
  layerSize: number;        // 层大小
  imageColor: number;       // 图像颜色深度 (8, 24, 32)
  checksum: number;         // 校验和
  ratioStep: number;        // 比例步长
  maxLayerSize: number;     // 最大层大小
  slideType: number;        // 切片类型 (0: HE)
  bkgColor: number;         // 背景颜色
  pixelSize: number;        // 100X图像的像素大小 (um/pixel)
}

// TMAP 7.x 版本的文件头结构
export interface TmapHeader7 extends TmapHeaderBase {
  reserved: Buffer;         // 保留字段
  compressAlgo: CompressAlgo; // 压缩算法
  quality: number;          // 压缩质量 (0, 100]
  maxZoomRate: number;      // 最大缩放倍率，通常是 10, 20, 40, 100
  imageNumber: number;      // 图像数量
  layerNumber: number;      // 层数
  tileNumber: number;       // 瓦片数量
  extOffset: bigint;        // 扩展信息偏移量，0表示无扩展信息
  ocr: string;              // OCR信息，64字节
  barcode: string;          // 条码信息，64字节
  client: string;           // 客户端信息，9字节
  reservedEx: Buffer;       // 扩展保留字段
}

// TMAP 5.x/6.x 版本的文件头结构
export interface TmapHeader5 extends TmapHeaderBase {
  imageWidth: number;       // 图像宽度
  imageHeight: number;      // 图像高度
  tileWidth: number;        // 瓦片宽度
  tileHeight: number;       // 瓦片高度
  imageRow: number;         // 图像行数
  imageCol: number;         // 图像列数
  totalImageNumber: number; // 总图像数量
  shrinkTileNumber: number; // 缩略瓦片数量
  airImageOffset: number;   // 空气图像偏移
  extInfoOffset: number;    // 扩展信息偏移
}

// 通用TMAP文件头（联合类型）
export type TmapHeader = TmapHeader7 | TmapHeader5;

// TMAP 7.x 图像信息结构
export interface ImageInfo7 {
  width: number;            // 图像宽度
  height: number;           // 图像高度
  depth: number;            // 图像深度（位数）
  type: ImageType;          // 图像类型
  offset: bigint;           // 文件中的偏移量
  length: number;           // 数据长度
}

// TMAP 5.x/6.x 图像信息结构
export interface ImageInfo5 {
  fileId: number;           // 文件ID
  layer: number;            // 层号，通常从-10到10
  reserved: Buffer;         // 保留字节
  topDx: number;            // 与顶部图像的Dx
  topDy: number;            // 与顶部图像的Dy
  leftDx: number;           // 与左侧图像的Dx
  leftDy: number;           // 与左侧图像的Dy
  imageCol: number;         // 图像列号
  imageRow: number;         // 图像行号
  x: number;                // 在大图像中的X坐标
  y: number;                // 在大图像中的Y坐标
  tiles: TileInfo5[];       // 瓦片信息数组
}

// 通用图像信息（联合类型）
export type ImageInfo = ImageInfo7 | ImageInfo5;

// 层信息结构
export interface LayerInfo {
  layerId: number;          // 层ID
  scale: number;            // 缩放比例
  width: number;            // 层宽度
  height: number;           // 层高度
  tileRow: number;          // 瓦片行数
  tileCol: number;          // 瓦片列数
  offset: number;           // 偏移量
  tileStart: number;        // 瓦片起始索引
}

// TMAP 7.x 瓦片信息结构
export interface TileInfo7 {
  layerId: number;          // 层ID
  focusId: number;          // 焦点ID (-10 ~ 10)
  x: number;                // X坐标
  y: number;                // Y坐标
  width: number;            // 瓦片宽度
  height: number;           // 瓦片高度
  offset: bigint;           // 文件中的偏移量
  length: number;           // 数据长度
}

// TMAP 5.x/6.x 瓦片信息结构
export interface TileInfo5 {
  fileId: number;           // 文件ID
  layer: number;            // 层号
  x: number;                // X坐标
  y: number;                // Y坐标
  fileOffset: number;       // 文件偏移量
  length: number;           // 数据长度
}

// 缩略瓦片信息结构（用于5.x/6.x版本）
export interface ShrinkTileInfo {
  fileId: number;           // 文件ID
  layerNo: number;          // 层号
  x: number;                // X坐标
  y: number;                // Y坐标
  fileOffset: number;       // 文件偏移量
  length: number;           // 数据长度
}

// 通用瓦片信息（联合类型）
export type TileInfo = TileInfo7 | TileInfo5;

// 扩展信息结构（用于5.x/6.x版本）
export interface TmapExtInfo {
  ocr: string;              // OCR信息
  barcode: string;          // 条码信息
  client: string;           // 客户端信息
  reserved: Buffer;         // 保留字段
}

// TMAP 7.x 完整信息结构
export interface TmapInfo7 {
  version: TmapVersion.VERSION_7;
  header: TmapHeader7;
  images: ImageInfo7[];
  layers: LayerInfo[];
  tiles: TileInfo7[];
}

// TMAP 5.x/6.x 完整信息结构
export interface TmapInfo5 {
  version: TmapVersion.VERSION_5 | TmapVersion.VERSION_6;
  header: TmapHeader5;
  extInfo?: TmapExtInfo;    // 扩展信息（5.x及以上版本）
  images: ImageInfo5[];
  shrinkTiles: ShrinkTileInfo[];
  extData?: Buffer;         // 扩展数据
}

// 通用TMAP信息结构（联合类型）
export type TmapInfo = TmapInfo7 | TmapInfo5;

// 瓦片请求参数
export interface TileRequest {
  layer: number;            // 层级
  row: number;              // 行
  col: number;              // 列
  format?: string;          // 输出格式 (jpeg, png)
}

// OpenSeadragon DZI信息
export interface DziInfo {
  format: string;           // 图像格式
  overlap: number;          // 重叠像素
  tileSize: number;         // 瓦片大小
  size: {
    width: number;          // 图像总宽度
    height: number;         // 图像总高度
  };
}

// TMAP文件基本信息
export interface TmapBasicInfo {
  version: string;          // 版本号
  imageSize: {              // 图像尺寸
    width: number;
    height: number;
  };
  pixelSize: number;        // 像素大小
  scanScale: number;        // 扫描倍率
  layerCount: number;       // 扫描层数
  focusCount: number;       // 焦点数量
  tileCount: number;        // 瓦片总数
  compressAlgo: CompressAlgo; // 压缩算法
  quality: number;          // 压缩质量
}

// 常量定义
export const CONSTANTS = {
  // 通用常量
  MAX_IMAGE_NUM: 8,
  MAX_LAYER_NUM: 16,
  MAX_TILE_NUM: 256,
  MAX_FILE_NUM: 16,
  MAX_LAYER_SIZE: 16,
  CLIENT_LEN: 8,
  TILE_SIZE: 256,
  PIXELSIZE_100X: 0.1,      // 100X下的标准像素大小

  // 版本相关的头部大小
  HEADER_SIZE_V7: 256,      // TMAP 7.x 头部大小
  HEADER_SIZE_V5: 128,      // TMAP 5.x/6.x 头部大小

  // 结构体大小
  IMAGE_INFO_7_SIZE: 32,    // TMAP 7.x 图像信息结构大小
  IMAGE_INFO_5_SIZE: 1024,  // TMAP 5.x 图像信息结构大小（包含瓦片数组）
  LAYER_INFO_SIZE: 32,      // 层信息结构大小
  TILE_INFO_7_SIZE: 32,     // TMAP 7.x 瓦片信息结构大小
  SHRINK_TILE_INFO_SIZE: 16, // 缩略瓦片信息结构大小
  EXT_INFO_SIZE: 256,       // 扩展信息结构大小

  // 支持的版本范围
  MIN_SUPPORTED_VERSION: 2,
  MAX_SUPPORTED_VERSION: 7,

  // 支持的颜色深度
  SUPPORTED_COLOR_DEPTHS: [8, 24, 32] as const,
} as const;
