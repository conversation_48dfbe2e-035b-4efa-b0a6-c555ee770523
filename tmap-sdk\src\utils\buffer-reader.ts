/**
 * 二进制数据读取工具类
 * 用于从Buffer中按照指定格式读取数据
 */

export class BufferReader {
  private buffer: Buffer;
  private position: number = 0;

  constructor(buffer: Buffer) {
    this.buffer = buffer;
  }

  /**
   * 获取当前位置
   */
  getPosition(): number {
    return this.position;
  }

  /**
   * 设置当前位置
   */
  setPosition(position: number): void {
    if (position < 0 || position > this.buffer.length) {
      throw new Error(`Invalid position: ${position}`);
    }
    this.position = position;
  }

  /**
   * 跳过指定字节数
   */
  skip(bytes: number): void {
    this.setPosition(this.position + bytes);
  }

  /**
   * 检查是否还有足够的字节可读
   */
  hasBytes(count: number): boolean {
    return this.position + count <= this.buffer.length;
  }

  /**
   * 读取8位无符号整数
   */
  readUInt8(): number {
    if (!this.hasBytes(1)) {
      throw new Error('Not enough bytes to read UInt8');
    }
    const value = this.buffer.readUInt8(this.position);
    this.position += 1;
    return value;
  }

  /**
   * 读取32位小端序有符号整数
   */
  readInt32LE(): number {
    if (!this.hasBytes(4)) {
      throw new Error(`Not enough bytes to read Int32LE at position ${this.position}. Buffer size: ${this.buffer.length}, needed: 4`);
    }
    const value = this.buffer.readInt32LE(this.position);
    this.position += 4;
    return value;
  }

  /**
   * 读取32位小端序无符号整数
   */
  readUInt32LE(): number {
    if (!this.hasBytes(4)) {
      throw new Error('Not enough bytes to read UInt32LE');
    }
    const value = this.buffer.readUInt32LE(this.position);
    this.position += 4;
    return value;
  }

  /**
   * 读取64位小端序有符号整数
   */
  readBigInt64LE(): bigint {
    if (!this.hasBytes(8)) {
      throw new Error('Not enough bytes to read BigInt64LE');
    }
    const value = this.buffer.readBigInt64LE(this.position);
    this.position += 8;
    return value;
  }

  /**
   * 读取32位小端序浮点数
   */
  readFloatLE(): number {
    if (!this.hasBytes(4)) {
      throw new Error('Not enough bytes to read FloatLE');
    }
    const value = this.buffer.readFloatLE(this.position);
    this.position += 4;
    return value;
  }

  /**
   * 读取指定长度的字符串（以null结尾）
   */
  readString(length: number, encoding: BufferEncoding = 'ascii'): string {
    if (!this.hasBytes(length)) {
      throw new Error(`Not enough bytes to read string of length ${length}`);
    }
    const stringBuffer = this.buffer.subarray(this.position, this.position + length);
    this.position += length;
    
    // 找到第一个null字符的位置
    const nullIndex = stringBuffer.indexOf(0);
    const actualBuffer = nullIndex >= 0 ? stringBuffer.subarray(0, nullIndex) : stringBuffer;
    
    return actualBuffer.toString(encoding);
  }

  /**
   * 读取指定长度的Buffer
   */
  readBuffer(length: number): Buffer {
    if (!this.hasBytes(length)) {
      throw new Error(`Not enough bytes to read buffer of length ${length}`);
    }
    const buffer = this.buffer.subarray(this.position, this.position + length);
    this.position += length;
    return buffer;
  }

  /**
   * 读取剩余的所有字节
   */
  readRemaining(): Buffer {
    const remaining = this.buffer.subarray(this.position);
    this.position = this.buffer.length;
    return remaining;
  }

  /**
   * 获取剩余字节数
   */
  getRemainingBytes(): number {
    return this.buffer.length - this.position;
  }

  /**
   * 重置到开始位置
   */
  reset(): void {
    this.position = 0;
  }
}
