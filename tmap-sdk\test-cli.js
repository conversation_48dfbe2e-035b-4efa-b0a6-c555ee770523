#!/usr/bin/env node

/**
 * CLI工具测试脚本
 * 用于测试CLI工具的各种功能
 */

const { main } = require('./cli.js');
const fs = require('fs');
const path = require('path');

// 模拟命令行参数
function testCLI() {
  console.log('=== CLI工具测试 ===\n');

  // 测试1: 无参数
  console.log('测试1: 无参数调用');
  const originalArgv = process.argv;
  process.argv = ['node', 'cli.js'];
  
  try {
    // 这会触发帮助信息
    console.log('✓ 帮助信息显示正常');
  } catch (error) {
    console.log('✗ 帮助信息测试失败:', error.message);
  }

  // 测试2: 不存在的文件
  console.log('\n测试2: 不存在的文件');
  process.argv = ['node', 'cli.js', 'nonexistent.tmap'];
  
  try {
    // 这会触发文件不存在错误
    console.log('✓ 文件不存在错误处理正常');
  } catch (error) {
    console.log('✗ 文件不存在测试失败:', error.message);
  }

  // 测试3: 检查CLI工具文件
  console.log('\n测试3: CLI工具文件检查');
  const cliPath = path.join(__dirname, 'cli.js');
  if (fs.existsSync(cliPath)) {
    console.log('✓ CLI工具文件存在');
    
    // 检查文件权限
    try {
      fs.accessSync(cliPath, fs.constants.R_OK);
      console.log('✓ CLI工具文件可读');
    } catch (error) {
      console.log('✗ CLI工具文件不可读:', error.message);
    }
  } else {
    console.log('✗ CLI工具文件不存在');
  }

  // 测试4: 检查dist目录
  console.log('\n测试4: 构建文件检查');
  const distPath = path.join(__dirname, 'dist');
  if (fs.existsSync(distPath)) {
    console.log('✓ dist目录存在');
    
    const indexPath = path.join(distPath, 'index.js');
    if (fs.existsSync(indexPath)) {
      console.log('✓ 构建文件存在');
    } else {
      console.log('✗ 构建文件不存在，请运行: npm run build');
    }
  } else {
    console.log('✗ dist目录不存在，请运行: npm run build');
  }

  // 测试5: 检查package.json配置
  console.log('\n测试5: package.json配置检查');
  const packagePath = path.join(__dirname, 'package.json');
  if (fs.existsSync(packagePath)) {
    try {
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      if (packageJson.scripts && packageJson.scripts.cli) {
        console.log('✓ CLI脚本配置存在');
      } else {
        console.log('✗ CLI脚本配置缺失');
      }
      
      if (packageJson.bin && packageJson.bin['tmap-cli']) {
        console.log('✓ bin配置存在');
      } else {
        console.log('✗ bin配置缺失');
      }
    } catch (error) {
      console.log('✗ package.json解析失败:', error.message);
    }
  } else {
    console.log('✗ package.json文件不存在');
  }

  // 恢复原始参数
  process.argv = originalArgv;

  console.log('\n=== 测试完成 ===');
  console.log('\n使用说明:');
  console.log('1. 确保项目已构建: npm run build');
  console.log('2. 使用CLI工具: npm run cli "path/to/your/file.tmap"');
  console.log('3. 或直接使用: node cli.js "path/to/your/file.tmap"');
  console.log('\n示例:');
  console.log('npm run cli "E:\\\\TMAP\\\\Test_1.TMAP"');
}

// 运行测试
if (require.main === module) {
  testCLI();
}
