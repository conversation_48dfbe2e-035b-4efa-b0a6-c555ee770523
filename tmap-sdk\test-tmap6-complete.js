#!/usr/bin/env node

/**
 * 完整的TMAP 6.x测试脚本
 * 测试图像尺寸、扫描倍率和图像提取功能
 */

const { TmapSDK } = require('./dist/index.js');
const fs = require('fs');
const path = require('path');

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(color + message + colors.reset);
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function testTmap6Complete(filePath) {
  colorLog(colors.cyan, '=== TMAP 6.x 完整功能测试 ===');
  console.log('');
  
  if (!fs.existsSync(filePath)) {
    colorLog(colors.red, `❌ 文件不存在: ${filePath}`);
    return;
  }

  const sdk = new TmapSDK(filePath);
  
  try {
    colorLog(colors.yellow, '⏳ 正在打开TMAP文件...');
    await sdk.open();
    colorLog(colors.green, '✓ 文件打开成功');
    console.log('');

    // 测试基本信息
    colorLog(colors.cyan, '=== 基本信息测试 ===');
    const basicInfo = sdk.getBasicInfo();
    
    console.log(`版本号: ${basicInfo.version}`);
    console.log(`图像尺寸: ${basicInfo.imageSize.width} × ${basicInfo.imageSize.height} 像素`);
    console.log(`像素大小: ${basicInfo.pixelSize} μm/pixel`);
    console.log(`扫描倍率: ${basicInfo.scanScale}X`);
    console.log(`扫描层数: ${basicInfo.layerCount}`);
    console.log(`焦点数量: ${basicInfo.focusCount}`);
    console.log(`瓦片总数: ${basicInfo.tileCount}`);
    console.log(`压缩算法: ${basicInfo.compressAlgo}`);
    console.log(`压缩质量: ${basicInfo.quality}`);
    console.log('');

    // 验证预期值
    colorLog(colors.cyan, '=== 预期值验证 ===');
    
    // 检查图像尺寸
    const expectedWidth = 109066;
    const expectedHeight = 58476;
    const expectedScanScale = 40;
    
    if (Math.abs(basicInfo.imageSize.width - expectedWidth) < 1000) {
      colorLog(colors.green, `✓ 图像宽度接近预期值: ${basicInfo.imageSize.width} ≈ ${expectedWidth}`);
    } else {
      colorLog(colors.red, `✗ 图像宽度不符合预期: ${basicInfo.imageSize.width} ≠ ${expectedWidth}`);
    }
    
    if (Math.abs(basicInfo.imageSize.height - expectedHeight) < 1000) {
      colorLog(colors.green, `✓ 图像高度接近预期值: ${basicInfo.imageSize.height} ≈ ${expectedHeight}`);
    } else {
      colorLog(colors.red, `✗ 图像高度不符合预期: ${basicInfo.imageSize.height} ≠ ${expectedHeight}`);
    }
    
    if (basicInfo.scanScale === expectedScanScale) {
      colorLog(colors.green, `✓ 扫描倍率符合预期: ${basicInfo.scanScale}X`);
    } else {
      colorLog(colors.yellow, `⚠ 扫描倍率与预期不同: ${basicInfo.scanScale}X ≠ ${expectedScanScale}X`);
    }
    console.log('');

    // 测试图像提取
    colorLog(colors.cyan, '=== 图像提取测试 ===');
    
    const imageTypes = [
      { name: 'thumbnail', displayName: '缩略图', method: 'getThumbnail' },
      { name: 'navigate', displayName: '导航图', method: 'getNavigateImage' },
      { name: 'macro', displayName: '宏观图', method: 'getMacroImage' },
      { name: 'label', displayName: '标签图', method: 'getLabelImage' },
      { name: 'macro-label', displayName: '宏观标签图', method: 'getMacroLabelImage' }
    ];

    let extractedCount = 0;
    const imageDir = path.join(__dirname, 'test-images');
    
    // 创建测试图像目录
    if (!fs.existsSync(imageDir)) {
      fs.mkdirSync(imageDir, { recursive: true });
    }

    for (const imageType of imageTypes) {
      try {
        colorLog(colors.yellow, `⏳ 正在提取${imageType.displayName}...`);
        const imageData = await sdk[imageType.method]('jpeg');
        
        if (imageData && imageData.length > 0) {
          const filename = `tmap6_${imageType.name}.jpg`;
          const filepath = path.join(imageDir, filename);
          fs.writeFileSync(filepath, imageData);
          colorLog(colors.green, `✓ ${imageType.displayName}已保存: ${filename} (${formatBytes(imageData.length)})`);
          extractedCount++;
        } else {
          colorLog(colors.yellow, `⚠ ${imageType.displayName}不存在或为空`);
        }
      } catch (error) {
        colorLog(colors.red, `✗ ${imageType.displayName}提取失败: ${error.message}`);
      }
    }
    
    console.log('');
    colorLog(colors.green, `✓ 图像提取完成: ${extractedCount}/5 个图像成功`);
    
    if (extractedCount > 0) {
      colorLog(colors.blue, `📁 图像保存目录: ${imageDir}`);
    }

    // 测试层级信息
    console.log('');
    colorLog(colors.cyan, '=== 层级信息测试 ===');
    try {
      const layers = sdk.getAllLayers();
      if (layers.length > 0) {
        console.log(`找到 ${layers.length} 个层级:`);
        layers.forEach((layer, index) => {
          console.log(`  层级 ${index}: ID=${layer.layerId}, 缩放=${layer.scale}, 尺寸=${layer.width}×${layer.height}`);
        });
      } else {
        colorLog(colors.yellow, '⚠ 当前版本不支持层级信息查询或无层级数据');
      }
    } catch (error) {
      colorLog(colors.yellow, `⚠ 层级信息获取失败: ${error.message}`);
    }

    // 测试瓦片信息
    console.log('');
    colorLog(colors.cyan, '=== 瓦片信息测试 ===');
    try {
      const layerInfo = sdk.getLayerTileInfo(0);
      if (layerInfo) {
        console.log(`层级0瓦片信息:`);
        console.log(`  总瓦片数: ${layerInfo.totalTiles}`);
        console.log(`  瓦片网格: ${layerInfo.tileCols} × ${layerInfo.tileRows}`);
      } else {
        colorLog(colors.yellow, '⚠ 当前版本不支持瓦片信息查询');
      }
    } catch (error) {
      colorLog(colors.yellow, `⚠ 瓦片信息获取失败: ${error.message}`);
    }

    console.log('');
    colorLog(colors.cyan, '=== 测试总结 ===');
    colorLog(colors.green, '✓ TMAP 6.x文件解析成功');
    colorLog(colors.green, '✓ 基本信息读取正常');
    
    if (extractedCount > 0) {
      colorLog(colors.green, `✓ 图像提取功能正常 (${extractedCount}/5)`);
    } else {
      colorLog(colors.yellow, '⚠ 图像提取功能需要进一步优化');
    }

  } catch (error) {
    colorLog(colors.red, `❌ 测试失败: ${error.message}`);
    console.error(error);
  } finally {
    sdk.close();
  }
}

async function main() {
  const args = process.argv.slice(2);
  if (args.length === 0) {
    colorLog(colors.red, '❌ 错误: 请提供TMAP文件路径');
    console.log('');
    console.log('使用方法:');
    console.log('  node test-tmap6-complete.js <tmap-file-path>');
    console.log('');
    console.log('示例:');
    console.log('  node test-tmap6-complete.js "E:\\TMAP\\Test_1.TMAP"');
    process.exit(1);
  }

  const tmapFilePath = args[0];
  await testTmap6Complete(tmapFilePath);
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    colorLog(colors.red, `❌ 未处理的错误: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

module.exports = { testTmap6Complete };
