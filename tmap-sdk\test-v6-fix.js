#!/usr/bin/env node

/**
 * 测试TMAP 6.x版本修复
 * 验证"Not enough bytes to read Int32LE"错误是否已修复
 */

const { TmapSDK } = require('./dist/index.js');
const fs = require('fs');

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(color + message + colors.reset);
}

async function testTmapV6Fix() {
  colorLog(colors.cyan, '=== TMAP 6.x 修复测试 ===');
  console.log('');

  // 测试用例：模拟TMAP 6.x文件的问题场景
  console.log('测试场景：');
  console.log('1. 文件大小不足以包含所有预期的图像数据');
  console.log('2. 解析器应该优雅地处理不完整的数据');
  console.log('3. 应该提供有用的警告信息而不是崩溃');
  console.log('');

  // 创建一个模拟的TMAP 6.x文件头部
  const headerBuffer = Buffer.alloc(128);
  
  // 写入TMAP标识
  headerBuffer.write('TMAP', 0, 'ascii');
  
  // 写入版本号 "06"
  headerBuffer.write('06', 4, 'ascii');
  
  // 写入一些基本的头部信息
  headerBuffer.writeUInt8(10, 6);    // maxFocusNumber
  headerBuffer.writeUInt8(1, 7);     // imageFormat
  headerBuffer.writeUInt8(1, 8);     // fileNumber
  headerBuffer.writeUInt8(4, 9);     // layerSize
  headerBuffer.writeUInt8(24, 10);   // imageColor (24位)
  headerBuffer.writeUInt8(0, 11);    // checksum
  
  headerBuffer.writeFloatLE(0.25, 16);    // pixelSize
  headerBuffer.writeInt32LE(1024, 20);    // imageWidth
  headerBuffer.writeInt32LE(768, 24);     // imageHeight
  headerBuffer.writeInt32LE(256, 28);     // tileWidth
  headerBuffer.writeInt32LE(256, 32);     // tileHeight
  headerBuffer.writeInt32LE(3, 36);       // imageRow
  headerBuffer.writeInt32LE(4, 40);       // imageCol
  headerBuffer.writeInt32LE(12, 44);      // totalImageNumber (3*4=12)
  headerBuffer.writeInt32LE(0, 48);       // shrinkTileNumber
  headerBuffer.writeInt32LE(0, 52);       // airImageOffset
  headerBuffer.writeInt32LE(0, 56);       // extInfoOffset

  // 创建测试文件
  const testFilePath = './test-tmap6.tmap';
  
  try {
    // 写入头部 + 扩展信息 + 部分图像数据（模拟不完整的文件）
    const extInfoSize = 256; // EXT_INFO_SIZE
    const imageInfoSize = 1024; // IMAGE_INFO_5_SIZE
    const totalImages = 12;
    
    // 创建一个不完整的文件：只包含头部 + 扩展信息 + 部分图像数据
    const partialImageCount = 5; // 只包含5个图像的数据，而不是12个
    const fileSize = 128 + extInfoSize + (partialImageCount * imageInfoSize);
    
    const fileBuffer = Buffer.alloc(fileSize);
    
    // 复制头部
    headerBuffer.copy(fileBuffer, 0);
    
    // 填充扩展信息（全零）
    // fileBuffer.fill(0, 128, 128 + extInfoSize);
    
    // 填充部分图像数据（模拟数据）
    for (let i = 0; i < partialImageCount; i++) {
      const offset = 128 + extInfoSize + (i * imageInfoSize);
      fileBuffer.writeUInt8(0, offset);      // fileId
      fileBuffer.writeInt8(0, offset + 1);   // layer
      // 其余字段保持为0
    }
    
    fs.writeFileSync(testFilePath, fileBuffer);
    
    colorLog(colors.blue, `✓ 创建测试文件: ${testFilePath}`);
    colorLog(colors.blue, `  文件大小: ${fileSize} 字节`);
    colorLog(colors.blue, `  预期图像数: ${totalImages}`);
    colorLog(colors.blue, `  实际图像数据: ${partialImageCount}`);
    console.log('');

    // 测试SDK是否能正确处理
    colorLog(colors.yellow, '⏳ 测试SDK处理...');
    
    const sdk = new TmapSDK(testFilePath);
    
    try {
      await sdk.open();
      colorLog(colors.green, '✓ SDK成功打开文件（没有崩溃）');
      
      const basicInfo = sdk.getBasicInfo();
      console.log('基本信息:');
      console.log(`  版本: ${basicInfo.version}`);
      console.log(`  图像尺寸: ${basicInfo.imageSize.width} x ${basicInfo.imageSize.height}`);
      console.log(`  像素大小: ${basicInfo.pixelSize}`);
      console.log(`  扫描层数: ${basicInfo.layerCount}`);
      
      colorLog(colors.green, '✓ 测试通过：SDK能够优雅地处理不完整的TMAP 6.x文件');
      
    } catch (error) {
      if (error.message.includes('Not enough bytes to read Int32LE')) {
        colorLog(colors.red, '✗ 测试失败：仍然出现"Not enough bytes to read Int32LE"错误');
        console.log('错误详情:', error.message);
      } else {
        colorLog(colors.yellow, `⚠ 其他错误（可能是预期的）: ${error.message}`);
      }
    } finally {
      sdk.close();
    }

  } catch (error) {
    colorLog(colors.red, `✗ 测试设置失败: ${error.message}`);
  } finally {
    // 清理测试文件
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
      colorLog(colors.blue, '✓ 清理测试文件');
    }
  }

  console.log('');
  colorLog(colors.cyan, '=== 测试完成 ===');
  console.log('');
  console.log('修复要点:');
  console.log('1. 添加了文件大小检查');
  console.log('2. 优雅处理不完整的数据');
  console.log('3. 提供详细的警告信息');
  console.log('4. 避免缓冲区溢出错误');
  console.log('');
  console.log('如果您的TMAP 6.x文件仍然有问题，请使用调试工具:');
  console.log('npm run debug "path/to/your/file.tmap"');
}

// 运行测试
if (require.main === module) {
  testTmapV6Fix().catch(error => {
    colorLog(colors.red, `❌ 测试失败: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

module.exports = { testTmapV6Fix };
